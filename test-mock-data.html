<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mock Data Test - YourStore</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <link rel="stylesheet" href="/wp-content/plugins/yourstore-commerce/assets/css/globals.css">
    
    <style>
        :root {
            --color-primary: #16a34a;
            --color-primary-50: #f0fdf4;
            --color-primary-100: #dcfce7;
            --color-secondary-900: #111827;
            --color-secondary-600: #4b5563;
            --color-accent: #f59e0b;
        }
    </style>
</head>
<body class="bg-gray-50">

    <div class="max-w-7xl mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold mb-8 text-center">Mock Data Test</h1>
        
        <div class="space-y-12">
            
            <!-- Featured Products Test -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-2xl font-semibold mb-4">Featured Products (Mock Data)</h2>
                <div class="yourstore-featured-products"
                     data-title="Test Featured Products"
                     data-subtitle="Using mock data"
                     data-layout="grid"
                     data-columns="4"
                     data-products-per-page="8"
                     data-show-filter="true"
                     data-show-quick-view="true"
                     data-show-wishlist="true">
                    <div class="text-center py-8">
                        <i class="fas fa-spinner fa-spin text-2xl text-gray-400"></i>
                        <p class="text-gray-500 mt-2">Loading featured products...</p>
                    </div>
                </div>
            </div>
            
            <!-- Category Showcase Test -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-2xl font-semibold mb-4">Category Showcase (Mock Data)</h2>
                <div class="yourstore-category-showcase"
                     data-title="Test Categories"
                     data-subtitle="Using mock data"
                     data-layout="grid"
                     data-columns="4"
                     data-show-product-count="true"
                     data-show-description="true"
                     data-display-style="cards"
                     data-exclude-empty="true"
                     data-max-categories="8">
                    <div class="text-center py-8">
                        <i class="fas fa-spinner fa-spin text-2xl text-gray-400"></i>
                        <p class="text-gray-500 mt-2">Loading categories...</p>
                    </div>
                </div>
            </div>
            
        </div>
        
        <!-- Status -->
        <div class="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div class="flex items-center">
                <i class="fas fa-info-circle text-yellow-600 mr-2"></i>
                <div>
                    <h3 class="text-yellow-800 font-semibold">GraphQL Temporarily Disabled</h3>
                    <p class="text-yellow-700 text-sm">Components are now using mock data due to GraphQL schema issues. This ensures the homepage displays products while we fix the GraphQL integration.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Load Scripts -->
    <script src="/wp-content/plugins/yourstore-commerce/assets/js/graphql-api.js"></script>
    <script src="/wp-content/plugins/yourstore-commerce/assets/js/api-utils.js"></script>
    <script src="/wp-content/plugins/yourstore-commerce/assets/js/components/featured-products.js"></script>
    <script src="/wp-content/plugins/yourstore-commerce/assets/js/components/category-showcase.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Mock data test page loaded');
            console.log('Available components:', {
                FeaturedProducts: !!window.FeaturedProducts,
                CategoryShowcase: !!window.CategoryShowcase,
                Vue: !!window.Vue
            });
            
            // Check if GraphQL is disabled
            setTimeout(() => {
                const products = document.querySelector('.yourstore-featured-products .product-card');
                const categories = document.querySelector('.yourstore-category-showcase .category-card');
                
                if (products || categories) {
                    console.log('✅ Components loaded successfully with mock data');
                } else {
                    console.log('⚠️ Components may still be loading...');
                }
            }, 3000);
        });
    </script>

</body>
</html>
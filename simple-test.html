<!DOCTYPE html>
<html>
<head>
    <title>Simple Hero Test</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
</head>
<body>
    <h1>Simple Hero Component Test</h1>
    
    <div id="status"></div>
    <div class="yourstore-hero-section" data-title="Test Title" data-subtitle="Test Subtitle"></div>
    
    <script>
        function updateStatus() {
            const status = document.getElementById('status');
            status.innerHTML = `
                <p>Vue: ${typeof Vue !== 'undefined' ? '✅ Loaded' : '❌ Missing'}</p>
                <p>HeroSection: ${typeof window.HeroSection !== 'undefined' ? '✅ Loaded' : '❌ Missing'}</p>
                <p>Location: ${window.location.href}</p>
            `;
        }
        
        setInterval(updateStatus, 1000);
        updateStatus();
        
        // Try to load hero component directly
        const script = document.createElement('script');
        script.src = '/wp-content/plugins/yourstore-commerce/assets/js/components/hero-section.js';
        script.onload = () => {
            console.log('Hero section script loaded!');
            updateStatus();
        };
        script.onerror = (e) => {
            console.error('Failed to load hero section:', e);
        };
        document.head.appendChild(script);
    </script>
</body>
</html>
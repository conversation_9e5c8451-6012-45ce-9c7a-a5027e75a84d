<!DOCTYPE html>
<html>
<head>
    <title>Simple Vue Test</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="p-8">
    <h1 class="text-2xl font-bold mb-4">Simple Vue Component Test</h1>
    
    <!-- Test 1: Basic Vue App -->
    <div class="mb-8 p-4 border rounded">
        <h2 class="text-lg font-semibold mb-2">Test 1: Basic Vue App</h2>
        <div id="app1">
            <p>{{ message }}</p>
            <button @click="count++" class="bg-blue-500 text-white px-4 py-2 rounded">
                Count: {{ count }}
            </button>
        </div>
    </div>

    <!-- Test 2: Component Format Similar to YourStore -->
    <div class="mb-8 p-4 border rounded">
        <h2 class="text-lg font-semibold mb-2">Test 2: YourStore-style Component</h2>
        <div class="test-hero-section" 
             data-title="Test Hero" 
             data-subtitle="Testing component mounting">
            <p class="text-red-500">Component should replace this text</p>
        </div>
    </div>

    <!-- Test 3: Data-props format -->
    <div class="mb-8 p-4 border rounded">
        <h2 class="text-lg font-semibold mb-2">Test 3: Data-props Format</h2>
        <div class="test-hero-props" 
             data-props='{"title":"Props Hero","subtitle":"Testing data-props format"}'>
            <p class="text-red-500">Component should replace this text</p>
        </div>
    </div>

    <script>
        console.log('Vue version:', Vue.version);
        
        // Test 1: Basic Vue app
        const { createApp, ref } = Vue;
        
        createApp({
            setup() {
                const message = ref('Vue.js is working! ✅')
                const count = ref(0)
                return { message, count }
            }
        }).mount('#app1');

        // Test 2: Simple Hero Component
        const SimpleHero = {
            props: {
                title: { type: String, default: 'Default Title' },
                subtitle: { type: String, default: 'Default Subtitle' }
            },
            template: `
                <div class="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-8 rounded-lg">
                    <h1 class="text-3xl font-bold mb-2">{{ title }}</h1>
                    <p class="text-lg opacity-90">{{ subtitle }}</p>
                    <button class="mt-4 bg-white text-blue-600 px-6 py-2 rounded font-semibold">
                        Learn More
                    </button>
                </div>
            `
        };

        // Mount components with data attributes (YourStore style)
        document.querySelectorAll('.test-hero-section').forEach(element => {
            const props = {
                title: element.dataset.title || 'Default Title',
                subtitle: element.dataset.subtitle || 'Default Subtitle'
            };
            const app = createApp(SimpleHero, props);
            app.mount(element);
        });

        // Mount components with data-props (Hybrid style)
        document.querySelectorAll('.test-hero-props').forEach(element => {
            const props = JSON.parse(element.dataset.props || '{}');
            const app = createApp(SimpleHero, props);
            app.mount(element);
        });

        console.log('All test components mounted');
    </script>
</body>
</html>
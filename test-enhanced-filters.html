<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Product Filters Test - YourStore Commerce</title>
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Vue.js 3 CDN -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    
    <!-- Custom Styles -->
    <link rel="stylesheet" href="wp-content/plugins/yourstore-commerce/assets/css/globals.css">
    
    <!-- Tailwind Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            DEFAULT: '#16a34a',
                            50: '#f0fdf4',
                            100: '#dcfce7',
                            200: '#bbf7d0',
                            300: '#86efac',
                            400: '#4ade80',
                            500: '#22c55e',
                            600: '#16a34a',
                            700: '#15803d',
                            800: '#166534',
                            900: '#14532d'
                        }
                    },
                    fontFamily: {
                        heading: ['Plus Jakarta Sans', 'system-ui', 'sans-serif'],
                        mono: ['JetBrains Mono', 'Monaco', 'Consolas', 'monospace']
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <div class="container mx-auto px-4 py-8">
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-gray-900 mb-4">Enhanced Product Filters Test</h1>
            <p class="text-lg text-gray-600">Testing advanced filtering with price sliders, color swatches, brands, and sizes</p>
        </div>
        
        <!-- Product Catalog Component -->
        <div id="product-catalog-app">
            <product-catalog 
                title="Test Product Catalog"
                subtitle="Enhanced filters with mock data"
                :show-filters="true"
                :show-sort="true"
                :show-pagination="true"
                :products-per-page="12"
                :show-search="true"
                :show-view-toggle="true"
                :grid-columns="4"
            ></product-catalog>
        </div>
    </div>

    <!-- Load Component Script -->
    <script src="wp-content/plugins/yourstore-commerce/assets/js/components/product-catalog.js"></script>
    
    <!-- Initialize Vue App -->
    <script>
        const { createApp } = Vue;
        
        createApp({
            components: {
                ProductCatalog: window.ProductCatalog
            }
        }).mount('#product-catalog-app');
    </script>
</body>
</html>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product Catalog Test - YourStore</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Vue.js -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    
    <!-- Custom Styles -->
    <link rel="stylesheet" href="/wp-content/plugins/yourstore-commerce/assets/css/globals.css">
    
    <style>
        /* Tailwind Config */
        :root {
            --color-primary: #16a34a;
            --color-primary-50: #f0fdf4;
            --color-primary-100: #dcfce7;
            --color-secondary-900: #111827;
            --color-secondary-600: #4b5563;
        }
    </style>
</head>
<body class="bg-gray-50">

    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-gray-900">YourStore</h1>
                    <span class="ml-3 px-3 py-1 bg-primary/10 text-primary text-sm rounded-full">Test Mode</span>
                </div>
                <nav class="flex items-center space-x-6">
                    <a href="/" class="text-gray-600 hover:text-primary">Home</a>
                    <a href="/shop" class="text-gray-600 hover:text-primary">Shop</a>
                    <a href="/categories" class="text-gray-600 hover:text-primary">Categories</a>
                </nav>
            </div>
        </div>
    </header>

    <!-- Page Header -->
    <div class="py-12 bg-gradient-to-r from-primary-600 to-primary-700 text-white">
        <div class="max-w-7xl mx-auto px-4 text-center">
            <h1 class="text-4xl font-bold mb-4">Product Catalog Test</h1>
            <p class="text-xl text-primary-100 mb-6">Testing the full-featured product catalog with all filters and options</p>
            
            <div class="flex justify-center items-center gap-6 text-sm text-primary-200">
                <span class="flex items-center">
                    <i class="fas fa-flask mr-2"></i>
                    Test Environment
                </span>
                <span>•</span>
                <span class="flex items-center">
                    <i class="fas fa-database mr-2"></i>
                    Live WooCommerce Data
                </span>
                <span>•</span>
                <span class="flex items-center">
                    <i class="fas fa-search mr-2"></i>
                    All Features Enabled
                </span>
            </div>
        </div>
    </div>

    <!-- Product Catalog Container -->
    <main class="py-0">
        <div class="yourstore-product-catalog" 
             data-title="" 
             data-subtitle="" 
             data-show-filters="true" 
             data-show-sort="true" 
             data-show-pagination="true" 
             data-show-search="true" 
             data-show-view-toggle="true" 
             data-products-per-page="24" 
             data-default-category="" 
             data-grid-columns="4">
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-8 mt-16">
        <div class="max-w-7xl mx-auto px-4 text-center">
            <p class="text-gray-400">
                <i class="fas fa-vial mr-2"></i>
                Product Catalog Test Page - YourStore Commerce Plugin
            </p>
        </div>
    </footer>

    <!-- Load JavaScript Components -->
    <script src="/wp-content/plugins/yourstore-commerce/assets/js/graphql-api.js"></script>
    <script src="/wp-content/plugins/yourstore-commerce/assets/js/api-utils.js"></script>
    <script src="/wp-content/plugins/yourstore-commerce/assets/js/components/product-catalog.js"></script>

    <!-- Initialize Vue App -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize product catalog components
            const catalogElements = document.querySelectorAll('.yourstore-product-catalog');
            
            catalogElements.forEach(element => {
                const { createApp } = Vue;
                
                const app = createApp({
                    components: {
                        ProductCatalog: window.ProductCatalog
                    },
                    setup() {
                        return {
                            title: element.dataset.title || '',
                            subtitle: element.dataset.subtitle || '',
                            showFilters: element.dataset.showFilters === 'true',
                            showSort: element.dataset.showSort === 'true',
                            showPagination: element.dataset.showPagination === 'true',
                            showSearch: element.dataset.showSearch === 'true',
                            showViewToggle: element.dataset.showViewToggle === 'true',
                            productsPerPage: parseInt(element.dataset.productsPerPage) || 12,
                            defaultCategory: element.dataset.defaultCategory || '',
                            gridColumns: parseInt(element.dataset.gridColumns) || 4
                        };
                    },
                    template: `
                        <ProductCatalog 
                            :title="title"
                            :subtitle="subtitle"
                            :show-filters="showFilters"
                            :show-sort="showSort"
                            :show-pagination="showPagination"
                            :show-search="showSearch"
                            :show-view-toggle="showViewToggle"
                            :products-per-page="productsPerPage"
                            :default-category="defaultCategory"
                            :grid-columns="gridColumns"
                        />
                    `
                });
                
                app.mount(element);
                console.log('Product catalog mounted successfully');
            });
        });
    </script>

</body>
</html>
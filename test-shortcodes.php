<?php
/**
 * Test Shortcodes - Verify shortcode rendering
 */

// WordPress bootstrap
require_once('wp-config.php');
require_once(ABSPATH . 'wp-settings.php');

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shortcode Test - YourStore</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <link rel="stylesheet" href="/wp-content/plugins/yourstore-commerce/assets/css/globals.css">
</head>
<body class="bg-gray-100 p-8">

    <div class="max-w-7xl mx-auto">
        <h1 class="text-3xl font-bold mb-8 text-center">YourStore Shortcode Test</h1>
        
        <div class="space-y-12">
            
            <!-- Featured Products Test -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-2xl font-semibold mb-4">Featured Products Shortcode</h2>
                <div class="border-2 border-dashed border-gray-300 p-4 mb-4">
                    <code>[yourstore_featured_products title="Test Products" columns="4" products_per_page="8"]</code>
                </div>
                <div class="shortcode-result">
                    <?php echo do_shortcode('[yourstore_featured_products title="Test Products" columns="4" products_per_page="8"]'); ?>
                </div>
            </div>
            
            <!-- Category Showcase Test -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-2xl font-semibold mb-4">Category Showcase Shortcode</h2>
                <div class="border-2 border-dashed border-gray-300 p-4 mb-4">
                    <code>[yourstore_category_showcase title="Test Categories" columns="4" max_categories="8"]</code>
                </div>
                <div class="shortcode-result">
                    <?php echo do_shortcode('[yourstore_category_showcase title="Test Categories" columns="4" max_categories="8"]'); ?>
                </div>
            </div>
            
            <!-- Product Catalog Test -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-2xl font-semibold mb-4">Product Catalog Shortcode</h2>
                <div class="border-2 border-dashed border-gray-300 p-4 mb-4">
                    <code>[yourstore_product_catalog title="Test Catalog" products_per_page="12"]</code>
                </div>
                <div class="shortcode-result">
                    <?php echo do_shortcode('[yourstore_product_catalog title="Test Catalog" products_per_page="12"]'); ?>
                </div>
            </div>
            
        </div>
    </div>

    <!-- Load Scripts -->
    <?php wp_footer(); ?>
    
    <!-- Manual script loading for testing -->
    <script src="/wp-content/plugins/yourstore-commerce/assets/js/graphql-api.js"></script>
    <script src="/wp-content/plugins/yourstore-commerce/assets/js/api-utils.js"></script>
    <script src="/wp-content/plugins/yourstore-commerce/assets/js/components/featured-products.js"></script>
    <script src="/wp-content/plugins/yourstore-commerce/assets/js/components/category-showcase.js"></script>
    <script src="/wp-content/plugins/yourstore-commerce/assets/js/components/product-catalog.js"></script>

    <script>
        // Initialize components after DOM loads
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize all Vue components
            setTimeout(() => {
                console.log('Available components:', {
                    FeaturedProducts: !!window.FeaturedProducts,
                    CategoryShowcase: !!window.CategoryShowcase,
                    ProductCatalog: !!window.ProductCatalog,
                    YourStoreGraphQL: !!window.YourStoreGraphQL
                });
                
                // Force load mock data if GraphQL isn't working
                if (!window.YourStoreGraphQL) {
                    console.log('GraphQL not available, components should use mock data');
                }
            }, 1000);
        });
    </script>

</body>
</html>
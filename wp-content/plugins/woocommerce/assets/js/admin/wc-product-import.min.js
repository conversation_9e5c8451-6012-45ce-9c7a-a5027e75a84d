!function(i,t){var r=function(i){this.$form=i,this.xhr=!1,this.mapping=wc_product_import_params.mapping,this.position=0,this.file=wc_product_import_params.file,this.update_existing=wc_product_import_params.update_existing,this.delimiter=wc_product_import_params.delimiter,this.security=wc_product_import_params.import_nonce,this.character_encoding=wc_product_import_params.character_encoding,this.imported=0,this.imported_variations=0,this.failed=0,this.updated=0,this.skipped=0,this.$form.find(".woocommerce-importer-progress").val(0),this.run_import=this.run_import.bind(this),this.run_import()};r.prototype.run_import=function(){var r=this;i.ajax({type:"POST",url:ajaxurl,data:{action:"woocommerce_do_ajax_product_import",position:r.position,mapping:r.mapping,file:r.file,update_existing:r.update_existing,delimiter:r.delimiter,security:r.security,character_encoding:r.character_encoding},dataType:"json",success:function(i){if(i.success)if(r.position=i.data.position,r.imported+=i.data.imported,r.imported_variations+=i.data.imported_variations,r.failed+=i.data.failed,r.updated+=i.data.updated,r.skipped+=i.data.skipped,r.$form.find(".woocommerce-importer-progress").val(i.data.percentage),"done"===i.data.position){var o=wc_product_import_params.file.split("/").pop();t.location=i.data.url+"&products-imported="+parseInt(r.imported,10)+"&products-imported-variations="+parseInt(r.imported_variations,10)+"&products-failed="+parseInt(r.failed,10)+"&products-updated="+parseInt(r.updated,10)+"&products-skipped="+parseInt(r.skipped,10)+"&file-name="+o}else r.run_import()}}).fail(function(i){t.console.log(i)})},i.fn.wc_product_importer=function(){return new r(this),this},i(".woocommerce-importer").wc_product_importer()}(jQuery,window);
(()=>{"use strict";var e,r,t,o={3531:(e,r,t)=>{const o=window.wp.blocks;var l=t(9822);const i=JSON.parse('{"name":"woocommerce/product-filter-price-slider","title":"Price Slider","description":"A slider helps shopper choose a price range.","category":"woocommerce","keywords":["WooCommerce"],"supports":{"html":false,"color":{"enableContrastChecker":false,"background":false,"text":false},"interactivity":true},"attributes":{"showInputFields":{"type":"boolean","default":true},"inlineInput":{"type":"boolean","default":false},"sliderHandle":{"type":"string","default":""},"customSliderHandle":{"type":"string","default":""},"sliderHandleBorder":{"type":"string","default":""},"customSliderHandleBorder":{"type":"string","default":""},"slider":{"type":"string","default":""},"customSlider":{"type":"string","default":""}},"ancestor":["woocommerce/product-filter-price"],"usesContext":["filterData"],"textdomain":"woocommerce","apiVersion":3,"$schema":"https://schemas.wp.org/trunk/block.json","viewScriptModule":"woocommerce/product-filter-price-slider","style":"file:../woocommerce/product-filter-price-slider-style.css"}');var n=t(4921),s=t(7723);const c=window.wp.components,a=window.wc.priceFormat,d=window.wp.blockEditor,p=["sliderHandle","sliderHandleBorder","slider"];var u=t(7356);function m(e){return`custom${e.charAt(0).toUpperCase()}${e.slice(1)}`}function f(e,r,t){const o={};return t.forEach((t=>{const l=r[t],i=m(t),n=r[i];("string"==typeof l&&l.length>0||"string"==typeof n&&n.length>0)&&(o[`--${e}-${(0,u.c)(t)}`]=function(e,r=""){return e?.length?`var(--wp--preset--color--${e})`:r||""}(l,n))})),o}function h(e,r){const t={};return r.forEach((r=>{if(!r.startsWith("custom")){const o=m(r),l=`has-${(0,u.c)(r)}-color`;t[l]=e[r]||e[o]}})),t}var w=t(790);const g=(0,d.withColors)(...p)((({clientId:e,context:r,attributes:t,setAttributes:o,sliderHandle:l,setSliderHandle:i,sliderHandleBorder:u,setSliderHandleBorder:m,slider:g,setSlider:b})=>{const{showInputFields:x,inlineInput:y,customSliderHandle:_,customSliderHandleBorder:v,customSlider:j}=t,{isLoading:S,price:k}=r.filterData,C=(0,d.useBlockProps)({className:(0,n.A)("wc-block-product-filter-price-slider",{"is-loading":S,...h(t,p)}),style:f("wc-product-filter-price",t,p)}),O=(0,d.__experimentalUseMultipleOriginColorsAndGradients)();if(S)return(0,w.jsx)(w.Fragment,{children:(0,s.__)("Loading…","woocommerce")});if(!k)return null;const{minPrice:B,maxPrice:H,minRange:N,maxRange:P}=k,A=(0,a.formatPrice)(B,(0,a.getCurrency)({minorUnit:0})),I=(0,a.formatPrice)(H,(0,a.getCurrency)({minorUnit:0})),M=x?(0,w.jsx)("input",{className:"min",type:"text",defaultValue:A}):(0,w.jsx)("span",{children:A}),F=x?(0,w.jsx)("input",{className:"max",type:"text",defaultValue:I}):(0,w.jsx)("span",{children:I});return(0,w.jsxs)(w.Fragment,{children:[(0,w.jsx)(d.InspectorControls,{children:(0,w.jsxs)(c.PanelBody,{title:(0,s.__)("Settings","woocommerce"),children:[(0,w.jsx)(c.ToggleControl,{label:(0,s.__)("Show input fields","woocommerce"),checked:x,onChange:()=>o({showInputFields:!x}),__nextHasNoMarginBottom:!0}),x&&(0,w.jsx)(c.ToggleControl,{label:(0,s.__)("Inline input fields","woocommerce"),checked:y,onChange:()=>o({inlineInput:!y}),__nextHasNoMarginBottom:!0})]})}),(0,w.jsx)(d.InspectorControls,{group:"color",children:O.hasColorsOrGradients&&(0,w.jsx)(d.__experimentalColorGradientSettingsDropdown,{__experimentalIsRenderedInSidebar:!0,settings:[{label:(0,s.__)("Slider Handle","woocommerce"),colorValue:l.color||_,isShownByDefault:!0,enableAlpha:!0,onColorChange:e=>{i(e),o({customSliderHandle:e})},resetAllFilter:()=>{i(""),o({customSliderHandle:""})}},{label:(0,s.__)("Slider Handle Border","woocommerce"),colorValue:u.color||v,isShownByDefault:!0,enableAlpha:!0,onColorChange:e=>{m(e),o({customSliderHandleBorder:e})},resetAllFilter:()=>{m(""),o({customSliderHandleBorder:""})}},{label:(0,s.__)("Slider","woocommerce"),colorValue:g.color||j,isShownByDefault:!0,enableAlpha:!0,onColorChange:e=>{b(e),o({customSlider:e})},resetAllFilter:()=>{b(""),o({customSlider:""})}}],panelId:e,...O})}),(0,w.jsx)("div",{...C,children:(0,w.jsx)(c.Disabled,{children:(0,w.jsxs)("div",{className:(0,n.A)("wc-block-product-filter-price-slider__content",{"wc-block-product-filter-price-slider__content--inline":y&&x}),children:[(0,w.jsx)("div",{className:"wc-block-product-filter-price-slider__left text",children:M}),(0,w.jsxs)("div",{className:"wc-block-product-filter-price-slider__range",children:[(0,w.jsx)("div",{className:"range-bar"}),(0,w.jsx)("input",{type:"range",className:"min",min:N,max:P,defaultValue:B}),(0,w.jsx)("input",{type:"range",className:"max",min:N,max:P,defaultValue:H})]}),(0,w.jsx)("div",{className:"wc-block-product-filter-price-slider__right text",children:F})]})})})]})}));(0,o.registerBlockType)(i,{edit:g,save:function({attributes:e}){const r=d.useBlockProps.save({className:(0,n.A)("wc-block-product-filter-price-slider",h(e,p)),style:f("wc-product-filter-price",e,p)});return(0,w.jsx)("div",{...r})},icon:l.A})},1609:e=>{e.exports=window.React},790:e=>{e.exports=window.ReactJSXRuntime},7723:e=>{e.exports=window.wp.i18n},5573:e=>{e.exports=window.wp.primitives}},l={};function i(e){var r=l[e];if(void 0!==r)return r.exports;var t=l[e]={exports:{}};return o[e].call(t.exports,t,t.exports,i),t.exports}i.m=o,e=[],i.O=(r,t,o,l)=>{if(!t){var n=1/0;for(d=0;d<e.length;d++){for(var[t,o,l]=e[d],s=!0,c=0;c<t.length;c++)(!1&l||n>=l)&&Object.keys(i.O).every((e=>i.O[e](t[c])))?t.splice(c--,1):(s=!1,l<n&&(n=l));if(s){e.splice(d--,1);var a=o();void 0!==a&&(r=a)}}return r}l=l||0;for(var d=e.length;d>0&&e[d-1][2]>l;d--)e[d]=e[d-1];e[d]=[t,o,l]},i.n=e=>{var r=e&&e.__esModule?()=>e.default:()=>e;return i.d(r,{a:r}),r},t=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,i.t=function(e,o){if(1&o&&(e=this(e)),8&o)return e;if("object"==typeof e&&e){if(4&o&&e.__esModule)return e;if(16&o&&"function"==typeof e.then)return e}var l=Object.create(null);i.r(l);var n={};r=r||[null,t({}),t([]),t(t)];for(var s=2&o&&e;"object"==typeof s&&!~r.indexOf(s);s=t(s))Object.getOwnPropertyNames(s).forEach((r=>n[r]=()=>e[r]));return n.default=()=>e,i.d(l,n),l},i.d=(e,r)=>{for(var t in r)i.o(r,t)&&!i.o(e,t)&&Object.defineProperty(e,t,{enumerable:!0,get:r[t]})},i.o=(e,r)=>Object.prototype.hasOwnProperty.call(e,r),i.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},i.j=9759,(()=>{var e={9759:0};i.O.j=r=>0===e[r];var r=(r,t)=>{var o,l,[n,s,c]=t,a=0;if(n.some((r=>0!==e[r]))){for(o in s)i.o(s,o)&&(i.m[o]=s[o]);if(c)var d=c(i)}for(r&&r(t);a<n.length;a++)l=n[a],i.o(e,l)&&e[l]&&e[l][0](),e[l]=0;return i.O(d)},t=globalThis.webpackChunkwebpackWcBlocksMainJsonp=globalThis.webpackChunkwebpackWcBlocksMainJsonp||[];t.forEach(r.bind(null,0)),t.push=r.bind(null,t.push.bind(t))})();var n=i.O(void 0,[94],(()=>i(3531)));n=i.O(n),((this.wc=this.wc||{}).blocks=this.wc.blocks||{})["product-filter-price-slider"]=n})();
<?php
/**
 * Base Component Abstract Class
 * 
 * Provides shared functionality for both Gutenberg Blocks and Shortcodes
 * 
 * @package YourStore\Commerce
 */

declare(strict_types=1);

namespace YourStore\Commerce\Abstracts;

// Prevent direct access
defined('ABSPATH') || exit;

/**
 * Base Component Abstract Class
 * 
 * This abstract class provides a unified approach for creating components
 * that work both as Gutenberg blocks and shortcodes with shared functionality.
 */
abstract class BaseComponent {
    
    /**
     * Component name/slug
     * 
     * @return string The component name (e.g., 'hero-section')
     */
    abstract protected function get_component_name(): string;
    
    /**
     * Default component attributes
     * 
     * @return array Default attributes with their default values
     */
    abstract protected function get_default_attributes(): array;
    
    /**
     * Render component content (shared between block and shortcode)
     * 
     * @param array $attributes Processed and sanitized attributes
     * @return string Rendered HTML content
     */
    abstract protected function render_content(array $attributes): string;
    
    /**
     * Initialize both block and shortcode registration
     * 
     * @return void
     */
    public function init(): void {
        add_action('init', [$this, 'register_block']);
        add_action('init', [$this, 'register_shortcode']);
    }
    
    /**
     * Register Gutenberg block
     * 
     * @return void
     */
    public function register_block(): void {
        $block_name = 'yourstore-commerce/' . $this->get_component_name();
        
        register_block_type($block_name, [
            'attributes' => $this->get_block_attributes(),
            'render_callback' => [$this, 'render_block'],
            'supports' => $this->get_block_supports(),
        ]);
        
        // Log registration for debugging
        error_log("YourStore: Registered block '{$block_name}'");
    }
    
    /**
     * Register shortcode
     * 
     * @return void
     */
    public function register_shortcode(): void {
        $shortcode_name = str_replace('-', '_', $this->get_component_name());
        add_shortcode($shortcode_name, [$this, 'render_shortcode']);
        
        // Log registration for debugging
        error_log("YourStore: Registered shortcode '[{$shortcode_name}]'");
    }
    
    /**
     * Block render callback
     * 
     * @param array $attributes Block attributes from Gutenberg
     * @return string Rendered HTML
     */
    public function render_block(array $attributes): string {
        $processed_attributes = $this->process_block_attributes($attributes);
        return $this->render_content($processed_attributes);
    }
    
    /**
     * Shortcode render callback
     * 
     * @param array|string $atts Shortcode attributes
     * @return string Rendered HTML
     */
    public function render_shortcode($atts): string {
        $attributes = shortcode_atts($this->get_default_attributes(), (array) $atts);
        $processed_attributes = $this->process_shortcode_attributes($attributes);
        return $this->render_content($processed_attributes);
    }
    
    /**
     * Process and sanitize block attributes
     * 
     * @param array $attributes Raw block attributes
     * @return array Processed attributes
     */
    protected function process_block_attributes(array $attributes): array {
        return $this->sanitize_attributes(
            array_merge($this->get_default_attributes(), $attributes)
        );
    }
    
    /**
     * Process and sanitize shortcode attributes
     * 
     * @param array $attributes Raw shortcode attributes
     * @return array Processed attributes
     */
    protected function process_shortcode_attributes(array $attributes): array {
        return $this->sanitize_attributes($attributes);
    }
    
    /**
     * Sanitize component attributes
     * 
     * @param array $attributes Raw attributes
     * @return array Sanitized attributes
     */
    protected function sanitize_attributes(array $attributes): array {
        $sanitized = [];
        $defaults = $this->get_default_attributes();
        
        foreach ($defaults as $key => $default_value) {
            if (isset($attributes[$key])) {
                $sanitized[$key] = $this->sanitize_attribute($key, $attributes[$key]);
            } else {
                $sanitized[$key] = $default_value;
            }
        }
        
        return $sanitized;
    }
    
    /**
     * Sanitize individual attribute
     * 
     * Override in child classes for specific sanitization rules.
     * 
     * @param string $key Attribute name
     * @param mixed $value Attribute value
     * @return mixed Sanitized value
     */
    protected function sanitize_attribute(string $key, $value) {
        // Default sanitization based on value type
        if (is_string($value)) {
            return sanitize_text_field($value);
        }
        if (is_int($value)) {
            return intval($value);
        }
        if (is_float($value)) {
            return floatval($value);
        }
        if (is_bool($value)) {
            return boolval($value);
        }
        if (is_array($value)) {
            return array_map([$this, 'sanitize_array_value'], $value);
        }
        
        return $value;
    }
    
    /**
     * Get block attributes configuration for registration
     * 
     * @return array Block attributes schema
     */
    protected function get_block_attributes(): array {
        $attributes = [];
        
        foreach ($this->get_default_attributes() as $key => $default_value) {
            $attributes[$key] = [
                'type' => $this->get_attribute_type($default_value),
                'default' => $default_value,
            ];
        }
        
        return $attributes;
    }
    
    /**
     * Get attribute type for block registration
     * 
     * @param mixed $value Default value to determine type
     * @return string Attribute type
     */
    protected function get_attribute_type($value): string {
        if (is_string($value)) return 'string';
        if (is_int($value)) return 'number';
        if (is_float($value)) return 'number';
        if (is_bool($value)) return 'boolean';
        if (is_array($value)) return 'array';
        
        return 'string';
    }
    
    /**
     * Get block supports configuration
     * 
     * Override in child classes for component-specific supports.
     * 
     * @return array Block supports configuration
     */
    protected function get_block_supports(): array {
        return [
            'align' => ['full', 'wide'],
            'spacing' => [
                'margin' => true,
                'padding' => true,
            ],
        ];
    }
    
    /**
     * Generate Vue component wrapper HTML
     * 
     * Creates the HTML wrapper that Vue.js will mount to.
     * 
     * @param array $attributes Component attributes
     * @param string $additional_classes Additional CSS classes
     * @return string HTML wrapper
     */
    protected function generate_vue_wrapper(array $attributes, string $additional_classes = ''): string {
        $component_name = $this->get_component_name();
        $props_data = wp_json_encode($attributes);
        
        $classes = sprintf('yourstore-%s %s', $component_name, $additional_classes);
        
        return sprintf(
            '<div class="%s" data-props="%s"></div>',
            esc_attr(trim($classes)),
            esc_attr($props_data)
        );
    }
    
    /**
     * Sanitize array values recursively
     * 
     * @param mixed $value Array value to sanitize
     * @return mixed Sanitized value
     */
    private function sanitize_array_value($value) {
        if (is_string($value)) {
            return sanitize_text_field($value);
        }
        if (is_array($value)) {
            return array_map([$this, 'sanitize_array_value'], $value);
        }
        return $value;
    }
    
    /**
     * Get cache key for attributes
     * 
     * @param array $attributes Attributes to generate cache key for
     * @return string Cache key
     */
    protected function get_cache_key(array $attributes): string {
        return 'yourstore_' . $this->get_component_name() . '_' . md5(serialize($attributes));
    }
    
    /**
     * Log debug information
     * 
     * @param string $message Debug message
     * @param mixed $data Additional data to log
     * @return void
     */
    protected function debug_log(string $message, $data = null): void {
        if (defined('WP_DEBUG') && WP_DEBUG) {
            $component_name = $this->get_component_name();
            $log_message = "YourStore [{$component_name}]: {$message}";
            
            if ($data !== null) {
                $log_message .= ' | Data: ' . print_r($data, true);
            }
            
            error_log($log_message);
        }
    }
}
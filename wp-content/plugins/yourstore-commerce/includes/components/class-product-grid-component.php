<?php
/**
 * Product Grid Hybrid Component
 * 
 * Provides Product Grid functionality for both Gutenberg blocks and shortcodes
 * 
 * @package YourStore\Commerce
 */

declare(strict_types=1);

namespace YourStore\Commerce\Components;

use YourStore\Commerce\Abstracts\BaseComponent;

// Prevent direct access
defined('ABSPATH') || exit;

/**
 * Product Grid Component
 * 
 * Creates responsive product grids with filtering, sorting, and pagination
 * that work both as blocks and shortcodes.
 */
final class ProductGridComponent extends BaseComponent {
    
    /**
     * Get component name
     * 
     * @return string Component name
     */
    protected function get_component_name(): string {
        return 'product-grid';
    }
    
    /**
     * Get default attributes
     * 
     * @return array Default component attributes
     */
    protected function get_default_attributes(): array {
        return [
            'limit' => 12,
            'columns' => 4,
            'categories' => [],
            'brands' => [],
            'tags' => [],
            'orderby' => 'menu_order',
            'order' => 'ASC',
            'show_filters' => true,
            'show_sorting' => true,
            'show_pagination' => true,
            'show_quick_view' => true,
            'show_wishlist' => true,
            'show_compare' => false,
            'featured_only' => false,
            'on_sale_only' => false,
            'min_price' => 0,
            'max_price' => 0,
            'layout' => 'grid',
            'card_style' => 'default',
            'enable_lazy_loading' => true,
            'hide_out_of_stock' => false,
        ];
    }
    
    /**
     * Render component content
     * 
     * @param array $attributes Processed attributes
     * @return string Rendered HTML
     */
    protected function render_content(array $attributes): string {
        // Add component-specific data
        $attributes['component_id'] = 'product-grid-' . wp_generate_uuid4();
        
        // Generate Vue wrapper with specific classes
        return $this->generate_vue_wrapper($attributes, 'product-grid-wrapper');
    }
    
    /**
     * Sanitize component attributes
     * 
     * @param string $key Attribute name
     * @param mixed $value Attribute value
     * @return mixed Sanitized value
     */
    protected function sanitize_attribute(string $key, $value) {
        switch ($key) {
            case 'limit':
                return max(1, min(100, absint($value)));
                
            case 'columns':
                return max(1, min(6, absint($value)));
                
            case 'categories':
            case 'brands':
            case 'tags':
                if (is_string($value)) {
                    // Convert comma-separated string to array
                    return array_filter(array_map('trim', explode(',', $value)));
                }
                return is_array($value) ? array_map('sanitize_text_field', $value) : [];
                
            case 'orderby':
                $allowed_orderby = [
                    'menu_order', 'popularity', 'rating', 'date', 'price', 
                    'price-desc', 'title', 'rand'
                ];
                return in_array($value, $allowed_orderby) ? $value : 'menu_order';
                
            case 'order':
                return in_array(strtoupper($value), ['ASC', 'DESC']) ? strtoupper($value) : 'ASC';
                
            case 'show_filters':
            case 'show_sorting':
            case 'show_pagination':
            case 'show_quick_view':
            case 'show_wishlist':
            case 'show_compare':
            case 'featured_only':
            case 'on_sale_only':
            case 'enable_lazy_loading':
            case 'hide_out_of_stock':
                return boolval($value);
                
            case 'min_price':
            case 'max_price':
                return max(0, floatval($value));
                
            case 'layout':
                $allowed_layouts = ['grid', 'list', 'masonry'];
                return in_array($value, $allowed_layouts) ? $value : 'grid';
                
            case 'card_style':
                $allowed_styles = ['default', 'minimal', 'modern', 'classic', 'overlay'];
                return in_array($value, $allowed_styles) ? $value : 'default';
                
            default:
                return parent::sanitize_attribute($key, $value);
        }
    }
    
    /**
     * Get block supports configuration
     * 
     * @return array Block supports
     */
    protected function get_block_supports(): array {
        return array_merge(parent::get_block_supports(), [
            'align' => ['wide', 'full'],
            'color' => [
                'background' => true,
                'text' => true,
            ],
            'spacing' => [
                'margin' => true,
                'padding' => true,
            ],
        ]);
    }
    
    /**
     * Get shortcode usage documentation
     * 
     * @return array Shortcode documentation
     */
    public static function get_shortcode_docs(): array {
        return [
            'name' => 'product_grid',
            'description' => 'Display a responsive grid of WooCommerce products with filtering and sorting',
            'examples' => [
                'basic' => '[product_grid limit="8" columns="4"]',
                'filtered' => '[product_grid categories="electronics,clothing" featured_only="true"]',
                'custom_layout' => '[product_grid layout="list" card_style="modern" show_filters="false"]',
                'price_range' => '[product_grid min_price="10" max_price="100" orderby="price"]',
                'brands' => '[product_grid brands="apple,samsung" show_compare="true"]',
                'sale_items' => '[product_grid on_sale_only="true" orderby="price-desc" limit="6"]',
            ],
            'attributes' => [
                'limit' => [
                    'type' => 'number',
                    'default' => 12,
                    'description' => 'Maximum number of products to display (1-100)',
                ],
                'columns' => [
                    'type' => 'number',
                    'default' => 4,
                    'description' => 'Number of columns in grid layout (1-6)',
                ],
                'categories' => [
                    'type' => 'array|string',
                    'default' => [],
                    'description' => 'Product categories (array or comma-separated string)',
                ],
                'brands' => [
                    'type' => 'array|string',
                    'default' => [],
                    'description' => 'Product brands (array or comma-separated string)',
                ],
                'tags' => [
                    'type' => 'array|string',
                    'default' => [],
                    'description' => 'Product tags (array or comma-separated string)',
                ],
                'orderby' => [
                    'type' => 'string',
                    'default' => 'menu_order',
                    'description' => 'Sort products by: menu_order, popularity, rating, date, price, price-desc, title, rand',
                ],
                'order' => [
                    'type' => 'string',
                    'default' => 'ASC',
                    'description' => 'Sort order: ASC or DESC',
                ],
                'show_filters' => [
                    'type' => 'boolean',
                    'default' => true,
                    'description' => 'Show product filtering options',
                ],
                'show_sorting' => [
                    'type' => 'boolean',
                    'default' => true,
                    'description' => 'Show sorting dropdown',
                ],
                'show_pagination' => [
                    'type' => 'boolean',
                    'default' => true,
                    'description' => 'Show pagination controls',
                ],
                'show_quick_view' => [
                    'type' => 'boolean',
                    'default' => true,
                    'description' => 'Enable quick view modal for products',
                ],
                'show_wishlist' => [
                    'type' => 'boolean',
                    'default' => true,
                    'description' => 'Show wishlist buttons on product cards',
                ],
                'show_compare' => [
                    'type' => 'boolean',
                    'default' => false,
                    'description' => 'Show product comparison features',
                ],
                'featured_only' => [
                    'type' => 'boolean',
                    'default' => false,
                    'description' => 'Show only featured products',
                ],
                'on_sale_only' => [
                    'type' => 'boolean',
                    'default' => false,
                    'description' => 'Show only products on sale',
                ],
                'min_price' => [
                    'type' => 'number',
                    'default' => 0,
                    'description' => 'Minimum product price filter',
                ],
                'max_price' => [
                    'type' => 'number',
                    'default' => 0,
                    'description' => 'Maximum product price filter (0 = no limit)',
                ],
                'layout' => [
                    'type' => 'string',
                    'default' => 'grid',
                    'description' => 'Display layout: grid, list, masonry',
                ],
                'card_style' => [
                    'type' => 'string',
                    'default' => 'default',
                    'description' => 'Product card style: default, minimal, modern, classic, overlay',
                ],
                'enable_lazy_loading' => [
                    'type' => 'boolean',
                    'default' => true,
                    'description' => 'Enable lazy loading for product images',
                ],
                'hide_out_of_stock' => [
                    'type' => 'boolean',
                    'default' => false,
                    'description' => 'Hide out of stock products',
                ],
            ],
        ];
    }
}
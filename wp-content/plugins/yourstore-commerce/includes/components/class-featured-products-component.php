<?php
/**
 * Featured Products Hybrid Component
 * 
 * Provides Featured Products functionality for both Gutenberg blocks and shortcodes
 * 
 * @package YourStore\Commerce
 */

declare(strict_types=1);

namespace YourStore\Commerce\Components;

use YourStore\Commerce\Abstracts\BaseComponent;

// Prevent direct access
defined('ABSPATH') || exit;

/**
 * Featured Products Component
 * 
 * Creates curated product showcases with category filtering, responsive layouts,
 * and customizable display options that work both as blocks and shortcodes.
 */
final class FeaturedProductsComponent extends BaseComponent {
    
    /**
     * Get component name
     * 
     * @return string Component name
     */
    protected function get_component_name(): string {
        return 'featured-products';
    }
    
    /**
     * Get default attributes
     * 
     * @return array Default component attributes
     */
    protected function get_default_attributes(): array {
        return [
            'title' => 'Featured Products',
            'subtitle' => 'Discover our hand-picked selection',
            'limit' => 8,
            'columns' => 4,
            'show_view_all' => true,
            'view_all_url' => '/shop',
            'view_all_text' => 'View All Products',
            'categories' => [],
            'featured_only' => true,
            'on_sale_only' => false,
            'orderby' => 'menu_order',
            'order' => 'ASC',
            'show_price' => true,
            'show_rating' => true,
            'show_add_to_cart' => true,
            'show_quick_view' => true,
            'show_wishlist' => true,
            'card_style' => 'default',
            'enable_carousel' => false,
            'carousel_autoplay' => true,
            'carousel_speed' => 5000,
            'enable_lazy_loading' => true,
            'hide_out_of_stock' => false,
        ];
    }
    
    /**
     * Render component content
     * 
     * @param array $attributes Processed attributes
     * @return string Rendered HTML
     */
    protected function render_content(array $attributes): string {
        // Add component-specific data
        $attributes['component_id'] = 'featured-products-' . wp_generate_uuid4();
        
        // Generate Vue wrapper with specific classes
        return $this->generate_vue_wrapper($attributes, 'featured-products-wrapper');
    }
    
    /**
     * Sanitize component attributes
     * 
     * @param string $key Attribute name
     * @param mixed $value Attribute value
     * @return mixed Sanitized value
     */
    protected function sanitize_attribute(string $key, $value) {
        switch ($key) {
            case 'title':
            case 'subtitle':
            case 'view_all_text':
                return wp_kses_post((string) $value);
                
            case 'limit':
                return max(1, min(50, absint($value)));
                
            case 'columns':
                return max(1, min(6, absint($value)));
                
            case 'view_all_url':
                return esc_url_raw((string) $value);
                
            case 'categories':
                if (is_string($value)) {
                    // Handle comma-separated string
                    return array_filter(array_map('trim', explode(',', $value)));
                }
                return is_array($value) ? array_filter($value) : [];
                
            case 'orderby':
                $allowed_orderby = [
                    'menu_order', 'title', 'date', 'price', 'popularity', 'rating'
                ];
                return in_array($value, $allowed_orderby, true) ? $value : 'menu_order';
                
            case 'order':
                return in_array(strtoupper($value), ['ASC', 'DESC'], true) ? strtoupper($value) : 'ASC';
                
            case 'card_style':
                $allowed_styles = ['default', 'modern', 'minimal', 'premium'];
                return in_array($value, $allowed_styles, true) ? $value : 'default';
                
            case 'carousel_speed':
                return max(1000, min(30000, absint($value)));
                
            case 'show_view_all':
            case 'featured_only':
            case 'on_sale_only':
            case 'show_price':
            case 'show_rating':
            case 'show_add_to_cart':
            case 'show_quick_view':
            case 'show_wishlist':
            case 'enable_carousel':
            case 'carousel_autoplay':
            case 'enable_lazy_loading':
            case 'hide_out_of_stock':
                return filter_var($value, FILTER_VALIDATE_BOOLEAN);
                
            default:
                return sanitize_text_field((string) $value);
        }
    }
    
    /**
     * Get component documentation
     * 
     * @return array Component documentation
     */
    public function get_documentation(): array {
        return [
            'name' => 'Featured Products',
            'description' => 'Display a curated selection of featured products with customizable layouts and filtering options.',
            'category' => 'E-commerce',
            'keywords' => ['products', 'featured', 'showcase', 'e-commerce', 'woocommerce'],
            'supports' => [
                'gutenberg_block' => true,
                'shortcode' => true,
                'php_function' => true,
            ],
            'attributes' => [
                [
                    'name' => 'title',
                    'type' => 'string',
                    'default' => 'Featured Products',
                    'description' => 'Main heading for the featured products section'
                ],
                [
                    'name' => 'subtitle',
                    'type' => 'string', 
                    'default' => 'Discover our hand-picked selection',
                    'description' => 'Subtitle or description text'
                ],
                [
                    'name' => 'limit',
                    'type' => 'number',
                    'default' => 8,
                    'description' => 'Maximum number of products to display (1-50)',
                    'range' => '1-50'
                ],
                [
                    'name' => 'columns',
                    'type' => 'number',
                    'default' => 4,
                    'description' => 'Number of columns in grid layout (1-6)',
                    'range' => '1-6'
                ],
                [
                    'name' => 'show_view_all',
                    'type' => 'boolean',
                    'default' => true,
                    'description' => 'Show "View All" button'
                ],
                [
                    'name' => 'view_all_url',
                    'type' => 'string',
                    'default' => '/shop',
                    'description' => 'URL for "View All" button'
                ],
                [
                    'name' => 'view_all_text',
                    'type' => 'string',
                    'default' => 'View All Products',
                    'description' => 'Text for "View All" button'
                ],
                [
                    'name' => 'categories',
                    'type' => 'array|string',
                    'default' => '[]',
                    'description' => 'Filter by product categories (comma-separated or array)'
                ],
                [
                    'name' => 'featured_only',
                    'type' => 'boolean',
                    'default' => true,
                    'description' => 'Show only featured products'
                ],
                [
                    'name' => 'on_sale_only',
                    'type' => 'boolean',
                    'default' => false,
                    'description' => 'Show only products on sale'
                ],
                [
                    'name' => 'orderby',
                    'type' => 'string',
                    'default' => 'menu_order',
                    'description' => 'Sort products by (menu_order, title, date, price, popularity, rating)',
                    'options' => ['menu_order', 'title', 'date', 'price', 'popularity', 'rating']
                ],
                [
                    'name' => 'order',
                    'type' => 'string',
                    'default' => 'ASC',
                    'description' => 'Sort order direction (ASC or DESC)',
                    'options' => ['ASC', 'DESC']
                ],
                [
                    'name' => 'show_price',
                    'type' => 'boolean',
                    'default' => true,
                    'description' => 'Display product prices'
                ],
                [
                    'name' => 'show_rating',
                    'type' => 'boolean',
                    'default' => true,
                    'description' => 'Display product ratings'
                ],
                [
                    'name' => 'show_add_to_cart',
                    'type' => 'boolean',
                    'default' => true,
                    'description' => 'Show "Add to Cart" buttons'
                ],
                [
                    'name' => 'show_quick_view',
                    'type' => 'boolean',
                    'default' => true,
                    'description' => 'Enable quick view functionality'
                ],
                [
                    'name' => 'show_wishlist',
                    'type' => 'boolean',
                    'default' => true,
                    'description' => 'Show wishlist buttons'
                ],
                [
                    'name' => 'card_style',
                    'type' => 'string',
                    'default' => 'default',
                    'description' => 'Product card style (default, modern, minimal, premium)',
                    'options' => ['default', 'modern', 'minimal', 'premium']
                ],
                [
                    'name' => 'enable_carousel',
                    'type' => 'boolean',
                    'default' => false,
                    'description' => 'Enable carousel/slider mode'
                ],
                [
                    'name' => 'carousel_autoplay',
                    'type' => 'boolean',
                    'default' => true,
                    'description' => 'Enable carousel autoplay'
                ],
                [
                    'name' => 'carousel_speed',
                    'type' => 'number',
                    'default' => 5000,
                    'description' => 'Carousel autoplay speed in milliseconds (1000-30000)',
                    'range' => '1000-30000'
                ],
                [
                    'name' => 'enable_lazy_loading',
                    'type' => 'boolean',
                    'default' => true,
                    'description' => 'Enable lazy loading for product images'
                ],
                [
                    'name' => 'hide_out_of_stock',
                    'type' => 'boolean',
                    'default' => false,
                    'description' => 'Hide out-of-stock products'
                ]
            ],
            'examples' => [
                [
                    'title' => 'Basic Usage',
                    'code' => '[featured_products title="Best Sellers" limit="6" columns="3"]'
                ],
                [
                    'title' => 'Category-Specific',
                    'code' => '[featured_products categories="electronics,clothing" limit="8"]'
                ],
                [
                    'title' => 'Sale Items Carousel',
                    'code' => '[featured_products title="Flash Sale" on_sale_only="true" enable_carousel="true" limit="10"]'
                ],
                [
                    'title' => 'Minimal Style',
                    'code' => '[featured_products card_style="minimal" show_rating="false" columns="2"]'
                ],
                [
                    'title' => 'Custom View All',
                    'code' => '[featured_products view_all_text="See More" view_all_url="/category/featured"]'
                ],
                [
                    'title' => 'PHP Template Usage',
                    'code' => '<?php echo do_shortcode(\'[featured_products title="Staff Picks" limit="4"]\'); ?>'
                ]
            ]
        ];
    }
}
<?php
/**
 * Hero Section Hybrid Component
 * 
 * Provides Hero Section functionality for both Gutenberg blocks and shortcodes
 * 
 * @package YourStore\Commerce
 */

declare(strict_types=1);

namespace YourStore\Commerce\Components;

use YourStore\Commerce\Abstracts\BaseComponent;

// Prevent direct access
defined('ABSPATH') || exit;

/**
 * Hero Section Component
 * 
 * Creates compelling hero banners that work both as blocks and shortcodes.
 */
final class HeroSectionComponent extends BaseComponent {
    
    /**
     * Get component name
     * 
     * @return string Component name
     */
    protected function get_component_name(): string {
        return 'hero-section';
    }
    
    /**
     * Get default attributes
     * 
     * @return array Default component attributes
     */
    protected function get_default_attributes(): array {
        return [
            'title' => 'Welcome to YourStore',
            'subtitle' => 'Discover amazing products at great prices',
            'cta_text' => 'Shop Now',
            'cta_url' => '/shop',
            'background_image' => '',
            'overlay_opacity' => 0.5,
            'text_alignment' => 'center',
            'height' => 'auto',
            'show_scroll_indicator' => true,
            'enable_parallax' => true,
        ];
    }
    
    /**
     * Render component content
     * 
     * @param array $attributes Processed attributes
     * @return string Rendered HTML
     */
    protected function render_content(array $attributes): string {
        // Add component-specific data
        $attributes['component_id'] = 'hero-' . wp_generate_uuid4();
        
        // Generate Vue wrapper with specific classes
        return $this->generate_vue_wrapper($attributes, 'hero-section-wrapper');
    }
    
    /**
     * Sanitize component attributes
     * 
     * @param string $key Attribute name
     * @param mixed $value Attribute value
     * @return mixed Sanitized value
     */
    protected function sanitize_attribute(string $key, $value) {
        switch ($key) {
            case 'title':
            case 'subtitle':
                return wp_kses_post($value);
                
            case 'cta_text':
                return sanitize_text_field($value);
                
            case 'cta_url':
                return esc_url_raw($value);
                
            case 'background_image':
                return esc_url_raw($value);
                
            case 'overlay_opacity':
                return max(0, min(1, floatval($value)));
                
            case 'text_alignment':
                $allowed_alignments = ['left', 'center', 'right'];
                return in_array($value, $allowed_alignments) ? $value : 'center';
                
            case 'height':
                // Allow specific height values or 'auto'
                if ($value === 'auto') {
                    return 'auto';
                }
                // Validate CSS height values (e.g., '500px', '50vh')
                if (preg_match('/^\d+(px|vh|vw|em|rem|%)$/', $value)) {
                    return sanitize_text_field($value);
                }
                return 'auto';
                
            case 'show_scroll_indicator':
            case 'enable_parallax':
                return boolval($value);
                
            default:
                return parent::sanitize_attribute($key, $value);
        }
    }
    
    /**
     * Get block supports configuration
     * 
     * @return array Block supports
     */
    protected function get_block_supports(): array {
        return array_merge(parent::get_block_supports(), [
            'align' => ['full'],
            'color' => [
                'background' => true,
                'text' => true,
            ],
            'spacing' => [
                'margin' => ['top', 'bottom'],
                'padding' => false,
            ],
        ]);
    }
    
    /**
     * Get shortcode usage documentation
     * 
     * @return array Shortcode documentation
     */
    public static function get_shortcode_docs(): array {
        return [
            'name' => 'hero_section',
            'description' => 'Create compelling hero banners for your pages',
            'examples' => [
                'basic' => '[hero_section title="Welcome" subtitle="Discover amazing products" cta_text="Shop Now"]',
                'with_background' => '[hero_section title="New Collection" background_image="/path/to/image.jpg" overlay_opacity="0.3"]',
                'custom_height' => '[hero_section title="Sale" height="70vh" text_alignment="left"]',
                'minimal' => '[hero_section title="Coming Soon" show_scroll_indicator="false" enable_parallax="false"]',
            ],
            'attributes' => [
                'title' => [
                    'type' => 'string',
                    'default' => 'Welcome to YourStore',
                    'description' => 'Main headline text (HTML allowed)',
                ],
                'subtitle' => [
                    'type' => 'string', 
                    'default' => 'Discover amazing products at great prices',
                    'description' => 'Subtitle text (HTML allowed)',
                ],
                'cta_text' => [
                    'type' => 'string',
                    'default' => 'Shop Now',
                    'description' => 'Call-to-action button text',
                ],
                'cta_url' => [
                    'type' => 'string',
                    'default' => '/shop',
                    'description' => 'Call-to-action button URL',
                ],
                'background_image' => [
                    'type' => 'string',
                    'default' => '',
                    'description' => 'Background image URL',
                ],
                'overlay_opacity' => [
                    'type' => 'number',
                    'default' => 0.5,
                    'description' => 'Background overlay opacity (0-1)',
                ],
                'text_alignment' => [
                    'type' => 'string',
                    'default' => 'center',
                    'description' => 'Text alignment: left, center, right',
                ],
                'height' => [
                    'type' => 'string',
                    'default' => 'auto',
                    'description' => 'Hero section height (auto, px, vh, etc.)',
                ],
                'show_scroll_indicator' => [
                    'type' => 'boolean',
                    'default' => true,
                    'description' => 'Show scroll down indicator',
                ],
                'enable_parallax' => [
                    'type' => 'boolean',
                    'default' => true,
                    'description' => 'Enable parallax scrolling effect',
                ],
            ],
        ];
    }
}
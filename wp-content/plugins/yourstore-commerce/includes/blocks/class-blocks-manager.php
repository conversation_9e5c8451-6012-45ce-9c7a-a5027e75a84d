<?php
/**
 * Blocks Manager
 *
 * @package YourStore\Commerce
 */

declare(strict_types=1);

namespace YourStore\Commerce\Blocks;

// Prevent direct access
defined('ABSPATH') || exit;

/**
 * Blocks Manager class
 */
class BlocksManager {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('init', [$this, 'register_block_category']);
        add_action('init', [$this, 'register_blocks']);
        add_action('enqueue_block_editor_assets', [$this, 'enqueue_block_editor_assets']);
    }
    
    /**
     * Register custom block category
     *
     * @return void
     */
    public function register_block_category(): void {
        if (function_exists('register_block_pattern_category')) {
            register_block_pattern_category('yourstore-commerce', [
                'label' => __('YourStore Commerce', 'yourstore-commerce')
            ]);
        }
    }
    
    /**
     * Register all blocks
     *
     * @return void
     */
    public function register_blocks(): void {
        // Debug: Log block registration
        error_log('YourStore: Registering blocks and hybrid components...');
        
        // Register new hybrid components (supports both blocks and shortcodes)
        $hybrid_components = [
            new \YourStore\Commerce\Components\HeroSectionComponent(),
            new \YourStore\Commerce\Components\ProductGridComponent(),
            new \YourStore\Commerce\Components\FeaturedProductsComponent(),
        ];
        
        foreach ($hybrid_components as $component) {
            $class_name = get_class($component);
            error_log("YourStore: Initializing hybrid component: {$class_name}");
            
            if (method_exists($component, 'init')) {
                $component->init(); // Registers both block and shortcode
                error_log("YourStore: Successfully initialized hybrid component: {$class_name}");
            } else {
                error_log("YourStore: Hybrid component {$class_name} has no init method");
            }
        }
        
        // Continue with legacy blocks (will be converted progressively)
        $legacy_blocks = [
            // ProductGridBlock moved to hybrid component above
            // FeaturedProductsBlock moved to hybrid component above
            new CategoryShowcaseBlock(),
            new NewsletterSignupBlock(),
            new SiteFooterBlock(),
        ];
        
        foreach ($legacy_blocks as $block) {
            $class_name = get_class($block);
            error_log("YourStore: Initializing legacy block: {$class_name}");
            
            if (method_exists($block, 'init')) {
                $block->init();
                error_log("YourStore: Successfully initialized legacy block: {$class_name}");
            } else {
                error_log("YourStore: Legacy block {$class_name} has no init method");
            }
        }
    }
    
    /**
     * Enqueue block editor assets
     *
     * @return void
     */
    public function enqueue_block_editor_assets(): void {
        wp_enqueue_script(
            'yourstore-commerce-blocks',
            YOURSTORE_COMMERCE_PLUGIN_URL . 'assets/js/admin/blocks.js',
            ['wp-blocks', 'wp-element', 'wp-editor'],
            YOURSTORE_COMMERCE_VERSION,
            true
        );
        
        wp_enqueue_style(
            'yourstore-commerce-blocks-editor',
            YOURSTORE_COMMERCE_PLUGIN_URL . 'assets/css/admin/blocks-editor.css',
            ['wp-edit-blocks'],
            YOURSTORE_COMMERCE_VERSION
        );
    }
}
/*
 * YourStore Commerce Global Styles
 * 
 * This file contains global styles that work with Tailwind CSS
 * when loaded via CDN. In production, this would be processed
 * with PostCSS to include Tailwind directives.
 */

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Plus+Jakarta+Sans:wght@400;500;600;700;800&family=JetBrains+Mono:wght@400;500&display=swap');
@import url('./components/authentication.css');

/* CSS Custom Properties for consistent theming */
:root {
  /* Light mode colors - matching theme.json and Tailwind config */
  --color-primary: #16a34a;
  --color-primary-600: #059669;
  --color-primary-700: #047857;
  --color-secondary: #64748b;
  --color-secondary-600: #475569;
  --color-secondary-700: #334155;
  --color-secondary-800: #1e293b;
  --color-secondary-900: #0f172a;
  --color-accent: #f59e0b;
  --color-white: #ffffff;
  --color-gray-50: #f8fafc;
  --color-gray-100: #f1f5f9;
  --color-gray-200: #e2e8f0;
  --color-gray-300: #cbd5e1;
  
  /* Typography */
  --font-inter: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
  --font-jakarta: 'Plus Jakarta Sans', -apple-system, BlinkMacSystemFont, system-ui, sans-serif;
  --font-mono: 'JetBrains Mono', 'Fira Code', Monaco, Consolas, monospace;
  
  /* Spacing */
  --content-width: 1200px;
  --content-wide-width: 1400px;
  
  /* Border radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  
  /* Transitions */
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-base: 200ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* Dark mode colors */
.dark {
  --color-white: #0f172a;
  --color-gray-50: #1e293b;
  --color-gray-100: #334155;
  --color-gray-200: #475569;
  --color-gray-300: #64748b;
}

/* Base styles */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-inter);
  line-height: 1.6;
  color: var(--color-secondary-700);
  background-color: var(--color-white);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Typography hierarchy */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-jakarta);
  font-weight: 600;
  line-height: 1.3;
  color: var(--color-secondary-900);
}

h1 {
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1.2;
}

h2 {
  font-size: 2rem;
  line-height: 1.25;
}

h3 {
  font-size: 1.5rem;
  line-height: 1.3;
}

/* Price display using monospace font */
.price,
.woocommerce-Price-amount,
.woocommerce .price,
.product-price {
  font-family: var(--font-mono) !important;
  font-weight: 500;
  color: var(--color-primary);
}

/* Button base styles */
.btn,
.wp-block-button__link {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-family: var(--font-inter);
  font-weight: 500;
  text-decoration: none;
  border-radius: var(--radius-md);
  transition: all var(--transition-base);
  cursor: pointer;
  border: none;
  outline: none;
}

.btn:focus,
.wp-block-button__link:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

.btn-primary {
  background-color: var(--color-primary);
  color: var(--color-white);
  padding: 0.625rem 1.5rem;
}

.btn-primary:hover {
  background-color: var(--color-primary-600);
}

.btn-primary:active {
  background-color: var(--color-primary-700);
}

/* Card styles */
.card {
  background-color: var(--color-white);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-gray-200);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-base);
}

.card:hover {
  box-shadow: var(--shadow-md);
}

/* Form elements */
.form-input,
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
textarea,
select {
  width: 100%;
  padding: 0.625rem 1rem;
  border: 1px solid var(--color-gray-300);
  border-radius: var(--radius-md);
  font-family: var(--font-inter);
  font-size: 0.875rem;
  background-color: var(--color-white);
  color: var(--color-secondary-700);
  transition: all var(--transition-fast);
}

.form-input:focus,
input:focus,
textarea:focus,
select:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(22, 163, 74, 0.1);
}

/* Loading states */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Vue.js transition classes */
.fade-enter-active,
.fade-leave-active {
  transition: opacity var(--transition-base);
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: all var(--transition-base);
}

.slide-enter-from {
  transform: translateX(-10px);
  opacity: 0;
}

.slide-leave-to {
  transform: translateX(10px);
  opacity: 0;
}

/* Hero Section Specific Animations */
.hero-enter-active {
  transition: all 1s cubic-bezier(0.4, 0, 0.2, 1);
}

.hero-enter-from {
  opacity: 0;
  transform: translateY(20px) scale(0.95);
}

.hero-enter-to {
  opacity: 1;
  transform: translateY(0) scale(1);
}

/* Enhanced animations for hero elements */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes fadeInScale {
  0% {
    opacity: 0;
    transform: scale(0.9) translateY(20px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Button shine animation */
@keyframes buttonShine {
  0% {
    transform: translateX(-100%) skewX(-15deg);
  }
  100% {
    transform: translateX(100%) skewX(-15deg);
  }
}

.btn-shine {
  position: relative;
  overflow: hidden;
}

.btn-shine::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transform: translateX(-100%) skewX(-15deg);
  transition: transform 0.6s;
}

.btn-shine:hover::before {
  transform: translateX(100%) skewX(-15deg);
}

/* Product specific styles */
.product-card {
  position: relative;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.product-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.product-image {
  aspect-ratio: 1;
  object-fit: cover;
  width: 100%;
  background-color: var(--color-gray-100);
  transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.product-card:hover .product-image {
  transform: scale(1.05);
}

.product-title {
  font-family: var(--font-jakarta);
  font-weight: 600;
  font-size: 1.125rem;
  color: var(--color-secondary-800);
  line-height: 1.4;
  transition: color var(--transition-base);
}

.product-card:hover .product-title {
  color: var(--color-primary);
}

.product-price {
  font-size: 1.25rem;
  font-weight: 600;
}

.sale-badge {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  background-color: var(--color-accent);
  color: var(--color-white);
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  animation: pulse 2s infinite;
}

.badge-sale {
  background: linear-gradient(135deg, var(--color-accent), #e97e0b);
  color: var(--color-white);
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  box-shadow: var(--shadow-sm);
}

/* Quick view modal styles */
.modal-backdrop {
  backdrop-filter: blur(4px);
  animation: fadeIn 0.2s ease-out;
}

.modal-content {
  animation: scaleIn 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  max-height: 90vh;
  overflow-y: auto;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Product hover effects */
.product-card .quick-actions {
  opacity: 0;
  transform: translateY(10px);
  transition: all var(--transition-base);
}

.product-card:hover .quick-actions {
  opacity: 1;
  transform: translateY(0);
}

.product-card .quick-add-cart {
  opacity: 0;
  transform: translateY(100%);
  transition: all var(--transition-base);
}

.product-card:hover .quick-add-cart {
  opacity: 1;
  transform: translateY(0);
}

/* Wishlist heart animation */
.wishlist-btn {
  transition: all var(--transition-fast);
}

.wishlist-btn:hover {
  transform: scale(1.1);
}

.wishlist-btn.active {
  color: #ef4444;
  animation: heartBeat 0.6s ease-in-out;
}

@keyframes heartBeat {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
}

/* Price display enhancements */
.price-tag {
  font-family: var(--font-mono);
  font-weight: 600;
  color: var(--color-primary);
  font-size: 1.125rem;
}

.price-original {
  text-decoration: line-through;
  color: var(--color-secondary-400);
  font-size: 0.875rem;
}

.price-save {
  color: #059669;
  font-weight: 500;
  font-size: 0.875rem;
}

/* Loading states for products */
.product-loading {
  position: relative;
  overflow: hidden;
}

.product-loading::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Enhanced button styles */
.btn-add-cart {
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, var(--color-primary), #059669);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius-md);
  font-weight: 600;
  transition: all var(--transition-base);
  box-shadow: var(--shadow-sm);
}

.btn-add-cart:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.btn-add-cart:active {
  transform: translateY(0);
}

.btn-add-cart:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

/* Text clamping utilities */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Responsive utilities */
.container {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .container {
    max-width: 640px;
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

@media (min-width: 1280px) {
  .container {
    max-width: var(--content-width);
  }
}

@media (min-width: 1536px) {
  .container {
    max-width: var(--content-wide-width);
  }
}

/* Accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus visible for better accessibility */
*:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* Prevent FOUC (Flash of Unstyled Content) */
[v-cloak] {
  display: none !important;
}

/* ===== CATEGORY SHOWCASE COMPONENT STYLES ===== */

.yourstore-category-showcase .category-card {
  position: relative;
  overflow: hidden;
  transition: all var(--transition-slow);
  border: 1px solid var(--color-gray-200);
}

.yourstore-category-showcase .category-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent, rgba(22, 163, 74, 0.05));
  opacity: 0;
  transition: opacity var(--transition-base);
  z-index: 1;
}

.yourstore-category-showcase .category-card:hover::before {
  opacity: 1;
}

.yourstore-category-showcase .category-card:hover {
  border-color: var(--color-primary);
  box-shadow: 0 20px 40px -12px rgba(22, 163, 74, 0.15);
}

/* Category icon animations */
.yourstore-category-showcase .category-card .category-icon {
  transition: all var(--transition-base);
  position: relative;
  z-index: 2;
}

.yourstore-category-showcase .category-card:hover .category-icon {
  transform: scale(1.1) rotate(5deg);
}

/* Category image overlay effects */
.yourstore-category-showcase .category-image-overlay {
  background: linear-gradient(135deg, rgba(0,0,0,0.3) 0%, rgba(0,0,0,0.1) 100%);
  transition: all var(--transition-base);
}

.yourstore-category-showcase .category-card:hover .category-image-overlay {
  background: linear-gradient(135deg, rgba(22, 163, 74, 0.4) 0%, rgba(22, 163, 74, 0.1) 100%);
}

/* Featured products preview styles */
.yourstore-category-showcase .featured-products-preview {
  transform: translateY(10px);
  opacity: 0;
  transition: all var(--transition-base);
}

.yourstore-category-showcase .category-card:hover .featured-products-preview {
  transform: translateY(0);
  opacity: 1;
}

/* Category statistics animation */
.yourstore-category-showcase .category-stats {
  animation: slideInUp 0.8s ease-out;
  animation-fill-mode: both;
}

.yourstore-category-showcase .category-stats:nth-child(1) { animation-delay: 0.1s; }
.yourstore-category-showcase .category-stats:nth-child(2) { animation-delay: 0.2s; }
.yourstore-category-showcase .category-stats:nth-child(3) { animation-delay: 0.3s; }

/* Carousel specific styles */
.yourstore-category-showcase .carousel-container {
  overflow: hidden;
  touch-action: pan-y;
}

.yourstore-category-showcase .carousel-track {
  display: flex;
  transition: transform var(--transition-slow);
  will-change: transform;
}

.yourstore-category-showcase .carousel-card {
  flex: 0 0 auto;
  scroll-snap-align: start;
}

/* Drag feedback for carousel */
.yourstore-category-showcase .carousel-track.dragging {
  cursor: grabbing;
  transition: none;
}

.yourstore-category-showcase .carousel-controls {
  margin-top: 2rem;
}

.yourstore-category-showcase .carousel-button {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  background: var(--color-white);
  border: 1px solid var(--color-gray-300);
  color: var(--color-secondary-600);
  transition: all var(--transition-base);
  box-shadow: var(--shadow-sm);
}

.yourstore-category-showcase .carousel-button:hover {
  background: var(--color-primary);
  color: var(--color-white);
  border-color: var(--color-primary);
  box-shadow: var(--shadow-md);
}

.yourstore-category-showcase .carousel-button:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  transform: none;
}

.yourstore-category-showcase .carousel-dots {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
}

.yourstore-category-showcase .carousel-dot {
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
  background: var(--color-gray-300);
  transition: all var(--transition-base);
  cursor: pointer;
}

.yourstore-category-showcase .carousel-dot.active {
  background: var(--color-primary);
  transform: scale(1.2);
}

.yourstore-category-showcase .carousel-dot:hover {
  background: var(--color-primary);
  opacity: 0.8;
}

/* Category badge styles */
.yourstore-category-showcase .category-badge {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-sm);
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  box-shadow: var(--shadow-sm);
  z-index: 10;
}

.yourstore-category-showcase .category-badge.hot {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  animation: pulse 2s infinite;
}

.yourstore-category-showcase .category-badge.new {
  background: linear-gradient(135deg, var(--color-primary), #059669);
}

.yourstore-category-showcase .category-badge.trending {
  background: linear-gradient(135deg, var(--color-accent), #d97706);
}

/* Loading skeleton for category cards */
.yourstore-category-showcase .category-skeleton {
  background: var(--color-gray-100);
  border-radius: var(--radius-lg);
  overflow: hidden;
  position: relative;
}

.yourstore-category-showcase .category-skeleton::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.8), transparent);
  animation: skeletonShimmer 1.5s infinite;
}

@keyframes skeletonShimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* Error state styling */
.yourstore-category-showcase .error-state {
  text-align: center;
  padding: 3rem 1rem;
  color: var(--color-secondary-600);
}

.yourstore-category-showcase .error-icon {
  font-size: 3rem;
  color: #ef4444;
  margin-bottom: 1rem;
}

.yourstore-category-showcase .retry-button {
  background: var(--color-primary);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius-md);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-base);
}

.yourstore-category-showcase .retry-button:hover {
  background: var(--color-primary-600);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

/* Responsive adjustments for category showcase */
@media (max-width: 768px) {
  .yourstore-category-showcase .category-card {
    margin-bottom: 1rem;
  }
  
  .yourstore-category-showcase .carousel-card {
    width: 85vw;
    max-width: 300px;
  }
  
  .yourstore-category-showcase .category-stats {
    font-size: 0.875rem;
  }
}

@media (max-width: 640px) {
  .yourstore-category-showcase .carousel-card {
    width: 90vw;
    max-width: 280px;
  }
  
  .yourstore-category-showcase .carousel-controls {
    margin-top: 1.5rem;
  }
  
  .yourstore-category-showcase .carousel-button {
    width: 2.5rem;
    height: 2.5rem;
  }
}

/* ===== NEWSLETTER SIGNUP COMPONENT STYLES ===== */

.newsletter-signup-section {
  position: relative;
  overflow: hidden;
}

.newsletter-signup-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 80%, rgba(22, 163, 74, 0.05) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(245, 158, 11, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.newsletter-signup-form {
  position: relative;
  z-index: 2;
}

/* Input field enhancements */
.newsletter-signup-form input[type="email"] {
  position: relative;
  background: white;
  border: 2px solid var(--color-gray-300);
  border-radius: var(--radius-lg);
  font-size: 1rem;
  line-height: 1.5;
  transition: all var(--transition-base);
  box-shadow: var(--shadow-sm);
}

.newsletter-signup-form input[type="email"]:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 4px rgba(22, 163, 74, 0.1);
  outline: none;
  transform: translateY(-1px);
}

.newsletter-signup-form input[type="email"]:hover {
  border-color: var(--color-primary);
}

/* Button enhancements */
.newsletter-signup-form button[type="submit"] {
  position: relative;
  background: linear-gradient(135deg, var(--color-primary), #059669);
  border: none;
  border-radius: var(--radius-lg);
  color: white;
  font-weight: 600;
  text-transform: none;
  letter-spacing: 0.025em;
  box-shadow: var(--shadow-md);
  transition: all var(--transition-base);
  overflow: hidden;
}

.newsletter-signup-form button[type="submit"]:hover {
  background: linear-gradient(135deg, #059669, #047857);
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.newsletter-signup-form button[type="submit"]:active {
  transform: translateY(0);
}

.newsletter-signup-form button[type="submit"]:disabled {
  background: var(--color-gray-400);
  cursor: not-allowed;
  transform: none;
  opacity: 0.7;
}

/* Button shine effect */
.newsletter-signup-form button[type="submit"]::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.6s;
}

.newsletter-signup-form button[type="submit"]:hover::before {
  left: 100%;
}

/* Success animation */
.newsletter-success {
  animation: successBounce 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes successBounce {
  0% {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Error states */
.newsletter-error {
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

/* Loading states */
.newsletter-loading {
  position: relative;
  overflow: hidden;
}

.newsletter-loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
  animation: loadingShimmer 1.5s infinite;
}

@keyframes loadingShimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* Theme variations */
.newsletter-signup-section.theme-dark {
  background: var(--color-secondary-900);
  color: white;
}

.newsletter-signup-section.theme-dark input[type="email"] {
  background: var(--color-secondary-800);
  border-color: var(--color-secondary-600);
  color: white;
}

.newsletter-signup-section.theme-dark input[type="email"]::placeholder {
  color: var(--color-secondary-400);
}

.newsletter-signup-section.theme-primary {
  background: linear-gradient(135deg, var(--color-primary), #059669);
  color: white;
}

.newsletter-signup-section.theme-primary input[type="email"] {
  background: rgba(255,255,255,0.9);
  border-color: rgba(255,255,255,0.3);
}

.newsletter-signup-section.theme-primary button[type="submit"] {
  background: white;
  color: var(--color-primary);
}

.newsletter-signup-section.theme-primary button[type="submit"]:hover {
  background: var(--color-gray-100);
}

/* Privacy notice styling */
.newsletter-privacy {
  font-size: 0.875rem;
  line-height: 1.4;
  opacity: 0.8;
}

.newsletter-privacy a {
  font-weight: 500;
  text-decoration: underline;
  text-underline-offset: 2px;
  transition: all var(--transition-fast);
}

.newsletter-privacy a:hover {
  text-decoration: none;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .newsletter-signup-form.horizontal {
    flex-direction: column;
    gap: 1rem;
  }
  
  .newsletter-signup-form input[type="email"] {
    text-align: center;
    font-size: 1rem;
  }
  
  .newsletter-signup-form button[type="submit"] {
    padding: 0.875rem 1.5rem;
    font-size: 1rem;
  }
}

/* Animation classes for Vue transitions */
.slideInUp-enter-active {
  animation: slideInUp 0.5s ease-out;
}

.slideInUp-leave-active {
  animation: slideInUp 0.3s ease-in reverse;
}

.fadeInScale-enter-active {
  animation: fadeInScale 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.fadeInScale-leave-active {
  animation: fadeInScale 0.3s ease-in reverse;
}

/* Honeypot hidden field */
input[name="website"] {
  position: absolute !important;
  left: -9999px !important;
  opacity: 0 !important;
  pointer-events: none !important;
  tab-index: -1 !important;
}

/* Focus management */
.newsletter-signup-form input:focus-visible,
.newsletter-signup-form button:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .newsletter-signup-form input[type="email"] {
    border-width: 3px;
  }
  
  .newsletter-signup-form button[type="submit"] {
    font-weight: 700;
    border: 2px solid currentColor;
  }
}

/* ===== SITE FOOTER COMPONENT STYLES ===== */

.site-footer {
  position: relative;
  background: var(--color-secondary-900);
  color: var(--color-secondary-300);
}

.site-footer.theme-light {
  background: var(--color-gray-100);
  color: var(--color-secondary-700);
}

/* Footer link styling */
.site-footer a {
  transition: color var(--transition-fast);
}

.site-footer a:hover {
  color: var(--color-primary);
}

/* Footer headings */
.site-footer h3 {
  color: white;
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: 1rem;
}

.site-footer.theme-light h3 {
  color: var(--color-secondary-900);
}

/* Social media icons */
.site-footer .social-icons {
  display: flex;
  gap: 1rem;
}

.site-footer .social-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  background: var(--color-secondary-800);
  color: var(--color-secondary-400);
  border-radius: 50%;
  transition: all var(--transition-base);
}

.site-footer .social-icon:hover {
  background: var(--color-primary);
  color: white;
  transform: translateY(-2px) scale(1.05);
}

.site-footer.theme-light .social-icon {
  background: white;
  color: var(--color-secondary-600);
  border: 1px solid var(--color-gray-300);
}

.site-footer.theme-light .social-icon:hover {
  background: var(--color-primary);
  color: white;
  border-color: var(--color-primary);
}

/* Payment icons */
.site-footer .payment-methods {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.site-footer .payment-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  background: var(--color-secondary-800);
  color: var(--color-secondary-400);
  border-radius: var(--radius-sm);
  font-size: 0.875rem;
  transition: all var(--transition-base);
}

.site-footer .payment-icon:hover {
  background: var(--color-secondary-700);
  color: var(--color-secondary-200);
}

.site-footer.theme-light .payment-icon {
  background: var(--color-gray-200);
  color: var(--color-secondary-600);
}

.site-footer.theme-light .payment-icon:hover {
  background: var(--color-gray-300);
}

/* Newsletter form in footer */
.site-footer .newsletter-form {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.site-footer .newsletter-form input[type="email"] {
  background: var(--color-secondary-800);
  border: 1px solid var(--color-secondary-700);
  color: white;
  padding: 0.75rem 1rem;
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  transition: all var(--transition-base);
}

.site-footer .newsletter-form input[type="email"]::placeholder {
  color: var(--color-secondary-400);
}

.site-footer .newsletter-form input[type="email"]:focus {
  border-color: var(--color-primary);
  background: var(--color-secondary-700);
  outline: none;
}

.site-footer.theme-light .newsletter-form input[type="email"] {
  background: white;
  border-color: var(--color-gray-300);
  color: var(--color-secondary-700);
}

.site-footer .newsletter-form button {
  background: var(--color-primary);
  color: white;
  border: none;
  padding: 0.75rem 1rem;
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-base);
}

.site-footer .newsletter-form button:hover {
  background: var(--color-primary-600);
  transform: translateY(-1px);
}

.site-footer .newsletter-form button:disabled {
  background: var(--color-secondary-600);
  cursor: not-allowed;
  transform: none;
}

/* Footer bottom border */
.site-footer .footer-bottom {
  border-top: 1px solid var(--color-secondary-800);
  margin-top: 3rem;
  padding-top: 2rem;
}

.site-footer.theme-light .footer-bottom {
  border-top-color: var(--color-gray-300);
}

/* Back to top button */
.back-to-top-button {
  position: fixed;
  bottom: 1.5rem;
  right: 1.5rem;
  width: 3rem;
  height: 3rem;
  background: var(--color-primary);
  color: white;
  border: none;
  border-radius: 50%;
  box-shadow: var(--shadow-lg);
  cursor: pointer;
  transition: all var(--transition-base);
  z-index: 50;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-to-top-button:hover {
  background: var(--color-primary-600);
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 20px 40px -12px rgba(22, 163, 74, 0.4);
}

.back-to-top-button:active {
  transform: translateY(0) scale(1);
}

/* Contact information styling */
.site-footer .contact-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.site-footer .contact-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}

.site-footer .contact-icon {
  color: var(--color-primary);
  width: 1rem;
  flex-shrink: 0;
  margin-top: 0.125rem;
}

/* Footer newsletter success state */
.site-footer .newsletter-success {
  text-align: center;
  padding: 1rem 0;
}

.site-footer .newsletter-success-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
  background: var(--color-primary);
  color: white;
  border-radius: 50%;
  margin-bottom: 0.75rem;
  animation: successPulse 0.6s ease-out;
}

@keyframes successPulse {
  0% {
    transform: scale(0.8);
    opacity: 0;
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Footer responsive design */
@media (max-width: 1024px) {
  .site-footer .footer-columns {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }
}

@media (max-width: 768px) {
  .site-footer .footer-columns {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  .site-footer .footer-bottom {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .site-footer .payment-methods {
    justify-content: center;
  }
  
  .back-to-top-button {
    bottom: 1rem;
    right: 1rem;
    width: 2.5rem;
    height: 2.5rem;
  }
}

@media (max-width: 640px) {
  .site-footer {
    padding: 2rem 0;
  }
  
  .site-footer .social-icons {
    justify-content: center;
  }
  
  .site-footer .contact-info {
    text-align: center;
  }
  
  .site-footer .contact-item {
    justify-content: center;
  }
}

/* Footer animation on page load */
.site-footer {
  animation: fadeInUp 0.8s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Footer links hover effects */
.site-footer ul li {
  position: relative;
}

.site-footer ul li a::before {
  content: '';
  position: absolute;
  left: 0;
  bottom: -2px;
  width: 0;
  height: 1px;
  background: var(--color-primary);
  transition: width var(--transition-base);
}

.site-footer ul li a:hover::before {
  width: 100%;
}

/* Footer company info section */
.site-footer .company-info {
  max-width: 20rem;
}

.site-footer .company-logo {
  margin-bottom: 1rem;
}

.site-footer .company-tagline {
  font-size: 0.875rem;
  line-height: 1.6;
  margin-bottom: 1.5rem;
  opacity: 0.9;
}

/* Newsletter form validation styles */
.site-footer .newsletter-form .form-error {
  color: #ef4444;
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

.site-footer .newsletter-form input.error {
  border-color: #ef4444;
  background-color: rgba(239, 68, 68, 0.1);
}

/* Footer legal links */
.site-footer .legal-links {
  display: flex;
  gap: 1.5rem;
  font-size: 0.875rem;
}

.site-footer .legal-links a {
  color: var(--color-secondary-400);
  transition: color var(--transition-fast);
}

.site-footer .legal-links a:hover {
  color: var(--color-primary);
}

@media (max-width: 640px) {
  .site-footer .legal-links {
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
  }
}

/* =================================================================
   HEADER & NAVIGATION STYLES
   ================================================================= */

/* Site Header */
.site-header {
  position: sticky;
  top: 0;
  z-index: 100;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--color-gray-200);
  transition: all var(--transition-base);
}

.site-header.scrolled {
  box-shadow: var(--shadow-md);
  background: rgba(255, 255, 255, 0.98);
}

/* Logo Styling */
.site-header .wp-block-site-logo {
  margin: 0;
}

.site-header .wp-block-site-logo img {
  height: 2.5rem;
  width: auto;
  max-width: 150px;
}

/* Navigation Links */
.site-header .wp-block-navigation {
  gap: 2rem;
  display: flex !important;
  flex-direction: row !important;
  align-items: center;
}

.site-header .wp-block-navigation.horizontal-nav {
  display: flex !important;
  flex-direction: row !important;
  align-items: center;
  justify-content: flex-start;
}

.site-header .wp-block-navigation .wp-block-navigation__container {
  display: flex !important;
  flex-direction: row !important;
  gap: 2rem;
  align-items: center;
  list-style: none;
  margin: 0;
  padding: 0;
}

.site-header .wp-block-navigation .wp-block-navigation-link {
  margin: 0;
  display: inline-block;
}

.site-header .wp-block-navigation .wp-block-navigation-link a {
  color: var(--color-secondary-700);
  font-weight: 500;
  font-size: 0.95rem;
  text-decoration: none;
  padding: 0.5rem 0;
  position: relative;
  transition: all var(--transition-fast);
}

.site-header .wp-block-navigation .wp-block-navigation-link a:hover {
  color: var(--color-primary);
}

.site-header .wp-block-navigation .wp-block-navigation-link a::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--color-primary);
  transition: width var(--transition-base);
}

.site-header .wp-block-navigation .wp-block-navigation-link a:hover::after,
.site-header .wp-block-navigation .wp-block-navigation-link.current-menu-item a::after {
  width: 100%;
}

/* Additional horizontal navigation fixes */
@media (min-width: 768px) {
  .site-header .wp-block-navigation:not(.has-modal-open) {
    display: flex !important;
    flex-direction: row !important;
  }
  
  .site-header .wp-block-navigation ul {
    display: flex !important;
    flex-direction: row !important;
    gap: 2rem;
    align-items: center;
    list-style: none;
    margin: 0;
    padding: 0;
  }
  
  .site-header .wp-block-navigation li {
    display: inline-block !important;
    margin: 0;
  }
  
  /* Override any WordPress vertical styles */
  .site-header .wp-block-navigation .wp-block-navigation-item {
    display: inline-block !important;
  }
  
  .site-header .wp-block-navigation .wp-block-navigation-submenu {
    position: relative;
  }
}

/* Button Styles for Header */
.btn-ghost {
  background: transparent;
  border: 1px solid transparent;
  padding: 0.5rem;
  border-radius: var(--radius-md);
  color: var(--color-secondary-600);
  transition: all var(--transition-fast);
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  line-height: 1;
}

.btn-ghost:hover {
  background: var(--color-gray-100);
  color: var(--color-secondary-800);
  border-color: var(--color-gray-200);
}

.btn-ghost:active {
  background: var(--color-gray-200);
  transform: scale(0.98);
}

.btn-sm {
  padding: 0.4rem;
  font-size: 0.875rem;
  min-width: 2.5rem;
  min-height: 2.5rem;
}

/* Header Icons with Badges */
.site-header .btn-ghost.relative .absolute {
  position: absolute;
  top: -2px;
  right: -2px;
  background: var(--color-primary);
  color: white;
  font-size: 0.65rem;
  font-weight: 600;
  min-width: 1.125rem;
  min-height: 1.125rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid white;
  line-height: 1;
}

/* Search Overlay Improvements */
#search-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 1000;
  backdrop-filter: blur(4px);
  transition: all var(--transition-base);
}

#search-overlay .bg-white {
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  max-width: 28rem;
  width: 100%;
  margin: 0 1rem;
}

/* Mobile Menu Styles */
#mobile-menu {
  background: white;
  border-top: 1px solid var(--color-gray-200);
  padding: 1rem 0;
}

#mobile-menu .wp-block-navigation {
  gap: 0;
}

#mobile-menu .wp-block-navigation .wp-block-navigation-link a {
  display: block;
  padding: 0.75rem 1rem;
  color: var(--color-secondary-700);
  font-weight: 500;
  border-radius: var(--radius-md);
  margin: 0.125rem 0;
  transition: all var(--transition-fast);
}

#mobile-menu .wp-block-navigation .wp-block-navigation-link a:hover {
  background: var(--color-gray-50);
  color: var(--color-primary);
}

/* Input Styling for Search */
.input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--color-gray-300);
  border-radius: var(--radius-md);
  font-size: 0.95rem;
  transition: all var(--transition-fast);
  background: white;
}

.input:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(22, 163, 74, 0.1);
}

.input.pl-10 {
  padding-left: 2.5rem;
}

/* Responsive Header Adjustments */
@media (max-width: 768px) {
  .site-header .flex.items-center.space-x-8 {
    gap: 1rem;
  }
  
  .site-header .flex.items-center.space-x-4 {
    gap: 0.5rem;
  }
  
  .site-header .wp-block-site-logo img {
    height: 2rem;
    max-width: 120px;
  }
}

@media (max-width: 640px) {
  .site-header .py-4 {
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
  }
  
  .btn-sm {
    padding: 0.3rem;
    min-width: 2.25rem;
    min-height: 2.25rem;
  }
}

/* =================================================================
   ENHANCED PRODUCT FILTER STYLES
   ================================================================= */

/* Custom Range Slider Styles */
.slider-thumb {
    -webkit-appearance: none;
    background: transparent;
    pointer-events: none;
    position: relative;
}

.slider-thumb::-webkit-slider-thumb {
    -webkit-appearance: none;
    height: 20px;
    width: 20px;
    border-radius: 50%;
    background: var(--color-primary);
    border: 2px solid #ffffff;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
    cursor: pointer;
    pointer-events: all;
    position: relative;
    transition: all var(--transition-base);
}

.slider-thumb::-webkit-slider-thumb:hover {
    background: var(--color-primary-600);
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(22, 163, 74, 0.3);
}

.slider-thumb::-moz-range-thumb {
    height: 20px;
    width: 20px;
    border-radius: 50%;
    background: var(--color-primary);
    border: 2px solid #ffffff;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
    cursor: pointer;
    pointer-events: all;
    border: none;
    transition: all var(--transition-base);
}

.slider-thumb::-moz-range-thumb:hover {
    background: var(--color-primary-600);
    transform: scale(1.1);
}

/* Slider track styling */
.slider-thumb::-webkit-slider-track {
    height: 8px;
    background: var(--color-gray-200);
    border-radius: 4px;
}

.slider-thumb::-moz-range-track {
    height: 8px;
    background: var(--color-gray-200);
    border-radius: 4px;
    border: none;
}

/* Color swatch styling enhancements */
.color-swatch {
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
}

.color-swatch:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.color-swatch.selected {
    transform: scale(1.05);
    animation: colorPulse 2s infinite;
}

@keyframes colorPulse {
    0%, 100% {
        box-shadow: 0 0 0 0 rgba(22, 163, 74, 0.4);
    }
    50% {
        box-shadow: 0 0 0 8px rgba(22, 163, 74, 0);
    }
}

/* Size filter button animations */
.size-filter-btn {
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
}

.size-filter-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.size-filter-btn.selected {
    animation: buttonGlow 0.3s ease-out;
}

@keyframes buttonGlow {
    0% {
        box-shadow: 0 0 0 0 rgba(22, 163, 74, 0.4);
    }
    100% {
        box-shadow: 0 0 0 6px rgba(22, 163, 74, 0);
    }
}

/* Brand filter enhancements */
.brand-filter-item {
    transition: all var(--transition-base);
    border-radius: var(--radius-md);
}

.brand-filter-item:hover {
    background: var(--color-gray-50);
    transform: translateX(4px);
}

.brand-filter-item input[type="checkbox"]:checked + .brand-name {
    color: var(--color-primary);
    font-weight: 600;
}

/* Filter section animations */
.filter-section {
    opacity: 0;
    transform: translateY(10px);
    animation: filterSlideIn 0.4s ease-out forwards;
}

.filter-section:nth-child(1) { animation-delay: 0.1s; }
.filter-section:nth-child(2) { animation-delay: 0.2s; }
.filter-section:nth-child(3) { animation-delay: 0.3s; }
.filter-section:nth-child(4) { animation-delay: 0.4s; }
.filter-section:nth-child(5) { animation-delay: 0.5s; }

@keyframes filterSlideIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Active filter badges */
.filter-badge {
    transition: all var(--transition-fast);
    animation: filterBadgeIn 0.3s ease-out;
}

.filter-badge:hover {
    transform: scale(1.05);
}

@keyframes filterBadgeIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Clear filters button enhancement */
.clear-filters-btn {
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
}

.clear-filters-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

.clear-filters-btn:hover::before {
    left: 100%;
}

.clear-filters-btn:hover {
    background: var(--color-primary);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(22, 163, 74, 0.3);
}

/* Filter count indicators */
.filter-count {
    background: var(--color-primary);
    color: white;
    font-size: 0.7rem;
    padding: 0.125rem 0.375rem;
    border-radius: 999px;
    font-weight: 600;
    line-height: 1;
    animation: countPulse 0.3s ease-out;
}

@keyframes countPulse {
    0% {
        transform: scale(0.8);
        opacity: 0;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Mobile filter overlay enhancements */
@media (max-width: 1024px) {
    .mobile-filter-overlay {
        backdrop-filter: blur(8px);
        animation: overlayFadeIn 0.3s ease-out;
    }
    
    .mobile-filter-content {
        animation: filterSlideInRight 0.3s ease-out;
    }
    
    @keyframes overlayFadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }
    
    @keyframes filterSlideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
}

/* Enhanced price input styling */
.price-input {
    font-family: var(--font-mono);
    text-align: center;
    background: var(--color-gray-50);
    border: 1px solid var(--color-gray-300);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.price-input:focus {
    background: white;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(22, 163, 74, 0.1);
    transform: scale(1.02);
}

/* Rating filter stars */
.rating-star {
    transition: all var(--transition-fast);
    cursor: pointer;
}

.rating-star:hover {
    transform: scale(1.2);
    filter: drop-shadow(0 0 4px currentColor);
}

/* Filter section headers */
.filter-section-header {
    position: relative;
    padding-bottom: 0.5rem;
    margin-bottom: 0.75rem;
}

.filter-section-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 2rem;
    height: 2px;
    background: var(--color-primary);
    border-radius: 1px;
}

/* Improved scrollbar for filter sections */
.filter-scrollable::-webkit-scrollbar {
    width: 4px;
}

.filter-scrollable::-webkit-scrollbar-track {
    background: var(--color-gray-100);
    border-radius: 2px;
}

.filter-scrollable::-webkit-scrollbar-thumb {
    background: var(--color-gray-400);
    border-radius: 2px;
}

.filter-scrollable::-webkit-scrollbar-thumb:hover {
    background: var(--color-primary);
}
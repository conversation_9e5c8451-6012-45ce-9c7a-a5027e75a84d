/**
 * Hero Section Vue Component
 * Modern hero section with background image, overlay, and CTA
 * Enhanced with animations, user interactions, and responsive design
 */

(function() {
    'use strict';
    
    // Wait for Vue to be available
    function initHeroSection() {
        if (typeof Vue === 'undefined') {
            console.warn('YourStore Hero: Vue.js not loaded yet, retrying...');
            setTimeout(initHeroSection, 100);
            return;
        }
        
        console.log('YourStore Hero: Vue.js detected, initializing component...');
        
        // Now safely destructure Vue methods
        const { ref, computed, onMounted, onUnmounted, nextTick } = Vue;

        // Hero Section Component
        const HeroSection = {
    name: 'HeroSection',
    
    props: {
        title: {
            type: String,
            default: 'Welcome to YourStore'
        },
        subtitle: {
            type: String,
            default: 'Discover amazing products at great prices'
        },
        ctaText: {
            type: String,
            default: 'Shop Now'
        },
        ctaUrl: {
            type: String,
            default: '/shop'
        },
        backgroundImage: {
            type: String,
            default: ''
        },
        overlayOpacity: {
            type: Number,
            default: 0.5
        }
    },
    
    setup(props) {
        // Reactive data
        const isVisible = ref(false);
        const currentSlide = ref(0);
        const isScrolling = ref(false);
        const ctaLoading = ref(false);
        const heroRef = ref(null);
        const intersectionObserver = ref(null);
        
        // Hero image carousel data
        const heroImages = [
            'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=1600&h=800&fit=crop&q=80',
            'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=1600&h=800&fit=crop&q=80',
            'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=1600&h=800&fit=crop&q=80'
        ];
        
        // Computed properties
        const currentHeroImage = computed(() => {
            return props.backgroundImage || heroImages[currentSlide.value];
        });
        
        const heroStyles = computed(() => ({
            backgroundImage: `url(${currentHeroImage.value})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            backgroundRepeat: 'no-repeat',
            minHeight: '100vh',
            position: 'relative',
            transition: 'background-image 1s ease-in-out'
        }));
        
        const overlayStyles = computed(() => ({
            background: `linear-gradient(135deg, rgba(15, 23, 42, ${props.overlayOpacity + 0.1}) 0%, rgba(30, 41, 59, ${props.overlayOpacity - 0.1}) 100%)`,
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            zIndex: 1
        }));
        
        const contentClasses = computed(() => [
            'relative z-10 flex items-center justify-center min-h-screen',
            'px-4 sm:px-6 lg:px-8',
            'text-center'
        ].join(' '));
        
        // Methods
        const handleCtaClick = async () => {
            ctaLoading.value = true;
            
            // Track analytics
            if (typeof gtag !== 'undefined') {
                gtag('event', 'hero_cta_click', {
                    event_category: 'engagement',
                    event_label: props.title,
                    value: 1
                });
            }
            
            // Add loading state
            await new Promise(resolve => setTimeout(resolve, 500));
            
            // Show notification
            if (window.YourStoreCommerce) {
                window.YourStoreCommerce.showNotification('Redirecting to shop...', 'info');
            }
            
            // Navigate with smooth transition
            setTimeout(() => {
                if (props.ctaUrl.startsWith('/') || props.ctaUrl.startsWith(window.location.origin)) {
                    window.location.href = props.ctaUrl;
                } else {
                    window.open(props.ctaUrl, '_blank');
                }
                ctaLoading.value = false;
            }, 1000);
        };
        
        const nextSlide = () => {
            if (!props.backgroundImage) {
                currentSlide.value = (currentSlide.value + 1) % heroImages.length;
            }
        };
        
        const prevSlide = () => {
            if (!props.backgroundImage) {
                currentSlide.value = currentSlide.value === 0 ? heroImages.length - 1 : currentSlide.value - 1;
            }
        };
        
        const goToSlide = (index) => {
            if (!props.backgroundImage) {
                currentSlide.value = index;
            }
        };
        
        const handleScroll = () => {
            const scrolled = window.pageYOffset;
            const rate = scrolled * -0.5;
            
            if (heroRef.value) {
                heroRef.value.style.transform = `translateY(${rate}px)`;
            }
        };
        
        const setupIntersectionObserver = () => {
            intersectionObserver.value = new IntersectionObserver(
                (entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            setTimeout(() => {
                                isVisible.value = true;
                            }, 200);
                        }
                    });
                },
                { threshold: 0.1 }
            );
            
            if (heroRef.value) {
                intersectionObserver.value.observe(heroRef.value);
            }
        };
        
        // Lifecycle
        onMounted(async () => {
            await nextTick();
            setupIntersectionObserver();
            
            // Add scroll parallax effect
            window.addEventListener('scroll', handleScroll, { passive: true });
            
            // Auto-rotate hero images if no custom background
            if (!props.backgroundImage) {
                setInterval(nextSlide, 5000);
            }
            
            // Preload hero images for smooth transitions
            heroImages.forEach(src => {
                const img = new Image();
                img.src = src;
            });
        });
        
        onUnmounted(() => {
            if (intersectionObserver.value) {
                intersectionObserver.value.disconnect();
            }
            window.removeEventListener('scroll', handleScroll);
        });
        
        return {
            isVisible,
            currentSlide,
            ctaLoading,
            heroRef,
            heroStyles,
            overlayStyles,
            contentClasses,
            heroImages,
            handleCtaClick,
            nextSlide,
            prevSlide,
            goToSlide
        };
    },
    
    template: `
        <section ref="heroRef" class="yourstore-hero-section relative overflow-hidden" :style="heroStyles">
            <!-- Gradient Overlay -->
            <div :style="overlayStyles"></div>
            
            <!-- Carousel Navigation (if using default images) -->
            <div v-if="!backgroundImage" class="absolute top-1/2 left-4 right-4 z-20 flex justify-between items-center pointer-events-none">
                <button 
                    @click="prevSlide"
                    class="btn-ghost text-white/80 hover:text-white hover:bg-white/20 backdrop-blur-sm rounded-full p-3 pointer-events-auto transition-all duration-200"
                    aria-label="Previous image"
                >
                    <i class="fas fa-chevron-left text-xl"></i>
                </button>
                <button 
                    @click="nextSlide"
                    class="btn-ghost text-white/80 hover:text-white hover:bg-white/20 backdrop-blur-sm rounded-full p-3 pointer-events-auto transition-all duration-200"
                    aria-label="Next image"
                >
                    <i class="fas fa-chevron-right text-xl"></i>
                </button>
            </div>
            
            <!-- Main Content -->
            <div :class="contentClasses">
                <div class="max-w-6xl mx-auto">
                    <!-- Hero Badge -->
                    <div 
                        class="inline-flex items-center px-4 py-2 rounded-full bg-primary/20 backdrop-blur-sm border border-primary/30 text-primary-100 text-sm font-medium mb-6"
                        :class="{
                            'opacity-0 translate-y-8 scale-95': !isVisible,
                            'opacity-100 translate-y-0 scale-100': isVisible,
                            'transition-all duration-700 ease-out': true
                        }"
                    >
                        <i class="fas fa-star mr-2"></i>
                        #1 Choice for Quality Products
                    </div>
                    
                    <!-- Main Title -->
                    <h1 
                        class="text-5xl sm:text-6xl lg:text-7xl xl:text-8xl font-bold text-white mb-6 leading-tight tracking-tight"
                        :class="{
                            'opacity-0 translate-y-12 scale-95': !isVisible,
                            'opacity-100 translate-y-0 scale-100': isVisible,
                            'transition-all duration-1000 ease-out delay-200': true
                        }"
                        style="text-shadow: 0 2px 4px rgba(0,0,0,0.3);"
                        v-html="title"
                    ></h1>
                    
                    <!-- Subtitle -->
                    <p 
                        class="text-xl sm:text-2xl lg:text-3xl text-gray-200 mb-8 leading-relaxed max-w-3xl mx-auto font-light"
                        :class="{
                            'opacity-0 translate-y-8': !isVisible,
                            'opacity-100 translate-y-0': isVisible,
                            'transition-all duration-1000 ease-out delay-400': true
                        }"
                        style="text-shadow: 0 1px 2px rgba(0,0,0,0.4);"
                        v-html="subtitle"
                    ></p>
                    
                    <!-- CTA Section -->
                    <div 
                        class="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12"
                        :class="{
                            'opacity-0 translate-y-8': !isVisible,
                            'opacity-100 translate-y-0': isVisible,
                            'transition-all duration-1000 ease-out delay-600': true
                        }"
                    >
                        <!-- Primary CTA -->
                        <button
                            @click="handleCtaClick"
                            :disabled="ctaLoading"
                            class="group relative overflow-hidden bg-gradient-primary text-white font-semibold px-8 py-4 rounded-lg text-lg shadow-xl hover:shadow-2xl transform hover:scale-105 active:scale-95 transition-all duration-300 min-w-[200px]"
                            :class="{ 'cursor-not-allowed opacity-75': ctaLoading }"
                        >
                            <span v-if="!ctaLoading" class="flex items-center">
                                {{ ctaText }}
                                <i class="fas fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform duration-200"></i>
                            </span>
                            <span v-else class="flex items-center">
                                <i class="fas fa-spinner fa-spin mr-2"></i>
                                Loading...
                            </span>
                            
                            <!-- Button shine effect -->
                            <div class="absolute top-0 left-0 right-0 bottom-0 bg-gradient-to-r from-transparent via-white/20 to-transparent transform -skew-x-12 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700"></div>
                        </button>
                        
                        <!-- Secondary CTA -->
                        <button class="group text-white font-medium px-6 py-4 rounded-lg border-2 border-white/30 backdrop-blur-sm hover:bg-white/10 hover:border-white/50 transition-all duration-200">
                            <i class="fas fa-play mr-2 group-hover:scale-110 transition-transform duration-200"></i>
                            Watch Demo
                        </button>
                    </div>
                    
                    <!-- Trust Indicators -->
                    <div 
                        class="flex flex-wrap justify-center items-center gap-6 sm:gap-8 text-white/80 text-sm"
                        :class="{
                            'opacity-0 translate-y-8': !isVisible,
                            'opacity-100 translate-y-0': isVisible,
                            'transition-all duration-1000 ease-out delay-800': true
                        }"
                    >
                        <div class="flex items-center group hover:text-white transition-colors duration-200">
                            <i class="fas fa-shipping-fast mr-2 group-hover:scale-110 transition-transform duration-200"></i>
                            <span>Free shipping on orders $50+</span>
                        </div>
                        <div class="flex items-center group hover:text-white transition-colors duration-200">
                            <i class="fas fa-undo mr-2 group-hover:scale-110 transition-transform duration-200"></i>
                            <span>30-day returns</span>
                        </div>
                        <div class="flex items-center group hover:text-white transition-colors duration-200">
                            <i class="fas fa-shield-alt mr-2 group-hover:scale-110 transition-transform duration-200"></i>
                            <span>Secure checkout</span>
                        </div>
                        <div class="flex items-center group hover:text-white transition-colors duration-200">
                            <i class="fas fa-star mr-2 group-hover:scale-110 transition-transform duration-200"></i>
                            <span>4.9/5 customer rating</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Image Carousel Dots (if using default images) -->
            <div v-if="!backgroundImage" class="absolute bottom-6 left-1/2 transform -translate-x-1/2 z-20 flex space-x-2">
                <button 
                    v-for="(image, index) in heroImages" 
                    :key="index"
                    @click="goToSlide(index)"
                    class="w-3 h-3 rounded-full transition-all duration-200"
                    :class="{
                        'bg-white': currentSlide === index,
                        'bg-white/50 hover:bg-white/70': currentSlide !== index
                    }"
                    :aria-label="'Go to slide ' + (index + 1)"
                ></button>
            </div>
            
            <!-- Scroll Indicator -->
            <div 
                class="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10"
                :class="{
                    'opacity-0 translate-y-4': !isVisible,
                    'opacity-100 translate-y-0': isVisible,
                    'transition-all duration-1000 ease-out delay-1000': true
                }"
            >
                <div class="animate-bounce cursor-pointer group" @click="$emit('scroll-to-next')">
                    <div class="flex flex-col items-center text-white/60 hover:text-white/80 transition-colors duration-200">
                        <span class="text-xs mb-2 group-hover:text-white transition-colors duration-200">Scroll to explore</span>
                        <i class="fas fa-chevron-down text-xl group-hover:translate-y-1 transition-transform duration-200"></i>
                    </div>
                </div>
            </div>
            
            <!-- Floating Elements -->
            <div class="absolute inset-0 overflow-hidden pointer-events-none z-0">
                <!-- Geometric shapes -->
                <div class="absolute top-1/4 right-10 w-32 h-32 opacity-10 animate-pulse-soft hidden lg:block">
                    <svg viewBox="0 0 100 100" class="w-full h-full text-white">
                        <circle cx="50" cy="50" r="40" fill="none" stroke="currentColor" stroke-width="2"/>
                        <circle cx="50" cy="50" r="20" fill="currentColor" opacity="0.3"/>
                    </svg>
                </div>
                
                <div class="absolute bottom-1/4 left-10 w-24 h-24 opacity-10 animate-bounce-soft hidden lg:block">
                    <svg viewBox="0 0 100 100" class="w-full h-full text-white">
                        <polygon points="50,10 90,90 10,90" fill="currentColor"/>
                    </svg>
                </div>
                
                <div class="absolute top-1/3 left-1/4 w-16 h-16 opacity-5 animate-pulse hidden xl:block">
                    <svg viewBox="0 0 100 100" class="w-full h-full text-white">
                        <rect x="25" y="25" width="50" height="50" fill="currentColor" transform="rotate(45 50 50)"/>
                    </svg>
                </div>
            </div>
        </section>
    `
};

// Make component globally accessible
console.log('YourStore: HeroSection component definition:', HeroSection);
        console.log('YourStore: Setting window.HeroSection...');
        window.HeroSection = HeroSection;
        console.log('YourStore: window.HeroSection is now set to:', window.HeroSection);

        // Auto-mount component if container exists (legacy support)
        document.addEventListener('DOMContentLoaded', () => {
            const containers = document.querySelectorAll('.yourstore-hero-section');
            containers.forEach(container => {
                // Skip if already mounted by app.js or has Vue app
                if (container.__vue_app || container.querySelector('[data-vue-app]')) {
                    return;
                }
                
                let props = {};
                
                // Handle hybrid component format (data-props)
                if (container.dataset.props) {
                    try {
                        props = JSON.parse(container.dataset.props);
                    } catch (e) {
                        console.warn('YourStore: Invalid JSON in data-props:', e);
                    }
                } 
                // Handle legacy format (individual data attributes)
                else {
                    if (container.dataset.title) props.title = container.dataset.title;
                    if (container.dataset.subtitle) props.subtitle = container.dataset.subtitle;
                    if (container.dataset.ctaText) props.ctaText = container.dataset.ctaText;
                    if (container.dataset.ctaUrl) props.ctaUrl = container.dataset.ctaUrl;
                    if (container.dataset.backgroundImage) props.backgroundImage = container.dataset.backgroundImage;
                    if (container.dataset.overlayOpacity) props.overlayOpacity = parseFloat(container.dataset.overlayOpacity);
                }
                
                const app = Vue.createApp(HeroSection, props);
                container.__vue_app = app.mount(container);
            });
        });

        // Export for different module systems
        if (typeof window !== 'undefined') {
            window.HeroSection = HeroSection;
            console.log('YourStore: HeroSection component registered on window object');
        } else if (typeof module !== 'undefined' && module.exports) {
            module.exports = HeroSection;
        }
    }
    
    // Initialize the component
    initHeroSection();
    
})();
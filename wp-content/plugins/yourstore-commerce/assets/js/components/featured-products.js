/**
 * Featured Products Vue Component
 * Professional product showcase with filtering, sorting, and cart functionality
 */

(function() {
    'use strict';
    
    // Wait for Vue to be available
    function initFeaturedProducts() {
        if (typeof Vue === 'undefined') {
            console.warn('YourStore Featured Products: Vue.js not loaded yet, retrying...');
            setTimeout(initFeaturedProducts, 100);
            return;
        }
        
        console.log('YourStore Featured Products: Vue.js detected, initializing component...');
        
        // Now safely destructure Vue methods
        const { ref, computed, onMounted, onUnmounted, nextTick } = Vue;

// Featured Products Component
const FeaturedProducts = {
    name: 'FeaturedProducts',
    
    props: {
        title: {
            type: String,
            default: 'Featured Products'
        },
        subtitle: {
            type: String,
            default: 'Discover our hand-picked selection'
        },
        limit: {
            type: Number,
            default: 8
        },
        columns: {
            type: Number,
            default: 4
        },
        showViewAll: {
            type: Boolean,
            default: true
        },
        viewAllUrl: {
            type: String,
            default: '/shop'
        },
        categoryFilter: {
            type: String,
            default: ''
        },
        showFilters: {
            type: Boolean,
            default: false
        }
    },
    
    setup(props) {
        // Reactive data
        const products = ref([]);
        const filteredProducts = ref([]);
        const loading = ref(true);
        const error = ref(null);
        const isVisible = ref(false);
        const intersectionObserver = ref(null);
        const sectionRef = ref(null);
        
        // Filter and sort states
        const activeFilter = ref(props.categoryFilter || 'all');
        const sortBy = ref('featured');
        const priceRange = ref([0, 1000]);
        
        // Cart and interaction states
        const addingToCart = ref({});
        const wishlist = ref(new Set());
        const quickViewProduct = ref(null);
        const showQuickView = ref(false);
        
        // Computed properties
        const currentProducts = computed(() => {
            return filteredProducts.value.slice(0, props.limit);
        });
        
        const gridClasses = computed(() => {
            const colsMap = {
                1: 'grid-cols-1',
                2: 'grid-cols-1 sm:grid-cols-2',
                3: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3',
                4: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
                5: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5',
                6: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 xl:grid-cols-6'
            };
            return `grid gap-6 ${colsMap[props.columns] || colsMap[4]}`;
        });
        
        const availableCategories = computed(() => {
            const categories = new Set();
            products.value.forEach(product => {
                if (product.category) {
                    categories.add(product.category);
                }
            });
            return Array.from(categories);
        });
        
        const productStats = computed(() => {
            return {
                total: filteredProducts.value.length,
                onSale: filteredProducts.value.filter(p => p.sale_price).length,
                avgRating: filteredProducts.value.reduce((acc, p) => acc + (p.rating || 0), 0) / filteredProducts.value.length || 0
            };
        });
        
        // Methods
        
        const loadFallbackData = () => {
            console.log('Loading fallback data for featured products...');
            // Fallback mock data
            products.value = [
                {
                    id: 1,
                    name: 'iPhone 15 Pro Max',
                    price: 1199.00,
                    sale_price: null,
                    image: 'https://images.unsplash.com/photo-1592286062491-b43a25466cdb?w=400&h=400&fit=crop',
                    category: 'electronics',
                    rating: 4.8,
                    review_count: 1247,
                    badge: 'New',
                    featured: true,
                    short_description: 'Latest iPhone with titanium design and A17 Pro chip'
                },
                {
                    id: 2,
                    name: 'Samsung Galaxy S24 Ultra',
                    price: 1299.00,
                    sale_price: 1199.00,
                    image: 'https://images.unsplash.com/photo-1610945265064-0e34e5519bbf?w=400&h=400&fit=crop',
                    category: 'electronics',
                    rating: 4.7,
                    review_count: 892,
                    badge: 'Sale',
                    featured: true,
                    short_description: 'Premium Android phone with S Pen and AI features'
                },
                {
                    id: 3,
                    name: 'Nike Air Force 1',
                    price: 110.00,
                    sale_price: null,
                    image: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400&h=400&fit=crop',
                    category: 'clothing',
                    rating: 4.8,
                    review_count: 3421,
                    badge: null,
                    featured: true,
                    short_description: 'Classic white sneakers with iconic design'
                },
                {
                    id: 4,
                    name: 'MacBook Air M3',
                    price: 1399.00,
                    sale_price: null,
                    image: 'https://images.unsplash.com/photo-1541807084-5c52b6b3adef?w=400&h=400&fit=crop',
                    category: 'electronics',
                    rating: 4.9,
                    review_count: 567,
                    badge: null,
                    featured: true,
                    short_description: 'Ultra-portable laptop with M3 chip'
                },
                {
                    id: 5,
                    name: 'Sony WH-1000XM5',
                    price: 399.99,
                    sale_price: 349.99,
                    image: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=400&fit=crop',
                    category: 'electronics',
                    rating: 4.6,
                    review_count: 2134,
                    badge: 'Sale',
                    featured: true,
                    short_description: 'Premium noise-canceling wireless headphones'
                },
                {
                    id: 6,
                    name: 'Dyson V15 Detect',
                    price: 749.99,
                    sale_price: 649.99,
                    image: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=400&fit=crop',
                    category: 'home-garden',
                    rating: 4.7,
                    review_count: 1234,
                    badge: 'Sale',
                    featured: true,
                    short_description: 'High-tech cordless vacuum with laser detection'
                },
                {
                    id: 7,
                    name: 'KitchenAid Stand Mixer',
                    price: 429.99,
                    sale_price: null,
                    image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=400&fit=crop',
                    category: 'home-garden',
                    rating: 4.8,
                    review_count: 2156,
                    badge: null,
                    featured: true,
                    short_description: 'Heavy-duty stand mixer for serious baking'
                },
                {
                    id: 8,
                    name: 'Levi\'s 501 Original',
                    price: 89.50,
                    sale_price: 69.99,
                    image: 'https://images.unsplash.com/photo-1542272454315-7ad85ba6f23d?w=400&h=400&fit=crop',
                    category: 'clothing',
                    rating: 4.5,
                    review_count: 1567,
                    badge: 'Sale',
                    featured: true,
                    short_description: 'Classic straight-fit denim jeans'
                }
            ];
            applyFilters();
        };
        
        const applyFilters = () => {
            let filtered = [...products.value];
            
            // Category filter
            if (activeFilter.value && activeFilter.value !== 'all') {
                filtered = filtered.filter(product => product.category === activeFilter.value);
            }
            
            // Price range filter
            filtered = filtered.filter(product => {
                const price = product.sale_price || product.price;
                return price >= priceRange.value[0] && price <= priceRange.value[1];
            });
            
            // Sort products
            filtered.sort((a, b) => {
                switch (sortBy.value) {
                    case 'price-asc':
                        return (a.sale_price || a.price) - (b.sale_price || b.price);
                    case 'price-desc':
                        return (b.sale_price || b.price) - (a.sale_price || a.price);
                    case 'rating':
                        return (b.rating || 0) - (a.rating || 0);
                    case 'newest':
                        return b.id - a.id;
                    case 'popular':
                        return (b.review_count || 0) - (a.review_count || 0);
                    default: // featured
                        return 0;
                }
            });
            
            filteredProducts.value = filtered;
        };
        
        const addToCart = async (product) => {
            if (addingToCart.value[product.id]) return;
            
            addingToCart.value[product.id] = true;
            
            try {
                // Track analytics
                if (typeof gtag !== 'undefined') {
                    gtag('event', 'add_to_cart', {
                        event_category: 'ecommerce',
                        event_label: product.name,
                        value: product.sale_price || product.price,
                        currency: 'USD'
                    });
                }
                
                // Use centralized cart store
                if (window.YourStoreCartStore) {
                    console.log(`Adding product ${product.id} to cart via Cart Store...`);
                    
                    await window.YourStoreCartStore.dispatch('ADD_TO_CART', {
                        productId: product.id,
                        quantity: 1
                    });
                    
                    console.log('✅ Product added to cart via Cart Store');
                    
                    // Show success notification
                    if (window.YourStoreCommerce) {
                        window.YourStoreCommerce.showNotification(
                            `${product.name} added to cart!`, 
                            'success'
                        );
                    }
                    
                    // Update cart count from store state
                    const cartState = window.YourStoreCartStore.getState();
                    const cartCount = document.querySelector('.cart-count');
                    if (cartCount) {
                        cartCount.textContent = cartState.cart.contents?.itemCount || 0;
                    }
                    
                    // Open cart drawer if available
                    if (window.YourStoreCart && window.YourStoreCart.instance) {
                        window.YourStoreCart.open();
                    }
                    
                    return;
                }
                
                // Fallback: Try GraphQL directly if cart store not available
                if (window.graphqlClient && window.GRAPHQL_MUTATIONS) {
                    console.log(`Adding product ${product.id} to cart via GraphQL fallback...`);
                    
                    const result = await window.graphqlClient.mutate(
                        window.GRAPHQL_MUTATIONS.ADD_TO_CART,
                        {
                            input: {
                                productId: product.id,
                                quantity: 1
                            }
                        }
                    );
                    
                    if (result.data && result.data.addToCart) {
                        console.log('✅ Product added to cart via GraphQL fallback');
                        
                        // Show success notification
                        if (window.YourStoreCommerce) {
                            window.YourStoreCommerce.showNotification(
                                `${product.name} added to cart!`, 
                                'success'
                            );
                        }
                        
                        // Update cart count from GraphQL response
                        const cartData = result.data.addToCart.cart;
                        if (cartData && cartData.contents) {
                            const cartCount = document.querySelector('.cart-count');
                            if (cartCount) {
                                cartCount.textContent = cartData.contents.itemCount || 0;
                            }
                        }
                        
                        return;
                    }
                }
                
                // Last resort: simulate for UI feedback
                console.log('All cart systems failed, simulating for UI...');
                await new Promise(resolve => setTimeout(resolve, 800));
                
                // Show success notification
                if (window.YourStoreCommerce) {
                    window.YourStoreCommerce.showNotification(
                        `${product.name} added to cart!`, 
                        'success'
                    );
                }
                
                // Update cart count simulation
                const cartCount = document.querySelector('.cart-count');
                if (cartCount) {
                    const current = parseInt(cartCount.textContent) || 0;
                    cartCount.textContent = current + 1;
                }
                
            } catch (error) {
                console.error('Add to cart error:', error);
                if (window.YourStoreCommerce) {
                    window.YourStoreCommerce.showNotification(
                        'Failed to add product to cart. Please try again.', 
                        'error'
                    );
                }
            } finally {
                delete addingToCart.value[product.id];
            }
        };
        
        const toggleWishlist = (product) => {
            if (wishlist.value.has(product.id)) {
                wishlist.value.delete(product.id);
                if (window.YourStoreCommerce) {
                    window.YourStoreCommerce.showNotification(
                        'Removed from wishlist', 
                        'info'
                    );
                }
            } else {
                wishlist.value.add(product.id);
                if (window.YourStoreCommerce) {
                    window.YourStoreCommerce.showNotification(
                        'Added to wishlist', 
                        'success'
                    );
                }
            }
        };
        
        const openQuickView = (product) => {
            quickViewProduct.value = product;
            showQuickView.value = true;
            document.body.style.overflow = 'hidden';
        };
        
        const closeQuickView = () => {
            showQuickView.value = false;
            quickViewProduct.value = null;
            document.body.style.overflow = '';
        };
        
        const formatPrice = (price) => {
            return new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD'
            }).format(price);
        };
        
        const renderStars = (rating) => {
            const stars = [];
            const fullStars = Math.floor(rating);
            const hasHalfStar = rating % 1 !== 0;
            
            for (let i = 0; i < fullStars; i++) {
                stars.push('★');
            }
            if (hasHalfStar) stars.push('☆');
            while (stars.length < 5) stars.push('☆');
            
            return stars.join('');
        };
        
        const calculateDiscount = (originalPrice, salePrice) => {
            if (!salePrice) return 0;
            return Math.round((originalPrice - salePrice) / originalPrice * 100);
        };
        
        const setupIntersectionObserver = () => {
            intersectionObserver.value = new IntersectionObserver(
                (entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            setTimeout(() => {
                                isVisible.value = true;
                            }, 100);
                        }
                    });
                },
                { threshold: 0.1 }
            );
            
            if (sectionRef.value) {
                intersectionObserver.value.observe(sectionRef.value);
            }
        };
        
        // Watchers
        const watchFilters = () => {
            applyFilters();
        };
        
        // Load products from GraphQL with intelligent fallbacks
        const loadProducts = async () => {
            loading.value = true;
            error.value = null;

            try {
                // Try GraphQL first if available
                if (window.graphqlClient && window.GRAPHQL_QUERIES) {
                    console.log('Loading products from GraphQL...');
                    
                    const result = await window.graphqlClient.query(
                        window.GRAPHQL_QUERIES.GET_PRODUCTS, 
                        { first: props.limit || 8 }
                    );
                    
                    if (result.data && result.data.products && result.data.products.edges.length > 0) {
                        // Transform GraphQL data to component format
                        const graphqlProducts = result.data.products.edges.map(edge => {
                            const product = edge.node;
                            
                            return {
                                id: product.databaseId,
                                name: product.name,
                                price: parseFloat(product.price?.replace(/[^\d.-]/g, '') || 0),
                                sale_price: product.salePrice ? parseFloat(product.salePrice.replace(/[^\d.-]/g, '')) : null,
                                regular_price: product.regularPrice ? parseFloat(product.regularPrice.replace(/[^\d.-]/g, '')) : null,
                                image: product.image?.sourceUrl || 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400&h=400&fit=crop',
                                category: product.productCategories?.nodes?.[0]?.name?.toLowerCase() || 'uncategorized',
                                rating: product.averageRating || 4.0,
                                review_count: product.reviewCount || 0,
                                badge: product.onSale ? 'Sale' : (product.featured ? 'Featured' : null),
                                featured: product.featured || false,
                                short_description: product.shortDescription?.replace(/<[^>]*>/g, '') || product.description?.replace(/<[^>]*>/g, '').substring(0, 100) + '...' || 'No description available',
                                slug: product.slug,
                                stock_status: product.stockStatus?.toLowerCase() || 'instock',
                                stock_quantity: product.stockQuantity || null,
                                gallery_images: product.galleryImages?.nodes?.map(img => img.sourceUrl) || [],
                                attributes: product.attributes?.nodes || [],
                                tags: product.productTags?.nodes?.map(tag => tag.name) || []
                            };
                        });
                        
                        products.value = graphqlProducts;
                        console.log(`✅ Loaded ${graphqlProducts.length} products from GraphQL`);
                        loading.value = false;
                        return;
                    }
                }
                
                // Fallback to WordPress REST API
                console.log('GraphQL unavailable, trying WordPress REST API...');
                
                const response = await fetch('/wp-json/wc/v3/products?featured=true&per_page=' + (props.limit || 8), {
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                
                if (response.ok) {
                    const wooProducts = await response.json();
                    
                    if (wooProducts && wooProducts.length > 0) {
                        // Transform WooCommerce REST API data to component format
                        const restProducts = wooProducts.map(product => ({
                            id: product.id,
                            name: product.name,
                            price: parseFloat(product.price || product.regular_price || 0),
                            sale_price: product.sale_price ? parseFloat(product.sale_price) : null,
                            regular_price: product.regular_price ? parseFloat(product.regular_price) : null,
                            image: product.images?.[0]?.src || 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400&h=400&fit=crop',
                            category: product.categories?.[0]?.name?.toLowerCase() || 'uncategorized',
                            rating: product.average_rating || 4.0,
                            review_count: product.rating_count || 0,
                            badge: product.on_sale ? 'Sale' : (product.featured ? 'Featured' : null),
                            featured: product.featured || false,
                            short_description: product.short_description?.replace(/<[^>]*>/g, '') || product.description?.replace(/<[^>]*>/g, '').substring(0, 100) + '...' || 'No description available',
                            slug: product.slug,
                            stock_status: product.stock_status || 'instock',
                            stock_quantity: product.stock_quantity || null,
                            gallery_images: product.images?.slice(1)?.map(img => img.src) || [],
                            attributes: product.attributes || [],
                            tags: product.tags?.map(tag => tag.name) || []
                        }));
                        
                        products.value = restProducts;
                        console.log(`✅ Loaded ${restProducts.length} products from WooCommerce REST API`);
                        loading.value = false;
                        return;
                    }
                }
                
                // Last resort: fallback to mock data
                console.log('All APIs failed, using fallback data...');
                loadFallbackData();
                
            } catch (err) {
                console.error('Error loading products:', err);
                error.value = 'Failed to load products. Please try again later.';
                
                // Use fallback data on error
                loadFallbackData();
            }
            
            loading.value = false;
        };

        // Lifecycle
        onMounted(async () => {
            await nextTick();
            setupIntersectionObserver();
            
            // Load products from GraphQL first, fallback to mock data
            await loadProducts();
        });
        
        onUnmounted(() => {
            if (intersectionObserver.value) {
                intersectionObserver.value.disconnect();
            }
        });
        
        return {
            products,
            filteredProducts,
            currentProducts,
            loading,
            error,
            isVisible,
            sectionRef,
            activeFilter,
            sortBy,
            priceRange,
            addingToCart,
            wishlist,
            quickViewProduct,
            showQuickView,
            gridClasses,
            availableCategories,
            productStats,
            addToCart,
            toggleWishlist,
            openQuickView,
            closeQuickView,
            formatPrice,
            renderStars,
            calculateDiscount,
            watchFilters
        };
    },
    
    template: `
        <section ref="sectionRef" class="yourstore-featured-products py-16 bg-white">
            <div class="container mx-auto px-4">
                
                <!-- Section Header -->
                <div class="text-center mb-12">
                    <div 
                        class="inline-flex items-center px-3 py-1 rounded-full bg-primary/10 text-primary text-sm font-medium mb-4"
                        :class="{
                            'opacity-0 translate-y-4': !isVisible,
                            'opacity-100 translate-y-0': isVisible,
                            'transition-all duration-700 ease-out': true
                        }"
                    >
                        <i class="fas fa-star mr-2"></i>
                        Featured Collection
                    </div>
                    
                    <h2 
                        class="text-3xl sm:text-4xl lg:text-5xl font-bold text-secondary-900 mb-4"
                        :class="{
                            'opacity-0 translate-y-8': !isVisible,
                            'opacity-100 translate-y-0': isVisible,
                            'transition-all duration-1000 ease-out delay-200': true
                        }"
                        v-html="title"
                    ></h2>
                    
                    <p 
                        class="text-xl text-secondary-600 max-w-2xl mx-auto mb-8"
                        :class="{
                            'opacity-0 translate-y-8': !isVisible,
                            'opacity-100 translate-y-0': isVisible,
                            'transition-all duration-1000 ease-out delay-400': true
                        }"
                        v-html="subtitle"
                    ></p>
                    
                    <!-- Product Stats -->
                    <div 
                        v-if="!loading && filteredProducts.length > 0"
                        class="flex flex-wrap justify-center items-center gap-6 text-sm text-secondary-500 mb-8"
                        :class="{
                            'opacity-0 translate-y-4': !isVisible,
                            'opacity-100 translate-y-0': isVisible,
                            'transition-all duration-1000 ease-out delay-600': true
                        }"
                    >
                        <span class="flex items-center">
                            <i class="fas fa-box mr-2 text-primary"></i>
                            {{ productStats.total }} Products
                        </span>
                        <span class="flex items-center">
                            <i class="fas fa-tag mr-2 text-accent"></i>
                            {{ productStats.onSale }} On Sale
                        </span>
                        <span class="flex items-center">
                            <i class="fas fa-star mr-2 text-yellow-500"></i>
                            {{ productStats.avgRating.toFixed(1) }} Avg Rating
                        </span>
                    </div>
                </div>
                
                <!-- Filters (if enabled) -->
                <div 
                    v-if="showFilters && !loading" 
                    class="mb-8"
                    :class="{
                        'opacity-0 translate-y-4': !isVisible,
                        'opacity-100 translate-y-0': isVisible,
                        'transition-all duration-1000 ease-out delay-700': true
                    }"
                >
                    <div class="flex flex-wrap justify-center items-center gap-4 mb-6">
                        <!-- Category Filter -->
                        <div class="flex items-center gap-2">
                            <label class="text-sm font-medium text-secondary-700">Category:</label>
                            <select 
                                v-model="activeFilter" 
                                @change="watchFilters"
                                class="input text-sm border-secondary-300 focus:border-primary focus:ring-primary"
                            >
                                <option value="all">All Categories</option>
                                <option v-for="category in availableCategories" :key="category" :value="category">
                                    {{ category.charAt(0).toUpperCase() + category.slice(1).replace('-', ' ') }}
                                </option>
                            </select>
                        </div>
                        
                        <!-- Sort Filter -->
                        <div class="flex items-center gap-2">
                            <label class="text-sm font-medium text-secondary-700">Sort by:</label>
                            <select 
                                v-model="sortBy" 
                                @change="watchFilters"
                                class="input text-sm border-secondary-300 focus:border-primary focus:ring-primary"
                            >
                                <option value="featured">Featured</option>
                                <option value="newest">Newest</option>
                                <option value="price-asc">Price: Low to High</option>
                                <option value="price-desc">Price: High to Low</option>
                                <option value="rating">Highest Rated</option>
                                <option value="popular">Most Popular</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <!-- Loading State -->
                <div v-if="loading" class="grid gap-6" :class="gridClasses">
                    <div v-for="n in limit" :key="'skeleton-' + n" class="product-card card overflow-hidden">
                        <div class="skeleton h-64 mb-4"></div>
                        <div class="p-4">
                            <div class="skeleton h-4 mb-2"></div>
                            <div class="skeleton h-4 w-3/4 mb-2"></div>
                            <div class="skeleton h-6 w-1/2"></div>
                        </div>
                    </div>
                </div>
                
                <!-- Error State -->
                <div v-else-if="error" class="text-center py-12">
                    <div class="text-red-600 text-lg mb-4">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        {{ error }}
                    </div>
                    <button @click="loadProducts" class="btn-primary">
                        <i class="fas fa-redo mr-2"></i>
                        Try Again
                    </button>
                </div>
                
                <!-- Products Grid -->
                <div 
                    v-else-if="currentProducts.length > 0" 
                    class="grid gap-6 mb-12" 
                    :class="gridClasses"
                >
                    <div
                        v-for="(product, index) in currentProducts"
                        :key="product.id"
                        class="product-card card group relative overflow-hidden hover:shadow-lg transition-all duration-300"
                        :class="{
                            'opacity-0 translate-y-8 scale-95': !isVisible,
                            'opacity-100 translate-y-0 scale-100': isVisible,
                            'transition-all duration-700 ease-out': true
                        }"
                        :style="{ transitionDelay: (index * 100) + 'ms' }"
                    >
                        <!-- Product Image -->
                        <div class="relative overflow-hidden bg-gray-100">
                            <img
                                :src="product.image"
                                :alt="product.name"
                                class="w-full h-64 object-cover group-hover:scale-110 transition-transform duration-500"
                                loading="lazy"
                            />
                            
                            <!-- Badges -->
                            <div class="absolute top-3 left-3 flex flex-col gap-2">
                                <span v-if="product.badge" class="badge-sale">
                                    {{ product.badge }}
                                </span>
                                <span v-if="product.sale_price" class="bg-red-500 text-white text-xs font-bold px-2 py-1 rounded">
                                    -{{ calculateDiscount(product.price, product.sale_price) }}%
                                </span>
                            </div>
                            
                            <!-- Quick Actions -->
                            <div class="absolute top-3 right-3 flex flex-col gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                                <button
                                    @click="toggleWishlist(product)"
                                    class="w-8 h-8 rounded-full bg-white/90 backdrop-blur-sm flex items-center justify-center hover:bg-white transition-colors"
                                    :class="{ 'text-red-500': wishlist.has(product.id), 'text-secondary-600': !wishlist.has(product.id) }"
                                    :title="wishlist.has(product.id) ? 'Remove from wishlist' : 'Add to wishlist'"
                                >
                                    <i :class="wishlist.has(product.id) ? 'fas fa-heart' : 'far fa-heart'"></i>
                                </button>
                                
                                <button
                                    @click="openQuickView(product)"
                                    class="w-8 h-8 rounded-full bg-white/90 backdrop-blur-sm flex items-center justify-center hover:bg-white transition-colors text-secondary-600"
                                    title="Quick view"
                                >
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            
                            <!-- Quick Add to Cart -->
                            <div class="absolute bottom-3 left-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                                <button
                                    @click="addToCart(product)"
                                    :disabled="addingToCart[product.id]"
                                    class="w-full btn-primary py-2 text-sm font-medium"
                                    :class="{ 'opacity-50 cursor-not-allowed': addingToCart[product.id] }"
                                >
                                    <span v-if="addingToCart[product.id]" class="flex items-center justify-center">
                                        <i class="fas fa-spinner fa-spin mr-2"></i>
                                        Adding...
                                    </span>
                                    <span v-else class="flex items-center justify-center">
                                        <i class="fas fa-shopping-cart mr-2"></i>
                                        Add to Cart
                                    </span>
                                </button>
                            </div>
                        </div>
                        
                        <!-- Product Info -->
                        <div class="p-4">
                            <div class="mb-2">
                                <span class="text-xs text-secondary-500 uppercase tracking-wide">
                                    {{ product.category?.replace('-', ' ') }}
                                </span>
                            </div>
                            
                            <h3 class="product-title text-lg font-semibold text-secondary-800 mb-2 line-clamp-2 group-hover:text-primary transition-colors duration-200">
                                {{ product.name }}
                            </h3>
                            
                            <p class="text-sm text-secondary-600 mb-3 line-clamp-2">
                                {{ product.short_description }}
                            </p>
                            
                            <!-- Rating -->
                            <div class="flex items-center mb-3">
                                <div class="text-yellow-400 mr-2 text-sm">
                                    {{ renderStars(product.rating) }}
                                </div>
                                <span class="text-xs text-secondary-500">
                                    ({{ product.review_count }})
                                </span>
                            </div>
                            
                            <!-- Price -->
                            <div class="flex items-center justify-between">
                                <div class="price-section">
                                    <span v-if="product.sale_price" class="price-tag text-lg font-bold">
                                        {{ formatPrice(product.sale_price) }}
                                    </span>
                                    <span 
                                        :class="product.sale_price ? 'text-sm text-secondary-400 line-through ml-2' : 'price-tag text-lg font-bold'"
                                    >
                                        {{ formatPrice(product.price) }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Empty State -->
                <div v-else-if="!loading" class="text-center py-12">
                    <div class="text-secondary-600 text-lg mb-4">
                        <i class="fas fa-search mr-2"></i>
                        No products found matching your criteria.
                    </div>
                    <button @click="activeFilter = 'all'; watchFilters()" class="btn-outline">
                        Clear Filters
                    </button>
                </div>
                
                <!-- View All Button -->
                <div 
                    v-if="showViewAll && !loading && currentProducts.length > 0" 
                    class="text-center mt-12"
                    :class="{
                        'opacity-0 translate-y-4': !isVisible,
                        'opacity-100 translate-y-0': isVisible,
                        'transition-all duration-1000 ease-out delay-1000': true
                    }"
                >
                    <a :href="viewAllUrl" class="btn-outline btn-lg group">
                        View All Products
                        <i class="fas fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform duration-200"></i>
                    </a>
                </div>
                
            </div>
            
            <!-- Quick View Modal -->
            <div 
                v-if="showQuickView && quickViewProduct"
                class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4"
                @click.self="closeQuickView"
            >
                <div class="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
                    <!-- Modal Header -->
                    <div class="flex items-center justify-between p-6 border-b">
                        <h3 class="text-xl font-semibold text-secondary-900">Quick View</h3>
                        <button @click="closeQuickView" class="text-secondary-400 hover:text-secondary-600">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                    
                    <!-- Modal Content -->
                    <div class="p-6 grid md:grid-cols-2 gap-8">
                        <!-- Product Image -->
                        <div>
                            <img 
                                :src="quickViewProduct.image" 
                                :alt="quickViewProduct.name"
                                class="w-full h-96 object-cover rounded-lg"
                            />
                        </div>
                        
                        <!-- Product Details -->
                        <div>
                            <div class="mb-2">
                                <span class="text-sm text-secondary-500 uppercase tracking-wide">
                                    {{ quickViewProduct.category?.replace('-', ' ') }}
                                </span>
                            </div>
                            
                            <h2 class="text-2xl font-bold text-secondary-900 mb-4">
                                {{ quickViewProduct.name }}
                            </h2>
                            
                            <p class="text-secondary-600 mb-4">
                                {{ quickViewProduct.short_description }}
                            </p>
                            
                            <!-- Rating -->
                            <div class="flex items-center mb-4">
                                <div class="text-yellow-400 mr-2">
                                    {{ renderStars(quickViewProduct.rating) }}
                                </div>
                                <span class="text-sm text-secondary-500">
                                    ({{ quickViewProduct.review_count }} reviews)
                                </span>
                            </div>
                            
                            <!-- Price -->
                            <div class="mb-6">
                                <span v-if="quickViewProduct.sale_price" class="price-tag text-2xl font-bold">
                                    {{ formatPrice(quickViewProduct.sale_price) }}
                                </span>
                                <span 
                                    :class="quickViewProduct.sale_price ? 'text-lg text-secondary-400 line-through ml-2' : 'price-tag text-2xl font-bold'"
                                >
                                    {{ formatPrice(quickViewProduct.price) }}
                                </span>
                                
                                <div v-if="quickViewProduct.sale_price" class="text-sm text-green-600 mt-1">
                                    You save {{ formatPrice(quickViewProduct.price - quickViewProduct.sale_price) }}
                                    ({{ calculateDiscount(quickViewProduct.price, quickViewProduct.sale_price) }}% off)
                                </div>
                            </div>
                            
                            <!-- Actions -->
                            <div class="flex gap-3">
                                <button
                                    @click="addToCart(quickViewProduct); closeQuickView()"
                                    class="flex-1 btn-primary py-3"
                                >
                                    <i class="fas fa-shopping-cart mr-2"></i>
                                    Add to Cart
                                </button>
                                <button
                                    @click="toggleWishlist(quickViewProduct)"
                                    class="btn-outline px-4 py-3"
                                    :class="{ 'text-red-500 border-red-500': wishlist.has(quickViewProduct.id) }"
                                >
                                    <i :class="wishlist.has(quickViewProduct.id) ? 'fas fa-heart' : 'far fa-heart'"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
        </section>
    `
};

        // Make component globally accessible
        console.log('YourStore: FeaturedProducts component definition:', FeaturedProducts);
        console.log('YourStore: Setting window.FeaturedProducts...');
        window.FeaturedProducts = FeaturedProducts;
        console.log('YourStore: window.FeaturedProducts is now set to:', window.FeaturedProducts);

        // Auto-mount component if container exists (legacy support)
        document.addEventListener('DOMContentLoaded', () => {
            const containers = document.querySelectorAll('.yourstore-featured-products');
            containers.forEach(container => {
                // Skip if already mounted by app.js or has Vue app
                if (container.__vue_app || container.querySelector('[data-vue-app]')) {
                    return;
                }
                
                let props = {};
                
                // Handle hybrid component format (data-props)
                if (container.dataset.props) {
                    try {
                        props = JSON.parse(container.dataset.props);
                    } catch (e) {
                        console.warn('YourStore: Invalid JSON in data-props:', e);
                    }
                } 
                // Handle legacy format (individual data attributes)
                else {
                    if (container.dataset.title) props.title = container.dataset.title;
                    if (container.dataset.subtitle) props.subtitle = container.dataset.subtitle;
                    if (container.dataset.limit) props.limit = parseInt(container.dataset.limit);
                    if (container.dataset.columns) props.columns = parseInt(container.dataset.columns);
                    if (container.dataset.showViewAll) props.showViewAll = container.dataset.showViewAll === 'true';
                    if (container.dataset.viewAllUrl) props.viewAllUrl = container.dataset.viewAllUrl;
                    if (container.dataset.categoryFilter) props.categoryFilter = container.dataset.categoryFilter;
                    if (container.dataset.showFilters) props.showFilters = container.dataset.showFilters === 'true';
                }
                
                const app = Vue.createApp(FeaturedProducts, props);
                container.__vue_app = app.mount(container);
            });
        });

        // Export for module use
        if (typeof module !== 'undefined' && module.exports) {
            module.exports = FeaturedProducts;
        }
    }
    
    // Initialize the component
    initFeaturedProducts();
    
})();
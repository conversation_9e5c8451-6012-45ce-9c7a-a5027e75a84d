/**
 * Site Footer Vue Component
 * Advanced responsive footer with multiple sections and interactive elements
 */

(function() {
    'use strict';
    
    // Wait for Vue to be available
    function initSiteFooter() {
        if (typeof Vue === 'undefined') {
            console.warn('YourStore SiteFooter: Vue.js not loaded yet, retrying...');
            setTimeout(initSiteFooter, 100);
            return;
        }
        
        console.log('YourStore SiteFooter: Vue.js detected, initializing component...');
        
        // Now safely destructure Vue methods
        const { ref, computed, reactive, onMounted, onUnmounted } = Vue;

// Site Footer Component
const SiteFooter = {
    name: 'SiteFooter',
    
    props: {
        logo: {
            type: String,
            default: ''
        },
        tagline: {
            type: String,
            default: 'Your trusted partner for quality products and exceptional service.'
        },
        showNewsletter: {
            type: Boolean,
            default: true
        },
        newsletterTitle: {
            type: String,
            default: 'Stay Updated'
        },
        newsletterSubtitle: {
            type: String,
            default: 'Get the latest deals and updates'
        },
        showSocial: {
            type: <PERSON>olean,
            default: true
        },
        socialLinks: {
            type: Array,
            default: () => []
        },
        showPaymentIcons: {
            type: Boolean,
            default: true
        },
        columns: {
            type: Array,
            default: () => []
        },
        copyrightText: {
            type: String,
            default: ''
        },
        showBackToTop: {
            type: Boolean,
            default: true
        },
        theme: {
            type: String,
            default: 'dark' // dark, light
        },
        layout: {
            type: String,
            default: 'standard' // standard, minimal, extended
        }
    },
    
    setup(props) {
        // Component state
        const newsletterForm = reactive({
            email: '',
            loading: false,
            success: false,
            error: null
        });
        
        const showBackToTopButton = ref(false);
        const footerRef = ref(null);
        const currentYear = new Date().getFullYear();
        
        // Default social links if none provided
        const defaultSocialLinks = [
            { name: 'Facebook', icon: 'fab fa-facebook-f', url: '#', color: '#1877f2' },
            { name: 'Twitter', icon: 'fab fa-twitter', url: '#', color: '#1da1f2' },
            { name: 'Instagram', icon: 'fab fa-instagram', url: '#', color: '#e4405f' },
            { name: 'LinkedIn', icon: 'fab fa-linkedin-in', url: '#', color: '#0077b5' },
            { name: 'YouTube', icon: 'fab fa-youtube', url: '#', color: '#ff0000' }
        ];
        
        // Default footer columns if none provided
        const defaultColumns = [
            {
                title: 'Shop',
                links: [
                    { text: 'All Products', url: '/shop' },
                    { text: 'Categories', url: '/categories' },
                    { text: 'Featured', url: '/featured' },
                    { text: 'Sale Items', url: '/sale' },
                    { text: 'New Arrivals', url: '/new' }
                ]
            },
            {
                title: 'Customer Service',
                links: [
                    { text: 'Contact Us', url: '/contact' },
                    { text: 'FAQ', url: '/faq' },
                    { text: 'Shipping Info', url: '/shipping' },
                    { text: 'Returns', url: '/returns' },
                    { text: 'Size Guide', url: '/size-guide' }
                ]
            },
            {
                title: 'Company',
                links: [
                    { text: 'About Us', url: '/about' },
                    { text: 'Careers', url: '/careers' },
                    { text: 'Press', url: '/press' },
                    { text: 'Blog', url: '/blog' },
                    { text: 'Sustainability', url: '/sustainability' }
                ]
            },
            {
                title: 'Legal',
                links: [
                    { text: 'Privacy Policy', url: '/privacy-policy' },
                    { text: 'Terms of Service', url: '/terms' },
                    { text: 'Cookie Policy', url: '/cookies' },
                    { text: 'Accessibility', url: '/accessibility' },
                    { text: 'Sitemap', url: '/sitemap' }
                ]
            }
        ];
        
        // Payment method icons
        const paymentMethods = [
            { name: 'Visa', icon: 'fab fa-cc-visa' },
            { name: 'MasterCard', icon: 'fab fa-cc-mastercard' },
            { name: 'American Express', icon: 'fab fa-cc-amex' },
            { name: 'Discover', icon: 'fab fa-cc-discover' },
            { name: 'PayPal', icon: 'fab fa-cc-paypal' },
            { name: 'Apple Pay', icon: 'fab fa-cc-apple-pay' },
            { name: 'Google Pay', icon: 'fab fa-google-pay' },
            { name: 'Stripe', icon: 'fab fa-cc-stripe' }
        ];
        
        // Computed properties
        const footerColumns = computed(() => {
            return props.columns.length > 0 ? props.columns : defaultColumns;
        });
        
        const socialLinksData = computed(() => {
            return props.socialLinks.length > 0 ? props.socialLinks : defaultSocialLinks;
        });
        
        const footerClasses = computed(() => {
            const baseClasses = 'site-footer';
            const themeClasses = {
                dark: 'bg-secondary-900 text-secondary-300',
                light: 'bg-gray-100 text-secondary-700'
            };
            const layoutClasses = {
                standard: 'py-16',
                minimal: 'py-12',
                extended: 'py-20'
            };
            
            return `${baseClasses} ${themeClasses[props.theme] || themeClasses.dark} ${layoutClasses[props.layout] || layoutClasses.standard}`;
        });
        
        const copyrightDisplay = computed(() => {
            if (props.copyrightText) {
                return props.copyrightText;
            }
            
            const siteName = window.yourStoreCommerce?.siteName || 'YourStore';
            return `© ${currentYear} ${siteName}. All rights reserved.`;
        });
        
        // Methods
        const handleNewsletterSubmit = async () => {
            if (!newsletterForm.email || newsletterForm.loading) return;
            
            newsletterForm.loading = true;
            newsletterForm.error = null;
            
            try {
                // Simulate API call (replace with actual newsletter signup)
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                newsletterForm.success = true;
                newsletterForm.email = '';
                
                // Track subscription
                if (typeof gtag !== 'undefined') {
                    gtag('event', 'newsletter_signup', {
                        event_category: 'engagement',
                        event_label: 'footer_newsletter'
                    });
                }
                
                setTimeout(() => {
                    newsletterForm.success = false;
                }, 3000);
                
            } catch (error) {
                newsletterForm.error = 'Failed to subscribe. Please try again.';
                setTimeout(() => {
                    newsletterForm.error = null;
                }, 5000);
            } finally {
                newsletterForm.loading = false;
            }
        };
        
        const scrollToTop = () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
            
            // Track scroll to top
            if (typeof gtag !== 'undefined') {
                gtag('event', 'scroll_to_top', {
                    event_category: 'navigation',
                    event_label: 'footer_button'
                });
            }
        };
        
        const handleScroll = () => {
            showBackToTopButton.value = window.scrollY > 500;
        };
        
        const handleSocialClick = (social) => {
            // Track social media click
            if (typeof gtag !== 'undefined') {
                gtag('event', 'social_link_click', {
                    event_category: 'social',
                    event_label: social.name.toLowerCase(),
                    social_platform: social.name
                });
            }
        };
        
        const handleLinkClick = (link, columnTitle) => {
            // Track footer link clicks
            if (typeof gtag !== 'undefined') {
                gtag('event', 'footer_link_click', {
                    event_category: 'navigation',
                    event_label: link.text,
                    link_section: columnTitle
                });
            }
        };
        
        // Lifecycle
        onMounted(() => {
            if (props.showBackToTop) {
                window.addEventListener('scroll', handleScroll, { passive: true });
                handleScroll(); // Check initial scroll position
            }
        });
        
        onUnmounted(() => {
            if (props.showBackToTop) {
                window.removeEventListener('scroll', handleScroll);
            }
        });
        
        return {
            newsletterForm,
            showBackToTopButton,
            footerRef,
            currentYear,
            footerColumns,
            socialLinksData,
            paymentMethods,
            footerClasses,
            copyrightDisplay,
            handleNewsletterSubmit,
            scrollToTop,
            handleSocialClick,
            handleLinkClick
        };
    },
    
    template: `
        <footer ref="footerRef" :class="footerClasses">
            <div class="container mx-auto px-4">
                
                <!-- Main Footer Content -->
                <div class="grid grid-cols-1 lg:grid-cols-12 gap-8 mb-12">
                    
                    <!-- Company Info Column -->
                    <div class="lg:col-span-4">
                        <div class="mb-6">
                            <!-- Logo -->
                            <div class="mb-4">
                                <img v-if="logo" :src="logo" alt="Logo" class="h-10 w-auto" />
                                <div v-else class="text-2xl font-bold" :class="theme === 'dark' ? 'text-white' : 'text-secondary-900'">
                                    YourStore
                                </div>
                            </div>
                            
                            <!-- Tagline -->
                            <p class="text-sm leading-relaxed mb-6 max-w-sm">
                                {{ tagline }}
                            </p>
                            
                            <!-- Social Links -->
                            <div v-if="showSocial" class="flex space-x-4 mb-6">
                                <a
                                    v-for="social in socialLinksData.slice(0, 5)"
                                    :key="social.name"
                                    :href="social.url"
                                    @click="handleSocialClick(social)"
                                    class="w-10 h-10 rounded-full flex items-center justify-center transition-all duration-200 hover:scale-110"
                                    :class="theme === 'dark' ? 'bg-secondary-800 hover:bg-secondary-700 text-secondary-400 hover:text-white' : 'bg-white hover:bg-gray-200 text-secondary-600 hover:text-secondary-900'"
                                    :title="social.name"
                                >
                                    <i :class="social.icon" class="text-sm"></i>
                                </a>
                            </div>
                            
                            <!-- Contact Info -->
                            <div class="text-sm space-y-2">
                                <div class="flex items-center">
                                    <i class="fas fa-envelope mr-3 text-primary w-4"></i>
                                    <a href="mailto:<EMAIL>" class="hover:text-primary transition-colors">
                                        <EMAIL>
                                    </a>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-phone mr-3 text-primary w-4"></i>
                                    <a href="tel:+1234567890" class="hover:text-primary transition-colors">
                                        (*************
                                    </a>
                                </div>
                                <div class="flex items-start">
                                    <i class="fas fa-map-marker-alt mr-3 text-primary w-4 mt-0.5"></i>
                                    <span>123 Commerce St<br>Business City, BC 12345</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Footer Columns -->
                    <div class="lg:col-span-6">
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
                            <div
                                v-for="column in footerColumns"
                                :key="column.title"
                                class="space-y-4"
                            >
                                <h3 class="font-semibold text-sm uppercase tracking-wider" :class="theme === 'dark' ? 'text-white' : 'text-secondary-900'">
                                    {{ column.title }}
                                </h3>
                                <ul class="space-y-2">
                                    <li v-for="link in column.links" :key="link.text">
                                        <a
                                            :href="link.url"
                                            @click="handleLinkClick(link, column.title)"
                                            class="text-sm hover:text-primary transition-colors duration-200 block py-1"
                                        >
                                            {{ link.text }}
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Newsletter Signup -->
                    <div v-if="showNewsletter" class="lg:col-span-2">
                        <div class="space-y-4">
                            <h3 class="font-semibold text-sm uppercase tracking-wider" :class="theme === 'dark' ? 'text-white' : 'text-secondary-900'">
                                {{ newsletterTitle }}
                            </h3>
                            
                            <p class="text-sm">
                                {{ newsletterSubtitle }}
                            </p>
                            
                            <!-- Success State -->
                            <div v-if="newsletterForm.success" class="text-center py-4">
                                <div class="inline-flex items-center justify-center w-12 h-12 bg-green-500 text-white rounded-full mb-3">
                                    <i class="fas fa-check"></i>
                                </div>
                                <p class="text-sm text-green-600 font-medium">
                                    Thanks for subscribing!
                                </p>
                            </div>
                            
                            <!-- Newsletter Form -->
                            <form v-else @submit.prevent="handleNewsletterSubmit" class="space-y-3">
                                <div class="relative">
                                    <input
                                        v-model="newsletterForm.email"
                                        type="email"
                                        placeholder="Your email address"
                                        class="w-full px-4 py-3 rounded-lg border text-sm transition-colors duration-200"
                                        :class="theme === 'dark' 
                                            ? 'bg-secondary-800 border-secondary-700 text-white placeholder-secondary-400 focus:border-primary' 
                                            : 'bg-white border-gray-300 text-secondary-700 placeholder-secondary-400 focus:border-primary'"
                                        required
                                        :disabled="newsletterForm.loading"
                                    />
                                </div>
                                
                                <button
                                    type="submit"
                                    :disabled="!newsletterForm.email || newsletterForm.loading"
                                    class="w-full bg-primary hover:bg-primary-600 text-white px-4 py-3 rounded-lg font-medium text-sm transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                    <span v-if="!newsletterForm.loading">Subscribe</span>
                                    <span v-else class="flex items-center justify-center">
                                        <i class="fas fa-spinner animate-spin mr-2"></i>
                                        Subscribing...
                                    </span>
                                </button>
                                
                                <!-- Error Message -->
                                <div v-if="newsletterForm.error" class="text-red-500 text-xs mt-2">
                                    {{ newsletterForm.error }}
                                </div>
                            </form>
                        </div>
                    </div>
                    
                </div>
                
                <!-- Bottom Bar -->
                <div class="border-t pt-8" :class="theme === 'dark' ? 'border-secondary-800' : 'border-gray-300'">
                    <div class="flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0">
                        
                        <!-- Copyright -->
                        <div class="text-sm">
                            {{ copyrightDisplay }}
                        </div>
                        
                        <!-- Payment Methods -->
                        <div v-if="showPaymentIcons" class="flex items-center space-x-3">
                            <span class="text-sm mr-4">We accept:</span>
                            <div class="flex space-x-2">
                                <div
                                    v-for="method in paymentMethods.slice(0, 6)"
                                    :key="method.name"
                                    class="w-8 h-8 rounded flex items-center justify-center text-lg"
                                    :class="theme === 'dark' ? 'bg-secondary-800 text-secondary-400' : 'bg-gray-200 text-secondary-600'"
                                    :title="method.name"
                                >
                                    <i :class="method.icon"></i>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Legal Links -->
                        <div class="flex space-x-6 text-sm">
                            <a href="/privacy-policy" class="hover:text-primary transition-colors">Privacy</a>
                            <a href="/terms" class="hover:text-primary transition-colors">Terms</a>
                            <a href="/sitemap" class="hover:text-primary transition-colors">Sitemap</a>
                        </div>
                        
                    </div>
                </div>
                
            </div>
            
            <!-- Back to Top Button -->
            <div
                v-if="showBackToTop && showBackToTopButton"
                @click="scrollToTop"
                class="fixed bottom-6 right-6 w-12 h-12 bg-primary hover:bg-primary-600 text-white rounded-full shadow-lg flex items-center justify-center cursor-pointer transition-all duration-300 hover:scale-110 z-50"
                title="Back to top"
            >
                <i class="fas fa-arrow-up"></i>
            </div>
            
        </footer>
    `
};

        // Make component globally accessible
        console.log('YourStore: SiteFooter component definition:', SiteFooter);
        console.log('YourStore: Setting window.SiteFooter...');
        window.SiteFooter = SiteFooter;
        console.log('YourStore: window.SiteFooter is now set to:', window.SiteFooter);

        // Auto-mount component if container exists (legacy support)
        document.addEventListener('DOMContentLoaded', () => {
            const containers = document.querySelectorAll('.yourstore-site-footer');
            containers.forEach(container => {
                // Skip if already mounted by app.js or has Vue app
                if (container.__vue_app || container.querySelector('[data-vue-app]')) {
                    return;
                }
                
                let props = {};
                
                // Handle hybrid component format (data-props)
                if (container.dataset.props) {
                    try {
                        props = JSON.parse(container.dataset.props);
                    } catch (e) {
                        console.warn('YourStore: Invalid JSON in data-props:', e);
                    }
                } 
                // Handle legacy format (individual data attributes)
                else {
                    if (container.dataset.logo) props.logo = container.dataset.logo;
                    if (container.dataset.tagline) props.tagline = container.dataset.tagline;
                    if (container.dataset.showNewsletter) props.showNewsletter = container.dataset.showNewsletter === 'true';
                    if (container.dataset.newsletterTitle) props.newsletterTitle = container.dataset.newsletterTitle;
                    if (container.dataset.newsletterSubtitle) props.newsletterSubtitle = container.dataset.newsletterSubtitle;
                    if (container.dataset.showSocial) props.showSocial = container.dataset.showSocial === 'true';
                    if (container.dataset.socialLinks) props.socialLinks = JSON.parse(container.dataset.socialLinks || '[]');
                    if (container.dataset.showPaymentIcons) props.showPaymentIcons = container.dataset.showPaymentIcons === 'true';
                    if (container.dataset.columns) props.columns = JSON.parse(container.dataset.columns || '[]');
                    if (container.dataset.copyrightText) props.copyrightText = container.dataset.copyrightText;
                    if (container.dataset.showBackToTop) props.showBackToTop = container.dataset.showBackToTop === 'true';
                    if (container.dataset.theme) props.theme = container.dataset.theme;
                    if (container.dataset.layout) props.layout = container.dataset.layout;
                }
                
                const app = Vue.createApp(SiteFooter, props);
                container.__vue_app = app.mount(container);
            });
        });

        // Export for module use
        if (typeof module !== 'undefined' && module.exports) {
            module.exports = SiteFooter;
        }
    }
    
    // Initialize the component
    initSiteFooter();
    
})();
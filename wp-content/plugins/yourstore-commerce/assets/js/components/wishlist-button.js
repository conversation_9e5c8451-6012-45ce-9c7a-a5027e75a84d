/**
 * Wishlist Button Component
 * Heart-shaped button for adding/removing products from wishlist
 */

(function() {
    'use strict';

    const { ref, computed, onMounted, onUnmounted, watch } = Vue;

// Wishlist Button Component
const WishlistButton = {
    name: 'WishlistButton',
    
    props: {
        product: {
            type: Object,
            required: true
        },
        size: {
            type: String,
            default: 'md', // sm, md, lg
            validator: value => ['sm', 'md', 'lg'].includes(value)
        },
        variant: {
            type: String,
            default: 'default', // default, filled, outline
            validator: value => ['default', 'filled', 'outline'].includes(value)
        },
        showLabel: {
            type: Boolean,
            default: false
        },
        showCount: {
            type: Boolean,
            default: false
        },
        animate: {
            type: Boolean,
            default: true
        },
        requireAuth: {
            type: Boolean,
            default: false
        }
    },
    
    emits: ['added', 'removed', 'toggled', 'auth-required'],
    
    setup(props, { emit }) {
        // Use wishlist store
        const {
            hasItem,
            toggleWishlist,
            addToWishlist,
            removeFromWishlist,
            itemCount,
            isLoading
        } = window.useWishlistStore();
        
        // Local state
        const isAnimating = ref(false);
        const showTooltip = ref(false);
        const tooltipTimeout = ref(null);
        
        // Computed properties
        const isInWishlist = computed(() => {
            if (!props.product || !props.product.id) return false;
            return hasItem(props.product.id);
        });
        
        const buttonClasses = computed(() => {
            const baseClasses = [
                'wishlist-button',
                'inline-flex',
                'items-center',
                'justify-center',
                'transition-all',
                'duration-200',
                'relative',
                'group'
            ];
            
            // Size classes
            const sizeClasses = {
                sm: ['w-8', 'h-8', 'text-sm'],
                md: ['w-10', 'h-10', 'text-base'],
                lg: ['w-12', 'h-12', 'text-lg']
            };
            
            // Variant classes
            const variantClasses = {
                default: [
                    'bg-white',
                    'border',
                    'border-gray-300',
                    'rounded-full',
                    'hover:border-red-300',
                    'hover:bg-red-50',
                    isInWishlist.value ? 'border-red-500 bg-red-50 text-red-600' : 'text-gray-400'
                ],
                filled: [
                    'rounded-full',
                    'border-2',
                    isInWishlist.value ? 'bg-red-500 border-red-500 text-white' : 'bg-gray-200 border-gray-200 text-gray-400 hover:bg-red-100 hover:border-red-300'
                ],
                outline: [
                    'bg-transparent',
                    'border-2',
                    'rounded-full',
                    isInWishlist.value ? 'border-red-500 text-red-500' : 'border-gray-300 text-gray-400 hover:border-red-300 hover:text-red-500'
                ]
            };
            
            // Animation classes
            const animationClasses = props.animate ? [
                'transform',
                'hover:scale-110',
                isAnimating.value ? 'animate-pulse scale-125' : ''
            ] : [];
            
            return [
                ...baseClasses,
                ...sizeClasses[props.size],
                ...variantClasses[props.variant],
                ...animationClasses
            ].filter(Boolean);
        });
        
        const heartIconClasses = computed(() => {
            const baseClasses = ['transition-all', 'duration-200'];
            
            if (isInWishlist.value) {
                baseClasses.push('fas', 'fa-heart');
            } else {
                baseClasses.push('far', 'fa-heart');
            }
            
            if (props.animate && isAnimating.value) {
                baseClasses.push('animate-bounce');
            }
            
            return baseClasses;
        });
        
        const tooltipText = computed(() => {
            if (isInWishlist.value) {
                return `Remove from wishlist`;
            } else {
                return `Add to wishlist`;
            }
        });
        
        // Methods
        const handleClick = async (event) => {
            event.preventDefault();
            event.stopPropagation();
            
            // Check authentication if required
            if (props.requireAuth && window.YourStoreAuthStore) {
                const authState = window.YourStoreAuthStore.getState();
                if (!authState.isAuthenticated) {
                    emit('auth-required', props.product);
                    // Open login modal
                    if (window.YourStoreAuth) {
                        window.YourStoreAuth.openLogin();
                    }
                    return;
                }
            }
            
            try {
                // Start animation
                if (props.animate) {
                    isAnimating.value = true;
                    setTimeout(() => {
                        isAnimating.value = false;
                    }, 600);
                }
                
                const wasInWishlist = isInWishlist.value;
                
                // Toggle wishlist state
                await toggleWishlist(props.product);
                
                // Emit appropriate event
                if (wasInWishlist) {
                    emit('removed', props.product);
                    showTemporaryTooltip('Removed from wishlist');
                } else {
                    emit('added', props.product);
                    showTemporaryTooltip('Added to wishlist');
                }
                
                emit('toggled', {
                    product: props.product,
                    isInWishlist: !wasInWishlist
                });
                
            } catch (error) {
                console.error('Error toggling wishlist:', error);
                showTemporaryTooltip('Error updating wishlist', 'error');
            }
        };
        
        const showTemporaryTooltip = (message, type = 'success') => {
            // Clear existing timeout
            if (tooltipTimeout.value) {
                clearTimeout(tooltipTimeout.value);
            }
            
            // Create temporary tooltip
            const tooltip = document.createElement('div');
            tooltip.className = `fixed z-50 px-2 py-1 text-xs text-white rounded shadow-lg pointer-events-none ${
                type === 'error' ? 'bg-red-500' : 'bg-green-500'
            }`;
            tooltip.textContent = message;
            
            // Position tooltip
            const rect = event.target.getBoundingClientRect();
            tooltip.style.left = `${rect.left + rect.width / 2}px`;
            tooltip.style.top = `${rect.top - 30}px`;
            tooltip.style.transform = 'translateX(-50%)';
            
            document.body.appendChild(tooltip);
            
            // Remove tooltip after delay
            tooltipTimeout.value = setTimeout(() => {
                if (tooltip.parentNode) {
                    tooltip.parentNode.removeChild(tooltip);
                }
            }, 2000);
        };
        
        const handleMouseEnter = () => {
            showTooltip.value = true;
        };
        
        const handleMouseLeave = () => {
            showTooltip.value = false;
        };
        
        // Cleanup
        onUnmounted(() => {
            if (tooltipTimeout.value) {
                clearTimeout(tooltipTimeout.value);
            }
        });
        
        return {
            isInWishlist,
            isAnimating,
            showTooltip,
            buttonClasses,
            heartIconClasses,
            tooltipText,
            itemCount,
            isLoading,
            handleClick,
            handleMouseEnter,
            handleMouseLeave
        };
    },
    
    template: `
        <div class="wishlist-button-wrapper relative">
            <button
                :class="buttonClasses"
                :disabled="isLoading"
                :title="tooltipText"
                @click="handleClick"
                @mouseenter="handleMouseEnter"
                @mouseleave="handleMouseLeave"
            >
                <!-- Loading Spinner -->
                <i 
                    v-if="isLoading" 
                    class="fas fa-spinner fa-spin"
                ></i>
                
                <!-- Heart Icon -->
                <i 
                    v-else
                    :class="heartIconClasses"
                ></i>
                
                <!-- Label -->
                <span 
                    v-if="showLabel" 
                    class="ml-2 text-sm font-medium"
                >
                    {{ isInWishlist ? 'Saved' : 'Save' }}
                </span>
                
                <!-- Count Badge -->
                <span 
                    v-if="showCount && itemCount > 0"
                    class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center"
                >
                    {{ itemCount > 99 ? '99+' : itemCount }}
                </span>
                
                <!-- Pulse Animation -->
                <div 
                    v-if="isAnimating"
                    class="absolute inset-0 border-2 border-red-500 rounded-full animate-ping opacity-75"
                ></div>
            </button>
            
            <!-- Tooltip -->
            <div 
                v-if="showTooltip"
                class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 text-xs text-white bg-gray-900 rounded whitespace-nowrap pointer-events-none z-10"
            >
                {{ tooltipText }}
                <!-- Tooltip Arrow -->
                <div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
            </div>
        </div>
    `
};

// Create a simple wishlist heart icon that can be used anywhere
const WishlistHeart = {
    name: 'WishlistHeart',
    
    props: {
        productId: {
            type: [Number, String],
            required: true
        },
        size: {
            type: String,
            default: 'md'
        }
    },
    
    setup(props) {
        const { hasItem, toggleWishlist } = window.useWishlistStore();
        
        const isInWishlist = computed(() => hasItem(props.productId));
        
        const handleClick = async () => {
            // Create minimal product object for wishlist
            const product = {
                id: props.productId,
                name: `Product ${props.productId}`, // Fallback name
                price: '$0.00' // Fallback price
            };
            
            try {
                await toggleWishlist(product);
            } catch (error) {
                console.error('Error toggling wishlist:', error);
            }
        };
        
        return {
            isInWishlist,
            handleClick
        };
    },
    
    template: `
        <button
            @click="handleClick"
            class="wishlist-heart text-red-500 hover:text-red-600 transition-colors"
            :class="{
                'text-lg': size === 'sm',
                'text-xl': size === 'md', 
                'text-2xl': size === 'lg'
            }"
        >
            <i :class="isInWishlist ? 'fas fa-heart' : 'far fa-heart'"></i>
        </button>
    `
};

// Global helper functions
window.YourStoreWishlistButton = {
    // Add product to wishlist programmatically
    async addProduct(product) {
        if (window.YourStoreWishlistStore) {
            return await window.YourStoreWishlistStore.dispatch('ADD_TO_WISHLIST', { product });
        }
    },
    
    // Remove product from wishlist programmatically
    async removeProduct(productId) {
        if (window.YourStoreWishlistStore) {
            return await window.YourStoreWishlistStore.dispatch('REMOVE_FROM_WISHLIST', { productId });
        }
    },
    
    // Toggle product in wishlist programmatically
    async toggleProduct(product) {
        if (window.YourStoreWishlistStore) {
            return await window.YourStoreWishlistStore.dispatch('TOGGLE_WISHLIST', { product });
        }
    },
    
    // Check if product is in wishlist
    hasProduct(productId) {
        if (window.YourStoreWishlistStore) {
            return window.YourStoreWishlistStore.hasItem(productId);
        }
        return false;
    },
    
    // Get wishlist count
    getCount() {
        if (window.YourStoreWishlistStore) {
            return window.YourStoreWishlistStore.getItemCount();
        }
        return 0;
    }
};

// Auto-mount components if containers exist
document.addEventListener('DOMContentLoaded', () => {
    // Mount wishlist buttons
    const buttonContainers = document.querySelectorAll('.yourstore-wishlist-button');
    buttonContainers.forEach(container => {
        if (!container.querySelector('[data-vue-app]')) {
            const app = Vue.createApp(WishlistButton);
            
            // Extract props from data attributes
            const props = {};
            if (container.dataset.product) {
                try {
                    props.product = JSON.parse(container.dataset.product);
                } catch (e) {
                    console.error('Invalid product data for wishlist button:', e);
                    return;
                }
            }
            if (container.dataset.size) props.size = container.dataset.size;
            if (container.dataset.variant) props.variant = container.dataset.variant;
            if (container.dataset.showLabel) props.showLabel = container.dataset.showLabel === 'true';
            if (container.dataset.showCount) props.showCount = container.dataset.showCount === 'true';
            if (container.dataset.animate) props.animate = container.dataset.animate === 'true';
            if (container.dataset.requireAuth) props.requireAuth = container.dataset.requireAuth === 'true';
            
            app.mount(container);
            container.setAttribute('data-vue-app', 'mounted');
        }
    });
    
    // Mount wishlist hearts
    const heartContainers = document.querySelectorAll('.yourstore-wishlist-heart');
    heartContainers.forEach(container => {
        if (!container.querySelector('[data-vue-app]')) {
            const app = Vue.createApp(WishlistHeart);
            
            const props = {};
            if (container.dataset.productId) props.productId = container.dataset.productId;
            if (container.dataset.size) props.size = container.dataset.size;
            
            app.mount(container);
            container.setAttribute('data-vue-app', 'mounted');
        }
    });
});

// Export for use in other components
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { WishlistButton, WishlistHeart };
} else {
    window.WishlistButtonComponent = WishlistButton;
    window.WishlistHeartComponent = WishlistHeart;
}

})();
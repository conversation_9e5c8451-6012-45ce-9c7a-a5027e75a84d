/**
 * Category Showcase Vue Component
 * Interactive category display with filtering and product previews
 */

(function() {
    'use strict';
    
    // Wait for Vue to be available
    function initCategoryShowcase() {
        if (typeof Vue === 'undefined') {
            console.warn('YourStore Category Showcase: Vue.js not loaded yet, retrying...');
            setTimeout(initCategoryShowcase, 100);
            return;
        }
        
        console.log('YourStore Category Showcase: Vue.js detected, initializing component...');
        
        // Now safely destructure Vue methods
        const { ref, computed, onMounted, onUnmounted, nextTick } = Vue;

// Category Showcase Component
const CategoryShowcase = {
    name: 'CategoryShowcase',
    
    props: {
        title: {
            type: String,
            default: 'Shop by Category'
        },
        subtitle: {
            type: String,
            default: 'Explore our diverse product categories'
        },
        layout: {
            type: String,
            default: 'grid' // grid, carousel, masonry
        },
        columns: {
            type: Number,
            default: 4
        },
        showProductCount: {
            type: Boolean,
            default: true
        },
        showDescription: {
            type: Boolean,
            default: true
        },
        displayStyle: {
            type: String,
            default: 'cards' // cards, tiles, minimal
        },
        categories: {
            type: Array,
            default: () => []
        },
        excludeEmpty: {
            type: Boolean,
            default: true
        },
        showFeaturedProducts: {
            type: Boolean,
            default: false
        },
        productsPerCategory: {
            type: Number,
            default: 4
        }
    },
    
    setup(props) {
        // Reactive data
        const allCategories = ref([]);
        const categoryProducts = ref({});
        const loading = ref(true);
        const error = ref(null);
        const isVisible = ref(false);
        const activeCategory = ref(null);
        const sectionRef = ref(null);
        const intersectionObserver = ref(null);
        
        // Carousel states
        const currentSlide = ref(0);
        const carouselContainer = ref(null);
        const isDragging = ref(false);
        const dragStart = ref({ x: 0, y: 0 });
        
        // Animation states
        const hoveredCategory = ref(null);
        const animationStates = ref({});
        
        // Computed properties
        const displayCategories = computed(() => {
            let categories = [...allCategories.value];
            
            // Filter specific categories if provided
            if (props.categories.length > 0) {
                categories = categories.filter(cat => props.categories.includes(cat.slug));
            }
            
            // Exclude empty categories if requested
            if (props.excludeEmpty) {
                categories = categories.filter(cat => cat.product_count > 0);
            }
            
            return categories;
        });
        
        const gridClasses = computed(() => {
            const colsMap = {
                1: 'grid-cols-1',
                2: 'grid-cols-1 sm:grid-cols-2',
                3: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3',
                4: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
                5: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5',
                6: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 xl:grid-cols-6'
            };
            return `grid gap-6 ${colsMap[props.columns] || colsMap[4]}`;
        });
        
        const categoryStats = computed(() => {
            const totalProducts = displayCategories.value.reduce((sum, cat) => sum + cat.product_count, 0);
            return {
                totalCategories: displayCategories.value.length,
                totalProducts,
                avgProductsPerCategory: Math.round(totalProducts / displayCategories.value.length || 0)
            };
        });
        
        const visibleSlides = computed(() => {
            const slideWidth = 280; // Approximate card width
            const containerWidth = 1200; // Approximate container width
            return Math.floor(containerWidth / slideWidth);
        });
        
        const maxSlides = computed(() => {
            return Math.max(0, displayCategories.value.length - visibleSlides.value);
        });
        
        // Methods
        
        const loadFallbackData = () => {
            // Fallback category data with enhanced information
            allCategories.value = [
                {
                    name: 'Electronics',
                    slug: 'electronics',
                    description: 'Latest gadgets, smartphones, laptops and tech accessories',
                    image: 'https://images.unsplash.com/photo-1498049794561-7780e7231661?w=400&h=300&fit=crop',
                    icon: 'fas fa-laptop',
                    product_count: 45,
                    color: '#3b82f6',
                    featured_badge: 'Hot'
                },
                {
                    name: 'Fashion & Clothing',
                    slug: 'clothing',
                    description: 'Trendy apparel, shoes, and fashion accessories for all styles',
                    image: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=400&h=300&fit=crop',
                    icon: 'fas fa-tshirt',
                    product_count: 78,
                    color: '#ec4899',
                    featured_badge: 'New'
                },
                {
                    name: 'Home & Garden',
                    slug: 'home-garden',
                    description: 'Furniture, decor, appliances and everything for your home',
                    image: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop',
                    icon: 'fas fa-home',
                    product_count: 62,
                    color: '#059669'
                },
                {
                    name: 'Sports & Fitness',
                    slug: 'sports-fitness',
                    description: 'Athletic gear, fitness equipment and active lifestyle products',
                    image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop',
                    icon: 'fas fa-dumbbell',
                    product_count: 34,
                    color: '#f59e0b'
                },
                {
                    name: 'Beauty & Health',
                    slug: 'beauty-health',
                    description: 'Skincare, makeup, wellness products and health essentials',
                    image: 'https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=400&h=300&fit=crop',
                    icon: 'fas fa-heart',
                    product_count: 41,
                    color: '#8b5cf6'
                },
                {
                    name: 'Books & Media',
                    slug: 'books-media',
                    description: 'Books, e-readers, music, movies and digital content',
                    image: 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=400&h=300&fit=crop',
                    icon: 'fas fa-book',
                    product_count: 29,
                    color: '#dc2626'
                },
                {
                    name: 'Toys & Games',
                    slug: 'toys-games',
                    description: 'Fun toys, board games and entertainment for all ages',
                    image: 'https://images.unsplash.com/photo-**********-d644479cb6f7?w=400&h=300&fit=crop',
                    icon: 'fas fa-gamepad',
                    product_count: 23,
                    color: '#f97316'
                },
                {
                    name: 'Automotive',
                    slug: 'automotive',
                    description: 'Car accessories, parts, tools and maintenance products',
                    image: 'https://images.unsplash.com/photo-1449965408869-eaa3f722e40d?w=400&h=300&fit=crop',
                    icon: 'fas fa-car',
                    product_count: 18,
                    color: '#6366f1'
                }
            ];
        };
        
        const loadCategoryProducts = async () => {
            for (const category of displayCategories.value) {
                try {
                    const response = await fetch(
                        `${window.yourStoreCommerce?.rest_url || '/wp-json/yourstore-commerce/v1/'}products?category=${category.slug}&limit=${props.productsPerCategory}`
                    );
                    
                    if (response.ok) {
                        const data = await response.json();
                        categoryProducts.value[category.slug] = data.products || [];
                    }
                } catch (err) {
                    console.error(`Error loading products for ${category.name}:`, err);
                }
            }
        };
        
        const navigateToCategory = (category) => {
            // Track analytics
            if (typeof gtag !== 'undefined') {
                gtag('event', 'category_click', {
                    event_category: 'navigation',
                    event_label: category.name,
                    category_slug: category.slug
                });
            }
            
            // Navigate to category page
            const categoryUrl = `/product-category/${category.slug}/`;
            window.location.href = categoryUrl;
        };
        
        const handleCategoryHover = (category, isEntering = true) => {
            if (isEntering) {
                hoveredCategory.value = category.slug;
                
                // Trigger hover animation
                animationStates.value[category.slug] = {
                    ...animationStates.value[category.slug],
                    isHovered: true
                };
            } else {
                hoveredCategory.value = null;
                
                // Reset hover animation
                if (animationStates.value[category.slug]) {
                    animationStates.value[category.slug].isHovered = false;
                }
            }
        };
        
        const toggleCategoryExpansion = (category) => {
            activeCategory.value = activeCategory.value === category.slug ? null : category.slug;
        };
        
        // Carousel methods
        const nextSlide = () => {
            if (currentSlide.value < maxSlides.value) {
                currentSlide.value++;
            }
        };
        
        const prevSlide = () => {
            if (currentSlide.value > 0) {
                currentSlide.value--;
            }
        };
        
        const goToSlide = (index) => {
            currentSlide.value = Math.max(0, Math.min(index, maxSlides.value));
        };
        
        // Drag functionality for carousel
        const handleDragStart = (e) => {
            isDragging.value = true;
            dragStart.value = {
                x: e.type === 'mousedown' ? e.clientX : e.touches[0].clientX,
                y: e.type === 'mousedown' ? e.clientY : e.touches[0].clientY
            };
        };
        
        const handleDragEnd = (e) => {
            if (!isDragging.value) return;
            
            const currentX = e.type === 'mouseup' ? e.clientX : e.changedTouches[0].clientX;
            const deltaX = dragStart.value.x - currentX;
            
            if (Math.abs(deltaX) > 50) { // Minimum drag distance
                if (deltaX > 0) {
                    nextSlide();
                } else {
                    prevSlide();
                }
            }
            
            isDragging.value = false;
        };
        
        const setupIntersectionObserver = () => {
            intersectionObserver.value = new IntersectionObserver(
                (entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            setTimeout(() => {
                                isVisible.value = true;
                            }, 100);
                        }
                    });
                },
                { threshold: 0.1 }
            );
            
            if (sectionRef.value) {
                intersectionObserver.value.observe(sectionRef.value);
            }
        };
        
        const formatNumber = (num) => {
            return new Intl.NumberFormat('en-US').format(num);
        };
        
        // Lifecycle
        // Load categories from GraphQL
        const loadCategories = async () => {
            loading.value = true;
            error.value = null;

            try {
                // Try GraphQL first if available
                if (window.graphqlClient && window.GRAPHQL_QUERIES) {
                    console.log('Loading categories from GraphQL...');
                    
                    const result = await window.graphqlClient.query(
                        window.GRAPHQL_QUERIES.GET_CATEGORIES,
                        { first: props.maxCategories || 8 }
                    );
                    
                    if (result.data && result.data.productCategories && result.data.productCategories.nodes.length > 0) {
                        // Transform GraphQL category data to component format
                        const graphqlCategories = result.data.productCategories.nodes.map(category => ({
                            id: category.databaseId,
                            name: category.name,
                            slug: category.slug,
                            description: category.description || `Explore our ${category.name.toLowerCase()} collection`,
                            image: category.image?.sourceUrl || `https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400&h=300&fit=crop`,
                            product_count: category.count || 0,
                            icon: 'fas fa-tag', // Default icon, could be enhanced with custom fields
                            color: '#10b981', // Default color, could be enhanced with custom fields
                            parent: category.parent?.node ? {
                                id: category.parent.node.databaseId,
                                name: category.parent.node.name,
                                slug: category.parent.node.slug
                            } : null,
                            children: category.children?.nodes?.map(child => ({
                                id: child.databaseId,
                                name: child.name,
                                slug: child.slug,
                                count: child.count || 0
                            })) || []
                        }));
                        
                        // Filter out empty categories if requested
                        const filteredCategories = props.excludeEmpty 
                            ? graphqlCategories.filter(cat => cat.product_count > 0)
                            : graphqlCategories;
                        
                        allCategories.value = filteredCategories;
                        console.log(`✅ Loaded ${filteredCategories.length} categories from GraphQL`);
                        loading.value = false;
                        return;
                    }
                }
                
                // Fallback to WordPress REST API
                console.log('GraphQL unavailable, trying WordPress REST API...');
                
                const response = await fetch(`/wp-json/wc/v3/products/categories?per_page=${props.maxCategories || 8}&hide_empty=${props.excludeEmpty}`, {
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                
                if (response.ok) {
                    const wooCategories = await response.json();
                    
                    if (wooCategories && wooCategories.length > 0) {
                        // Transform WooCommerce REST API data to component format
                        const restCategories = wooCategories.map(category => ({
                            id: category.id,
                            name: category.name,
                            slug: category.slug,
                            description: category.description || `Explore our ${category.name.toLowerCase()} collection`,
                            image: category.image?.src || `https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400&h=300&fit=crop`,
                            product_count: category.count || 0,
                            icon: 'fas fa-tag', // Default icon
                            color: '#10b981', // Default color
                            parent: category.parent ? {
                                id: category.parent,
                                name: 'Parent Category',
                                slug: 'parent'
                            } : null,
                            children: [] // REST API doesn't include children in basic response
                        }));
                        
                        allCategories.value = restCategories;
                        console.log(`✅ Loaded ${restCategories.length} categories from WooCommerce REST API`);
                        loading.value = false;
                        return;
                    }
                }
                
                // Last resort: fallback to mock data
                console.log('All APIs failed, using fallback data...');
                loadFallbackData();
                
            } catch (err) {
                console.error('Error loading categories:', err);
                error.value = 'Failed to load categories. Please try again later.';
                
                // Use fallback data on error
                loadFallbackData();
            }
            
            loading.value = false;
        };

        onMounted(async () => {
            await nextTick();
            setupIntersectionObserver();
            
            // Load categories from GraphQL first, fallback to mock data
            await loadCategories();
        });
        
        onUnmounted(() => {
            if (intersectionObserver.value) {
                intersectionObserver.value.disconnect();
            }
        });
        
        return {
            allCategories,
            categoryProducts,
            displayCategories,
            loading,
            error,
            isVisible,
            activeCategory,
            sectionRef,
            currentSlide,
            carouselContainer,
            hoveredCategory,
            animationStates,
            gridClasses,
            categoryStats,
            visibleSlides,
            maxSlides,
            navigateToCategory,
            handleCategoryHover,
            toggleCategoryExpansion,
            nextSlide,
            prevSlide,
            goToSlide,
            handleDragStart,
            handleDragEnd,
            formatNumber
        };
    },
    
    template: `
        <section ref="sectionRef" class="yourstore-category-showcase py-16">
            <div class="container mx-auto px-4">
                
                <!-- Section Header -->
                <div class="text-center mb-12">
                    <div 
                        class="inline-flex items-center px-3 py-1 rounded-full bg-primary/10 text-primary text-sm font-medium mb-4"
                        :class="{
                            'opacity-0 translate-y-4': !isVisible,
                            'opacity-100 translate-y-0': isVisible,
                            'transition-all duration-700 ease-out': true
                        }"
                    >
                        <i class="fas fa-th-large mr-2"></i>
                        Product Categories
                    </div>
                    
                    <h2 
                        class="text-3xl sm:text-4xl lg:text-5xl font-bold text-secondary-900 mb-4"
                        :class="{
                            'opacity-0 translate-y-8': !isVisible,
                            'opacity-100 translate-y-0': isVisible,
                            'transition-all duration-1000 ease-out delay-200': true
                        }"
                        v-html="title"
                    ></h2>
                    
                    <p 
                        class="text-xl text-secondary-600 max-w-2xl mx-auto mb-8"
                        :class="{
                            'opacity-0 translate-y-8': !isVisible,
                            'opacity-100 translate-y-0': isVisible,
                            'transition-all duration-1000 ease-out delay-400': true
                        }"
                        v-html="subtitle"
                    ></p>
                    
                    <!-- Category Stats -->
                    <div 
                        v-if="!loading && displayCategories.length > 0"
                        class="flex flex-wrap justify-center items-center gap-6 text-sm text-secondary-500 mb-8"
                        :class="{
                            'opacity-0 translate-y-4': !isVisible,
                            'opacity-100 translate-y-0': isVisible,
                            'transition-all duration-1000 ease-out delay-600': true
                        }"
                    >
                        <span class="flex items-center">
                            <i class="fas fa-tags mr-2 text-primary"></i>
                            {{ categoryStats.totalCategories }} Categories
                        </span>
                        <span class="flex items-center">
                            <i class="fas fa-box mr-2 text-accent"></i>
                            {{ formatNumber(categoryStats.totalProducts) }} Products
                        </span>
                        <span class="flex items-center">
                            <i class="fas fa-chart-line mr-2 text-green-500"></i>
                            {{ categoryStats.avgProductsPerCategory }} Avg per Category
                        </span>
                    </div>
                </div>
                
                <!-- Loading State -->
                <div v-if="loading" class="grid gap-6" :class="gridClasses">
                    <div v-for="n in columns" :key="'skeleton-' + n" class="card overflow-hidden">
                        <div class="skeleton h-48 mb-0"></div>
                        <div class="p-6">
                            <div class="skeleton h-6 mb-2"></div>
                            <div class="skeleton h-4 mb-3"></div>
                            <div class="skeleton h-4 w-1/2"></div>
                        </div>
                    </div>
                </div>
                
                <!-- Error State -->
                <div v-else-if="error" class="text-center py-12">
                    <div class="text-red-600 text-lg mb-4">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        {{ error }}
                    </div>
                    <button @click="loadCategories" class="btn-primary">
                        <i class="fas fa-redo mr-2"></i>
                        Try Again
                    </button>
                </div>
                
                <!-- Grid Layout -->
                <div 
                    v-else-if="layout === 'grid' && displayCategories.length > 0" 
                    class="grid gap-6" 
                    :class="gridClasses"
                >
                    <div
                        v-for="(category, index) in displayCategories"
                        :key="category.slug"
                        class="category-card group relative overflow-hidden bg-white rounded-xl shadow-sm hover:shadow-xl transition-all duration-500 cursor-pointer"
                        :class="{
                            'opacity-0 translate-y-8 scale-95': !isVisible,
                            'opacity-100 translate-y-0 scale-100': isVisible,
                            'transition-all duration-700 ease-out': true,
                            'transform hover:-translate-y-2': displayStyle === 'cards'
                        }"
                        :style="{ transitionDelay: (index * 100) + 'ms' }"
                        @click="navigateToCategory(category)"
                        @mouseenter="handleCategoryHover(category, true)"
                        @mouseleave="handleCategoryHover(category, false)"
                    >
                        <!-- Category Image -->
                        <div class="relative overflow-hidden" :class="displayStyle === 'cards' ? 'h-48' : 'h-32'">
                            <img
                                :src="category.image"
                                :alt="category.name"
                                class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
                                loading="lazy"
                            />
                            
                            <!-- Overlay -->
                            <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent group-hover:from-black/70 transition-all duration-300"></div>
                            
                            <!-- Badge -->
                            <div v-if="category.featured_badge" class="absolute top-3 right-3">
                                <span class="badge-sale" :style="{ backgroundColor: category.color }">
                                    {{ category.featured_badge }}
                                </span>
                            </div>
                            
                            <!-- Icon Overlay -->
                            <div class="absolute top-3 left-3 w-12 h-12 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                                <i :class="category.icon" class="text-xl" :style="{ color: category.color }"></i>
                            </div>
                            
                            <!-- Product Count -->
                            <div v-if="showProductCount" class="absolute bottom-3 left-3 text-white text-sm font-medium">
                                <i class="fas fa-box mr-1"></i>
                                {{ formatNumber(category.product_count) }} Products
                            </div>
                        </div>
                        
                        <!-- Category Info -->
                        <div class="p-6">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-xl font-bold text-secondary-900 group-hover:text-primary transition-colors duration-200">
                                    {{ category.name }}
                                </h3>
                                <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                                    <i class="fas fa-arrow-right text-primary"></i>
                                </div>
                            </div>
                            
                            <p 
                                v-if="showDescription && category.description" 
                                class="text-secondary-600 text-sm line-clamp-2 mb-4"
                            >
                                {{ category.description }}
                            </p>
                            
                            <!-- Category Stats -->
                            <div class="flex items-center justify-between text-xs text-secondary-500">
                                <span>{{ formatNumber(category.product_count) }} items</span>
                                <span class="group-hover:text-primary transition-colors duration-200">
                                    Shop now →
                                </span>
                            </div>
                        </div>
                        
                        <!-- Featured Products Preview -->
                        <div 
                            v-if="showFeaturedProducts && categoryProducts[category.slug] && categoryProducts[category.slug].length > 0"
                            class="border-t border-gray-100 p-4 bg-gray-50"
                        >
                            <div class="text-xs font-medium text-secondary-700 mb-2">Featured Products:</div>
                            <div class="flex gap-2 overflow-hidden">
                                <div 
                                    v-for="product in categoryProducts[category.slug].slice(0, 3)" 
                                    :key="product.id"
                                    class="flex-shrink-0 w-12 h-12 rounded-md overflow-hidden"
                                >
                                    <img 
                                        :src="product.image" 
                                        :alt="product.name"
                                        class="w-full h-full object-cover"
                                    />
                                </div>
                                <div 
                                    v-if="categoryProducts[category.slug].length > 3"
                                    class="flex-shrink-0 w-12 h-12 rounded-md bg-gray-200 flex items-center justify-center text-xs text-secondary-600 font-medium"
                                >
                                    +{{ categoryProducts[category.slug].length - 3 }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Carousel Layout -->
                <div 
                    v-else-if="layout === 'carousel' && displayCategories.length > 0"
                    class="relative"
                    :class="{
                        'opacity-0 translate-y-8': !isVisible,
                        'opacity-100 translate-y-0': isVisible,
                        'transition-all duration-1000 ease-out delay-600': true
                    }"
                >
                    <!-- Carousel Container -->
                    <div class="overflow-hidden" ref="carouselContainer">
                        <div 
                            class="flex transition-transform duration-300 ease-out gap-6"
                            :style="{ transform: \`translateX(-\${currentSlide * 300}px)\` }"
                            @mousedown="handleDragStart"
                            @mouseup="handleDragEnd"
                            @touchstart="handleDragStart"
                            @touchend="handleDragEnd"
                        >
                            <div
                                v-for="category in displayCategories"
                                :key="category.slug"
                                class="flex-shrink-0 w-72 category-card group relative overflow-hidden bg-white rounded-xl shadow-sm hover:shadow-xl transition-all duration-500 cursor-pointer"
                                @click="navigateToCategory(category)"
                            >
                                <!-- Same category card content as grid layout -->
                                <div class="relative overflow-hidden h-40">
                                    <img
                                        :src="category.image"
                                        :alt="category.name"
                                        class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
                                        loading="lazy"
                                    />
                                    <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent"></div>
                                    <div class="absolute top-3 left-3 w-10 h-10 bg-white/90 rounded-full flex items-center justify-center">
                                        <i :class="category.icon" class="text-lg" :style="{ color: category.color }"></i>
                                    </div>
                                    <div v-if="showProductCount" class="absolute bottom-3 left-3 text-white text-sm font-medium">
                                        {{ formatNumber(category.product_count) }} Products
                                    </div>
                                </div>
                                
                                <div class="p-4">
                                    <h3 class="text-lg font-bold text-secondary-900 group-hover:text-primary transition-colors mb-2">
                                        {{ category.name }}
                                    </h3>
                                    <p v-if="showDescription" class="text-secondary-600 text-sm line-clamp-2">
                                        {{ category.description }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Carousel Controls -->
                    <div class="flex items-center justify-center mt-8 gap-4">
                        <button
                            @click="prevSlide"
                            :disabled="currentSlide === 0"
                            class="btn-ghost rounded-full p-3"
                            :class="{ 'opacity-50 cursor-not-allowed': currentSlide === 0 }"
                        >
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        
                        <div class="flex gap-2">
                            <button
                                v-for="i in Math.ceil(displayCategories.length / visibleSlides)"
                                :key="i"
                                @click="goToSlide(i - 1)"
                                class="w-3 h-3 rounded-full transition-all duration-200"
                                :class="{
                                    'bg-primary': currentSlide === i - 1,
                                    'bg-gray-300 hover:bg-gray-400': currentSlide !== i - 1
                                }"
                            ></button>
                        </div>
                        
                        <button
                            @click="nextSlide"
                            :disabled="currentSlide >= maxSlides"
                            class="btn-ghost rounded-full p-3"
                            :class="{ 'opacity-50 cursor-not-allowed': currentSlide >= maxSlides }"
                        >
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
                
                <!-- Empty State -->
                <div v-else-if="!loading && displayCategories.length === 0" class="text-center py-12">
                    <div class="text-secondary-600 text-lg mb-4">
                        <i class="fas fa-folder-open mr-2"></i>
                        No categories found.
                    </div>
                    <button @click="loadCategories" class="btn-outline">
                        <i class="fas fa-redo mr-2"></i>
                        Refresh Categories
                    </button>
                </div>
                
            </div>
        </section>
    `
};

        // Make component globally accessible
        console.log('YourStore: CategoryShowcase component definition:', CategoryShowcase);
        console.log('YourStore: Setting window.CategoryShowcase...');
        window.CategoryShowcase = CategoryShowcase;
        console.log('YourStore: window.CategoryShowcase is now set to:', window.CategoryShowcase);

        // Auto-mount component if container exists (legacy support)
        document.addEventListener('DOMContentLoaded', () => {
            const containers = document.querySelectorAll('.yourstore-category-showcase');
            containers.forEach(container => {
                // Skip if already mounted by app.js or has Vue app
                if (container.__vue_app || container.querySelector('[data-vue-app]')) {
                    return;
                }
                
                let props = {};
                
                // Handle hybrid component format (data-props)
                if (container.dataset.props) {
                    try {
                        props = JSON.parse(container.dataset.props);
                    } catch (e) {
                        console.warn('YourStore: Invalid JSON in data-props:', e);
                    }
                } 
                // Handle legacy format (individual data attributes)
                else {
                    if (container.dataset.title) props.title = container.dataset.title;
                    if (container.dataset.subtitle) props.subtitle = container.dataset.subtitle;
                    if (container.dataset.layout) props.layout = container.dataset.layout;
                    if (container.dataset.columns) props.columns = parseInt(container.dataset.columns);
                    if (container.dataset.showProductCount) props.showProductCount = container.dataset.showProductCount === 'true';
                    if (container.dataset.showDescription) props.showDescription = container.dataset.showDescription === 'true';
                    if (container.dataset.displayStyle) props.displayStyle = container.dataset.displayStyle;
                    if (container.dataset.categories) props.categories = JSON.parse(container.dataset.categories);
                    if (container.dataset.excludeEmpty) props.excludeEmpty = container.dataset.excludeEmpty === 'true';
                    if (container.dataset.showFeaturedProducts) props.showFeaturedProducts = container.dataset.showFeaturedProducts === 'true';
                    if (container.dataset.productsPerCategory) props.productsPerCategory = parseInt(container.dataset.productsPerCategory);
                }
                
                const app = Vue.createApp(CategoryShowcase, props);
                container.__vue_app = app.mount(container);
            });
        });

        // Export for different module systems
        if (typeof window !== 'undefined') {
            window.CategoryShowcase = CategoryShowcase;
            console.log('YourStore: CategoryShowcase component registered on window object');
        } else if (typeof module !== 'undefined' && module.exports) {
            module.exports = CategoryShowcase;
        }
    }
    
    // Initialize the component
    initCategoryShowcase();
    
})();
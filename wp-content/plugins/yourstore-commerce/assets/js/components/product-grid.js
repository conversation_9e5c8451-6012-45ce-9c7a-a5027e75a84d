/**
 * Product Grid Vue Component
 * Displays a filterable grid of products with pagination
 */

(function() {
    'use strict';
    
    const { ref, computed, onMounted, watch } = Vue;

// Product Grid Component
const ProductGrid = {
    name: 'ProductGrid',
    
    props: {
        limit: {
            type: Number,
            default: 12
        },
        columns: {
            type: Number,
            default: 4
        },
        categories: {
            type: Array,
            default: () => []
        },
        showFilters: {
            type: Boolean,
            default: true
        },
        showPagination: {
            type: Boolean,
            default: true
        }
    },
    
    setup(props) {
        // Reactive data
        const products = ref([]);
        const filteredProducts = ref([]);
        const currentPage = ref(1);
        const loading = ref(true);
        const error = ref(null);
        
        // Filter states
        const selectedCategory = ref('');
        const priceRange = ref([0, 1000]);
        const sortBy = ref('name-asc');
        const searchQuery = ref('');
        
        // Computed properties
        const totalPages = computed(() => Math.ceil(filteredProducts.value.length / props.limit));
        const currentProducts = computed(() => {
            const start = (currentPage.value - 1) * props.limit;
            const end = start + props.limit;
            return filteredProducts.value.slice(start, end);
        });
        
        const gridClasses = computed(() => {
            const colsMap = {
                1: 'grid-cols-1',
                2: 'grid-cols-1 sm:grid-cols-2',
                3: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3',
                4: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
                5: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5',
                6: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6'
            };
            return `grid gap-6 ${colsMap[props.columns] || colsMap[4]}`;
        });
        
        // Methods
        const loadProducts = async () => {
            loading.value = true;
            error.value = null;
            
            try {
                // Use new API service for smart data fetching
                if (window.YourStoreAPI) {
                    const data = await window.YourStoreAPI.getData.products({
                        per_page: props.limit || 12,
                        orderby: 'date',
                        order: 'DESC'
                    });
                    products.value = data.products || data || [];
                    console.log('Loaded products via API service:', products.value.length);
                } else {
                    // Fallback to direct REST API
                    const response = await fetch(`${window.yourStoreCommerce?.rest_url || '/wp-json/yourstore-commerce/v1/'}products?limit=${props.limit || 12}`);
                    
                    if (!response.ok) {
                        throw new Error('Failed to load products');
                    }
                    
                    const data = await response.json();
                    products.value = data.products || [];
                    console.log('Loaded products via REST API:', products.value.length);
                }
                
                // If no products loaded, use fallback mock data
                if (!products.value || products.value.length === 0) {
                    console.log('No products found, loading fallback mock data');
                    loadFallbackData();
                } else {
                    applyFilters();
                }
            } catch (err) {
                console.error('Error loading products:', err);
                error.value = 'Failed to load products. Using fallback data.';
                loadFallbackData();
            } finally {
                loading.value = false;
            }
        };
        
        const loadFallbackData = () => {
            // Fallback mock data for when WooCommerce is not available
            products.value = [
                {
                    id: 1,
                    name: 'Premium Wireless Headphones',
                    price: 299.99,
                    sale_price: 249.99,
                    image: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=400&fit=crop',
                    categories: ['electronics'],
                    rating: 4.5,
                    review_count: 128,
                    on_sale: true,
                    featured: false
                },
                {
                    id: 2,
                    name: 'Organic Cotton T-Shirt',
                    price: 39.99,
                    image: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400&h=400&fit=crop',
                    categories: ['clothing'],
                    rating: 4.8,
                    review_count: 89,
                    on_sale: false,
                    featured: true
                },
                {
                    id: 3,
                    name: 'Minimalist Watch',
                    price: 159.99,
                    image: 'https://images.unsplash.com/photo-1524805444758-089113d48a6d?w=400&h=400&fit=crop',
                    categories: ['accessories'],
                    rating: 4.3,
                    review_count: 45,
                    on_sale: false,
                    featured: false
                },
                {
                    id: 4,
                    name: 'Ergonomic Office Chair',
                    price: 449.99,
                    sale_price: 399.99,
                    image: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=400&fit=crop',
                    categories: ['furniture'],
                    rating: 4.6,
                    review_count: 67,
                    on_sale: true,
                    featured: true
                }
            ];
            applyFilters();
        };
        
        const applyFilters = () => {
            let filtered = [...products.value];
            
            // Category filter
            if (selectedCategory.value) {
                filtered = filtered.filter(product => product.category === selectedCategory.value);
            }
            
            // Price range filter
            filtered = filtered.filter(product => {
                const price = product.salePrice || product.price;
                return price >= priceRange.value[0] && price <= priceRange.value[1];
            });
            
            // Search filter
            if (searchQuery.value) {
                const query = searchQuery.value.toLowerCase();
                filtered = filtered.filter(product => 
                    product.name.toLowerCase().includes(query)
                );
            }
            
            // Sort
            filtered.sort((a, b) => {
                switch (sortBy.value) {
                    case 'name-asc':
                        return a.name.localeCompare(b.name);
                    case 'name-desc':
                        return b.name.localeCompare(a.name);
                    case 'price-asc':
                        return (a.salePrice || a.price) - (b.salePrice || b.price);
                    case 'price-desc':
                        return (b.salePrice || b.price) - (a.salePrice || a.price);
                    case 'rating-desc':
                        return b.rating - a.rating;
                    default:
                        return 0;
                }
            });
            
            filteredProducts.value = filtered;
            currentPage.value = 1; // Reset to first page
        };
        
        const formatPrice = (price) => {
            return new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD'
            }).format(price);
        };
        
        const renderStars = (rating) => {
            const stars = [];
            const fullStars = Math.floor(rating);
            const hasHalfStar = rating % 1 !== 0;
            
            for (let i = 0; i < fullStars; i++) {
                stars.push('★');
            }
            
            if (hasHalfStar) {
                stars.push('☆');
            }
            
            while (stars.length < 5) {
                stars.push('☆');
            }
            
            return stars.join('');
        };
        
        // Watchers
        watch([selectedCategory, priceRange, sortBy, searchQuery], applyFilters);
        
        // Lifecycle
        onMounted(() => {
            loadProducts();
        });
        
        return {
            products,
            filteredProducts,
            currentProducts,
            currentPage,
            totalPages,
            loading,
            error,
            selectedCategory,
            priceRange,
            sortBy,
            searchQuery,
            gridClasses,
            formatPrice,
            renderStars,
            applyFilters
        };
    },
    
    template: `
        <div class="yourstore-product-grid">
            <!-- Filters Section -->
            <div v-if="showFilters" class="mb-8">
                <div class="flex flex-wrap gap-4 items-center justify-between mb-6">
                    <div class="flex flex-wrap gap-4 items-center">
                        <!-- Search -->
                        <div class="relative">
                            <input
                                v-model="searchQuery"
                                type="text"
                                placeholder="Search products..."
                                class="input pl-10 w-64"
                            />
                            <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                        </div>
                        
                        <!-- Category Filter -->
                        <select v-model="selectedCategory" class="input w-48">
                            <option value="">All Categories</option>
                            <option value="electronics">Electronics</option>
                            <option value="clothing">Clothing</option>
                            <option value="accessories">Accessories</option>
                            <option value="furniture">Furniture</option>
                        </select>
                        
                        <!-- Sort -->
                        <select v-model="sortBy" class="input w-48">
                            <option value="name-asc">Name (A-Z)</option>
                            <option value="name-desc">Name (Z-A)</option>
                            <option value="price-asc">Price (Low to High)</option>
                            <option value="price-desc">Price (High to Low)</option>
                            <option value="rating-desc">Highest Rated</option>
                        </select>
                    </div>
                    
                    <!-- Results Count -->
                    <div class="text-sm text-secondary-600">
                        {{ filteredProducts.length }} products found
                    </div>
                </div>
            </div>
            
            <!-- Loading State -->
            <div v-if="loading" class="grid gap-6" :class="gridClasses">
                <div v-for="n in limit" :key="'skeleton-' + n" class="card p-4">
                    <div class="skeleton h-48 mb-4 rounded"></div>
                    <div class="skeleton h-4 mb-2 rounded"></div>
                    <div class="skeleton h-4 w-3/4 mb-2 rounded"></div>
                    <div class="skeleton h-6 w-1/2 rounded"></div>
                </div>
            </div>
            
            <!-- Error State -->
            <div v-else-if="error" class="text-center py-12">
                <div class="text-red-600 text-lg mb-4">
                    <i class="fas fa-exclamation-triangle mr-2"></i>
                    {{ error }}
                </div>
                <button @click="loadProducts" class="btn-primary">
                    Try Again
                </button>
            </div>
            
            <!-- Products Grid -->
            <div v-else class="grid gap-6" :class="gridClasses">
                <div
                    v-for="product in currentProducts"
                    :key="product.id"
                    class="product-card card group"
                >
                    <!-- Product Image -->
                    <div class="relative overflow-hidden rounded-t-lg">
                        <img
                            :src="product.image"
                            :alt="product.name"
                            class="product-image w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                        />
                        <div v-if="product.badge" class="sale-badge">
                            {{ product.badge }}
                        </div>
                    </div>
                    
                    <!-- Product Info -->
                    <div class="p-4">
                        <h3 class="product-title mb-2 line-clamp-2">{{ product.name }}</h3>
                        
                        <!-- Rating -->
                        <div class="flex items-center mb-2">
                            <div class="text-yellow-400 mr-1">{{ renderStars(product.rating) }}</div>
                            <span class="text-sm text-secondary-500">({{ product.reviewCount }})</span>
                        </div>
                        
                        <!-- Price -->
                        <div class="flex items-center justify-between">
                            <div class="price-section">
                                <span v-if="product.salePrice" class="price-tag text-lg">
                                    {{ formatPrice(product.salePrice) }}
                                </span>
                                <span 
                                    :class="product.salePrice ? 'text-sm text-secondary-400 line-through ml-2' : 'price-tag text-lg'"
                                >
                                    {{ formatPrice(product.price) }}
                                </span>
                            </div>
                            
                            <button class="btn-primary btn-sm opacity-0 group-hover:opacity-100 transition-opacity">
                                <i class="fas fa-shopping-cart mr-1"></i>
                                Add
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Pagination -->
            <div v-if="showPagination && totalPages > 1" class="flex justify-center mt-8">
                <nav class="flex space-x-1">
                    <button
                        @click="currentPage = Math.max(1, currentPage - 1)"
                        :disabled="currentPage === 1"
                        class="btn-ghost btn-sm"
                        :class="{ 'opacity-50 cursor-not-allowed': currentPage === 1 }"
                    >
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    
                    <button
                        v-for="page in Math.min(totalPages, 5)"
                        :key="page"
                        @click="currentPage = page"
                        :class="page === currentPage ? 'btn-primary btn-sm' : 'btn-ghost btn-sm'"
                    >
                        {{ page }}
                    </button>
                    
                    <button
                        @click="currentPage = Math.min(totalPages, currentPage + 1)"
                        :disabled="currentPage === totalPages"
                        class="btn-ghost btn-sm"
                        :class="{ 'opacity-50 cursor-not-allowed': currentPage === totalPages }"
                    >
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </nav>
            </div>
        </div>
    `
};

// Auto-mount component if container exists
document.addEventListener('DOMContentLoaded', () => {
    const containers = document.querySelectorAll('.yourstore-product-grid');
    containers.forEach(container => {
        if (!container.querySelector('[data-vue-app]')) {
            const app = Vue.createApp(ProductGrid);
            const mountPoint = document.createElement('div');
            mountPoint.setAttribute('data-vue-app', 'product-grid');
            container.appendChild(mountPoint);
            app.mount(mountPoint);
        }
    });
});

    // Export for different module systems
    if (typeof window !== 'undefined') {
        window.ProductGrid = ProductGrid;
        console.log('YourStore: ProductGrid component registered on window object');
    } else if (typeof module !== 'undefined' && module.exports) {
        module.exports = ProductGrid;
    }
    
})();
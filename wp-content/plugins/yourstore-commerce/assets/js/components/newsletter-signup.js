/**
 * Newsletter Signup Vue Component
 * Advanced newsletter subscription with validation and AJAX
 */

(function() {
    'use strict';
    
    // Wait for Vue to be available
    function initNewsletterSignup() {
        if (typeof Vue === 'undefined') {
            console.warn('YourStore Newsletter: Vue.js not loaded yet, retrying...');
            setTimeout(initNewsletterSignup, 100);
            return;
        }
        
        console.log('YourStore Newsletter: Vue.js detected, initializing component...');
        
        // Now safely destructure Vue methods
        const { ref, computed, reactive, onMounted, onUnmounted } = Vue;

// Newsletter Signup Component
const NewsletterSignup = {
    name: 'NewsletterSignup',
    
    props: {
        title: {
            type: String,
            default: 'Stay in the Loop'
        },
        subtitle: {
            type: String,
            default: 'Get the latest updates on new products, exclusive deals, and insider tips delivered straight to your inbox.'
        },
        placeholder: {
            type: String,
            default: 'Enter your email address'
        },
        buttonText: {
            type: String,
            default: 'Subscribe Now'
        },
        layout: {
            type: String,
            default: 'horizontal' // horizontal, vertical, inline
        },
        showPrivacy: {
            type: Boolean,
            default: true
        },
        privacyText: {
            type: String,
            default: 'By subscribing, you agree to our Privacy Policy. Unsubscribe anytime.'
        },
        apiEndpoint: {
            type: String,
            default: '/wp-json/yourstore-commerce/v1/newsletter/subscribe'
        },
        theme: {
            type: String,
            default: 'light' // light, dark, primary
        },
        showIcon: {
            type: Boolean,
            default: true
        },
        animation: {
            type: String,
            default: 'slide' // slide, fade, bounce
        }
    },
    
    setup(props) {
        // Form state
        const form = reactive({
            email: '',
            consent: false,
            honeypot: '' // Anti-spam honeypot field
        });
        
        // Component state
        const loading = ref(false);
        const submitted = ref(false);
        const error = ref(null);
        const success = ref(false);
        const isVisible = ref(false);
        
        // Form validation
        const emailError = ref(null);
        const formRef = ref(null);
        const emailInputRef = ref(null);
        const sectionRef = ref(null);
        
        // Animation states
        const showSuccessAnimation = ref(false);
        const intersectionObserver = ref(null);
        
        // Computed properties
        const isEmailValid = computed(() => {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return form.email.length === 0 || emailRegex.test(form.email);
        });
        
        const canSubmit = computed(() => {
            return form.email.length > 0 && isEmailValid.value && !loading.value;
        });
        
        const formClasses = computed(() => {
            const baseClasses = 'newsletter-signup-form';
            const layoutClasses = {
                horizontal: 'flex flex-col sm:flex-row gap-3',
                vertical: 'flex flex-col gap-4',
                inline: 'flex gap-2'
            };
            const themeClasses = {
                light: 'bg-white',
                dark: 'bg-secondary-900 text-white',
                primary: 'bg-gradient-primary text-white'
            };
            
            return `${baseClasses} ${layoutClasses[props.layout] || layoutClasses.horizontal} ${themeClasses[props.theme] || ''}`;
        });
        
        const inputClasses = computed(() => {
            const baseClasses = 'flex-1 px-4 py-3 rounded-lg border transition-all duration-200 font-medium';
            const validationClasses = emailError.value 
                ? 'border-red-500 bg-red-50 text-red-900 placeholder-red-400 focus:border-red-500 focus:ring-red-200'
                : 'border-gray-300 bg-white text-secondary-700 placeholder-secondary-400 focus:border-primary focus:ring-primary/20';
            const themeClasses = {
                light: validationClasses,
                dark: 'bg-secondary-800 border-secondary-600 text-white placeholder-secondary-400 focus:border-primary',
                primary: 'bg-white/20 border-white/30 text-white placeholder-white/60 focus:border-white'
            };
            
            return `${baseClasses} ${themeClasses[props.theme] || themeClasses.light}`;
        });
        
        const buttonClasses = computed(() => {
            const baseClasses = 'px-6 py-3 rounded-lg font-semibold transition-all duration-200 whitespace-nowrap disabled:opacity-50 disabled:cursor-not-allowed';
            const themeClasses = {
                light: 'bg-primary hover:bg-primary-600 text-white shadow-sm hover:shadow-md',
                dark: 'bg-primary hover:bg-primary-600 text-white shadow-sm hover:shadow-md',
                primary: 'bg-white text-primary hover:bg-gray-100 shadow-sm hover:shadow-md'
            };
            
            return `${baseClasses} ${themeClasses[props.theme] || themeClasses.light}`;
        });
        
        // Methods
        const validateEmail = () => {
            if (form.email.length === 0) {
                emailError.value = 'Email address is required';
                return false;
            }
            
            if (!isEmailValid.value) {
                emailError.value = 'Please enter a valid email address';
                return false;
            }
            
            emailError.value = null;
            return true;
        };
        
        const handleEmailInput = () => {
            if (emailError.value) {
                validateEmail();
            }
        };
        
        const handleSubmit = async (event) => {
            event.preventDefault();
            
            // Validate form
            if (!validateEmail()) {
                emailInputRef.value?.focus();
                return;
            }
            
            // Check honeypot (anti-spam)
            if (form.honeypot) {
                console.warn('Spam submission detected');
                return;
            }
            
            loading.value = true;
            error.value = null;
            
            try {
                const response = await submitNewsletter();
                
                if (response.success) {
                    success.value = true;
                    submitted.value = true;
                    showSuccessAnimation.value = true;
                    
                    // Track subscription event
                    if (typeof gtag !== 'undefined') {
                        gtag('event', 'newsletter_signup', {
                            event_category: 'engagement',
                            event_label: 'newsletter',
                            email_domain: form.email.split('@')[1]
                        });
                    }
                    
                    // Reset form after success animation
                    setTimeout(() => {
                        form.email = '';
                        showSuccessAnimation.value = false;
                    }, 3000);
                    
                } else {
                    throw new Error(response.message || 'Subscription failed');
                }
            } catch (err) {
                console.error('Newsletter submission error:', err);
                error.value = err.message || 'Failed to subscribe. Please try again.';
                
                // Show error for 5 seconds then clear
                setTimeout(() => {
                    error.value = null;
                }, 5000);
            } finally {
                loading.value = false;
            }
        };
        
        const submitNewsletter = async () => {
            const endpoint = props.apiEndpoint;
            const nonce = window.yourStoreCommerce?.nonce || '';
            
            const body = new FormData();
            body.append('email', form.email);
            body.append('consent', form.consent ? '1' : '0');
            body.append('nonce', nonce);
            body.append('source', 'newsletter_component');
            body.append('url', window.location.href);
            
            const response = await fetch(endpoint, {
                method: 'POST',
                body: body,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            return await response.json();
        };
        
        const setupIntersectionObserver = () => {
            intersectionObserver.value = new IntersectionObserver(
                (entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            setTimeout(() => {
                                isVisible.value = true;
                            }, 100);
                        }
                    });
                },
                { threshold: 0.3 }
            );
            
            if (sectionRef.value) {
                intersectionObserver.value.observe(sectionRef.value);
            }
        };
        
        const handleKeyPress = (event) => {
            if (event.key === 'Enter') {
                handleSubmit(event);
            }
        };
        
        const resetForm = () => {
            form.email = '';
            form.consent = false;
            emailError.value = null;
            error.value = null;
            success.value = false;
            submitted.value = false;
            showSuccessAnimation.value = false;
        };
        
        // Lifecycle
        onMounted(() => {
            setupIntersectionObserver();
            
            // Auto-focus email input if visible (disabled to prevent auto-scroll)
            // Note: Auto-focus was causing page to scroll to newsletter section on load
            // if (emailInputRef.value && window.innerWidth > 768) {
            //     setTimeout(() => {
            //         emailInputRef.value.focus();
            //     }, 1000);
            // }
        });
        
        onUnmounted(() => {
            if (intersectionObserver.value) {
                intersectionObserver.value.disconnect();
            }
        });
        
        return {
            form,
            loading,
            submitted,
            error,
            success,
            isVisible,
            emailError,
            formRef,
            emailInputRef,
            sectionRef,
            showSuccessAnimation,
            isEmailValid,
            canSubmit,
            formClasses,
            inputClasses,
            buttonClasses,
            validateEmail,
            handleEmailInput,
            handleSubmit,
            handleKeyPress,
            resetForm
        };
    },
    
    template: `
        <section ref="sectionRef" class="newsletter-signup-section py-16" :class="theme === 'dark' ? 'bg-secondary-900' : theme === 'primary' ? 'bg-gradient-primary' : 'bg-secondary-50'">
            <div class="container mx-auto px-4">
                
                <!-- Header -->
                <div class="text-center mb-8 max-w-2xl mx-auto">
                    <div 
                        class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium mb-4"
                        :class="{
                            'bg-primary/10 text-primary': theme === 'light',
                            'bg-white/20 text-white': theme === 'dark' || theme === 'primary',
                            'opacity-0 translate-y-4': !isVisible,
                            'opacity-100 translate-y-0': isVisible,
                            'transition-all duration-700 ease-out': true
                        }"
                    >
                        <i class="fas fa-envelope mr-2" v-if="showIcon"></i>
                        Newsletter
                    </div>
                    
                    <h2 
                        class="text-3xl sm:text-4xl font-bold mb-4"
                        :class="{
                            'text-secondary-900': theme === 'light',
                            'text-white': theme === 'dark' || theme === 'primary',
                            'opacity-0 translate-y-8': !isVisible,
                            'opacity-100 translate-y-0': isVisible,
                            'transition-all duration-1000 ease-out delay-200': true
                        }"
                        v-html="title"
                    ></h2>
                    
                    <p 
                        class="text-lg"
                        :class="{
                            'text-secondary-600': theme === 'light',
                            'text-secondary-300': theme === 'dark',
                            'text-primary-100': theme === 'primary',
                            'opacity-0 translate-y-8': !isVisible,
                            'opacity-100 translate-y-0': isVisible,
                            'transition-all duration-1000 ease-out delay-400': true
                        }"
                        v-html="subtitle"
                    ></p>
                </div>
                
                <!-- Success State -->
                <div 
                    v-if="success && showSuccessAnimation"
                    class="max-w-md mx-auto text-center"
                    :class="{
                        'opacity-0 scale-95': !showSuccessAnimation,
                        'opacity-100 scale-100': showSuccessAnimation,
                        'transition-all duration-500 ease-out': true
                    }"
                >
                    <div class="mb-6">
                        <div 
                            class="w-20 h-20 mx-auto rounded-full flex items-center justify-center animate-bounce"
                            :class="theme === 'primary' ? 'bg-white text-primary' : 'bg-green-500 text-white'"
                        >
                            <i class="fas fa-check text-3xl"></i>
                        </div>
                    </div>
                    
                    <h3 class="text-2xl font-bold mb-2" :class="theme === 'light' ? 'text-secondary-900' : 'text-white'">
                        Thanks for Subscribing!
                    </h3>
                    <p class="text-lg mb-6" :class="theme === 'light' ? 'text-secondary-600' : theme === 'dark' ? 'text-secondary-300' : 'text-primary-100'">
                        We've sent a confirmation email to <strong>{{ form.email }}</strong>
                    </p>
                    
                    <button 
                        @click="resetForm" 
                        class="text-sm underline hover:no-underline transition-all duration-200"
                        :class="theme === 'light' ? 'text-primary hover:text-primary-600' : 'text-white hover:text-primary-200'"
                    >
                        Subscribe another email
                    </button>
                </div>
                
                <!-- Form -->
                <div 
                    v-else
                    class="max-w-lg mx-auto"
                    :class="{
                        'opacity-0 translate-y-8': !isVisible,
                        'opacity-100 translate-y-0': isVisible,
                        'transition-all duration-1000 ease-out delay-600': true
                    }"
                >
                    <form ref="formRef" @submit="handleSubmit" :class="formClasses">
                        
                        <!-- Email Input -->
                        <div class="relative" :class="layout === 'inline' ? 'flex-1' : ''">
                            <input
                                ref="emailInputRef"
                                v-model="form.email"
                                @input="handleEmailInput"
                                @keypress="handleKeyPress"
                                type="email"
                                :placeholder="placeholder"
                                :class="inputClasses"
                                :disabled="loading"
                                required
                            />
                            
                            <!-- Input Icon -->
                            <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                <i 
                                    v-if="!loading && !emailError" 
                                    class="fas fa-envelope text-secondary-400"
                                    :class="theme !== 'light' ? 'text-white/60' : ''"
                                ></i>
                                <i 
                                    v-if="emailError" 
                                    class="fas fa-exclamation-circle text-red-500"
                                ></i>
                                <div v-if="loading" class="animate-spin">
                                    <i class="fas fa-spinner text-secondary-400"></i>
                                </div>
                            </div>
                            
                            <!-- Error Message -->
                            <div 
                                v-if="emailError" 
                                class="absolute left-0 mt-1 text-sm text-red-600 animate-slideInUp"
                            >
                                {{ emailError }}
                            </div>
                        </div>
                        
                        <!-- Submit Button -->
                        <button
                            type="submit"
                            :disabled="!canSubmit"
                            :class="buttonClasses"
                        >
                            <span v-if="!loading">
                                {{ buttonText }}
                                <i class="fas fa-paper-plane ml-2"></i>
                            </span>
                            <span v-else class="flex items-center">
                                <i class="fas fa-spinner animate-spin mr-2"></i>
                                Subscribing...
                            </span>
                        </button>
                        
                        <!-- Honeypot (hidden anti-spam field) -->
                        <input 
                            v-model="form.honeypot"
                            type="text" 
                            name="website" 
                            tabindex="-1" 
                            autocomplete="off"
                            class="absolute left-[-9999px] opacity-0 pointer-events-none"
                        />
                    </form>
                    
                    <!-- Privacy Notice -->
                    <div v-if="showPrivacy" class="text-center mt-4">
                        <p 
                            class="text-sm"
                            :class="theme === 'light' ? 'text-secondary-500' : theme === 'dark' ? 'text-secondary-400' : 'text-primary-100'"
                            v-html="privacyText"
                        ></p>
                    </div>
                    
                    <!-- Error Message -->
                    <div 
                        v-if="error" 
                        class="mt-4 p-3 bg-red-100 border border-red-300 rounded-lg text-red-700 text-sm animate-slideInUp"
                        role="alert"
                    >
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        {{ error }}
                    </div>
                </div>
                
            </div>
        </section>
    `
};

        // Make component globally accessible
        console.log('YourStore: NewsletterSignup component definition:', NewsletterSignup);
        console.log('YourStore: Setting window.NewsletterSignup...');
        window.NewsletterSignup = NewsletterSignup;
        console.log('YourStore: window.NewsletterSignup is now set to:', window.NewsletterSignup);

        // Auto-mount component if container exists (legacy support)
        document.addEventListener('DOMContentLoaded', () => {
            const containers = document.querySelectorAll('.yourstore-newsletter-signup');
            containers.forEach(container => {
                // Skip if already mounted by app.js or has Vue app
                if (container.__vue_app || container.querySelector('[data-vue-app]')) {
                    return;
                }
                
                let props = {};
                
                // Handle hybrid component format (data-props)
                if (container.dataset.props) {
                    try {
                        props = JSON.parse(container.dataset.props);
                    } catch (e) {
                        console.warn('YourStore: Invalid JSON in data-props:', e);
                    }
                } 
                // Handle legacy format (individual data attributes)
                else {
                    if (container.dataset.title) props.title = container.dataset.title;
                    if (container.dataset.subtitle) props.subtitle = container.dataset.subtitle;
                    if (container.dataset.placeholder) props.placeholder = container.dataset.placeholder;
                    if (container.dataset.buttonText) props.buttonText = container.dataset.buttonText;
                    if (container.dataset.layout) props.layout = container.dataset.layout;
                    if (container.dataset.showPrivacy) props.showPrivacy = container.dataset.showPrivacy === 'true';
                    if (container.dataset.privacyText) props.privacyText = container.dataset.privacyText;
                    if (container.dataset.apiEndpoint) props.apiEndpoint = container.dataset.apiEndpoint;
                    if (container.dataset.theme) props.theme = container.dataset.theme;
                    if (container.dataset.showIcon) props.showIcon = container.dataset.showIcon === 'true';
                    if (container.dataset.animation) props.animation = container.dataset.animation;
                }
                
                const app = Vue.createApp(NewsletterSignup, props);
                container.__vue_app = app.mount(container);
            });
        });

        // Export for different module systems
        if (typeof window !== 'undefined') {
            window.NewsletterSignup = NewsletterSignup;
            console.log('YourStore: NewsletterSignup component registered on window object');
        } else if (typeof module !== 'undefined' && module.exports) {
            module.exports = NewsletterSignup;
        }
    }
    
    // Initialize the component
    initNewsletterSignup();
    
})();
/**
 * YourStore Commerce - Vue.js Application Initializer
 * Handles Vue component mounting and global app state
 */

(function() {
    'use strict';
    
    // Wait for DOM and Vue.js to be ready
    function initVueApp() {
        if (typeof Vue === 'undefined') {
            console.warn('Vue.js not loaded, retrying in 100ms...');
            setTimeout(initVueApp, 100);
            return;
        }
        
        const { ref, reactive, computed, onMounted, nextTick } = Vue;
        
        // Global app state
        const globalState = reactive({
            cartCount: 0,
            wishlistCount: 0,
            isLoading: false,
            notifications: []
        });
        
        // Initialize all Vue components on the page
        function mountComponents() {
            // Register all components globally for easier sharing
            const sharedComponents = {
                'product-card': window.ProductCard
            };
            
            // Hero Section Components
            document.querySelectorAll('.yourstore-hero-section').forEach(element => {
                if (!element.__vue_app) {
                    let props = {};
                    
                    // Handle new hybrid component format (data-props)
                    if (element.dataset.props) {
                        try {
                            props = JSON.parse(element.dataset.props);
                            console.log('YourStore: Mounting Hero with hybrid props:', props);
                        } catch (e) {
                            console.warn('YourStore: Invalid JSON in hero data-props:', e);
                            props = {};
                        }
                    } 
                    // Handle legacy block format (individual data attributes)
                    else {
                        props = {
                            title: element.dataset.title || 'Welcome to YourStore',
                            subtitle: element.dataset.subtitle || 'Discover amazing products',
                            ctaText: element.dataset.ctaText || 'Shop Now',
                            ctaUrl: element.dataset.ctaUrl || '/shop',
                            backgroundImage: element.dataset.backgroundImage || '',
                            overlayOpacity: parseFloat(element.dataset.overlayOpacity) || 0.5,
                            textAlignment: element.dataset.textAlignment || 'center',
                            height: element.dataset.height || 'auto',
                            showScrollIndicator: element.dataset.showScrollIndicator !== 'false',
                            enableParallax: element.dataset.enableParallax !== 'false',
                            componentId: element.dataset.componentId || 'hero-' + Date.now()
                        };
                        console.log('YourStore: Mounting Hero with legacy props:', props);
                    }
                    
                    if (window.HeroSection) {
                        const app = Vue.createApp(window.HeroSection, props);
                        
                        // Register shared components
                        Object.entries(sharedComponents).forEach(([name, component]) => {
                            if (component) app.component(name, component);
                        });
                        
                        app.provide('globalState', globalState);
                        element.__vue_app = app.mount(element);
                        console.log('YourStore: Hero Section mounted successfully');
                    } else {
                        console.warn('YourStore: HeroSection component not found on window object');
                    }
                }
            });
            
            // Featured Products Components
            document.querySelectorAll('.yourstore-featured-products').forEach(element => {
                if (!element.__vue_app) {
                    let props = {};
                    
                    // Handle new hybrid component format (data-props)
                    if (element.dataset.props) {
                        props = JSON.parse(element.dataset.props);
                    } 
                    // Handle legacy block format (individual data attributes)
                    else {
                        props = {
                            title: element.dataset.title || 'Featured Products',
                            subtitle: element.dataset.subtitle || 'Hand-picked selections from our team',
                            limit: parseInt(element.dataset.limit) || 8,
                            columns: parseInt(element.dataset.columns) || 4,
                            show_view_all: element.dataset.showViewAll !== 'false',
                            view_all_url: element.dataset.viewAllUrl || '/shop',
                            component_id: element.dataset.componentId || 'featured-' + Date.now()
                        };
                    }
                    
                    const app = Vue.createApp(window.FeaturedProducts, props);
                    
                    // Register shared components
                    Object.entries(sharedComponents).forEach(([name, component]) => {
                        if (component) app.component(name, component);
                    });
                    
                    app.provide('globalState', globalState);
                    element.__vue_app = app.mount(element);
                }
            });
            
            // Category Showcase Components
            document.querySelectorAll('.yourstore-category-showcase').forEach(element => {
                if (!element.__vue_app) {
                    let props = {};
                    
                    // Handle new hybrid component format (data-props)
                    if (element.dataset.props) {
                        props = JSON.parse(element.dataset.props);
                    } 
                    // Handle legacy block format (individual data attributes)
                    else {
                        props = {
                            title: element.dataset.title || 'Shop by Category',
                            subtitle: element.dataset.subtitle || 'Explore our diverse product categories',
                            layout: element.dataset.layout || 'grid',
                            columns: parseInt(element.dataset.columns) || 4,
                            component_id: element.dataset.componentId || 'category-' + Date.now()
                        };
                    }
                    
                    const app = Vue.createApp(window.CategoryShowcase, props);
                    
                    // Register shared components
                    Object.entries(sharedComponents).forEach(([name, component]) => {
                        if (component) app.component(name, component);
                    });
                    
                    app.provide('globalState', globalState);
                    element.__vue_app = app.mount(element);
                }
            });
            
            // Newsletter Signup Components
            document.querySelectorAll('.yourstore-newsletter-signup').forEach(element => {
                if (!element.__vue_app) {
                    let props = {};
                    
                    // Handle new hybrid component format (data-props)
                    if (element.dataset.props) {
                        props = JSON.parse(element.dataset.props);
                    } 
                    // Handle legacy block format (individual data attributes)
                    else {
                        props = {
                            title: element.dataset.title || 'Stay in the Loop',
                            subtitle: element.dataset.subtitle || 'Get the latest updates delivered to your inbox',
                            placeholder: element.dataset.placeholder || 'Enter your email address',
                            button_text: element.dataset.buttonText || 'Subscribe Now',
                            layout: element.dataset.layout || 'horizontal',
                            component_id: element.dataset.componentId || 'newsletter-' + Date.now()
                        };
                    }
                    
                    const app = Vue.createApp(window.NewsletterSignup, props);
                    
                    // Register shared components
                    Object.entries(sharedComponents).forEach(([name, component]) => {
                        if (component) app.component(name, component);
                    });
                    
                    app.provide('globalState', globalState);
                    element.__vue_app = app.mount(element);
                }
            });
            
            // Site Footer Components
            document.querySelectorAll('.yourstore-site-footer').forEach(element => {
                if (!element.__vue_app) {
                    let props = {};
                    
                    // Handle new hybrid component format (data-props)
                    if (element.dataset.props) {
                        props = JSON.parse(element.dataset.props);
                    } 
                    // Handle legacy block format (individual data attributes)
                    else {
                        props = {
                            company_name: element.dataset.companyName || 'YourStore',
                            tagline: element.dataset.tagline || 'Your trusted shopping destination',
                            show_social_links: element.dataset.showSocialLinks !== 'false',
                            show_newsletter: element.dataset.showNewsletter !== 'false',
                            copyright_year: element.dataset.copyrightYear || new Date().getFullYear(),
                            component_id: element.dataset.componentId || 'footer-' + Date.now()
                        };
                    }
                    
                    const app = Vue.createApp(window.SiteFooter, props);
                    
                    // Register shared components
                    Object.entries(sharedComponents).forEach(([name, component]) => {
                        if (component) app.component(name, component);
                    });
                    
                    app.provide('globalState', globalState);
                    element.__vue_app = app.mount(element);
                }
            });
            
            // Product Grid Components (if present)
            document.querySelectorAll('.yourstore-product-grid').forEach(element => {
                if (!element.__vue_app && window.ProductGrid) {
                    const props = JSON.parse(element.dataset.props || '{}');
                    const app = Vue.createApp(window.ProductGrid, props);
                    
                    // Register shared components
                    Object.entries(sharedComponents).forEach(([name, component]) => {
                        if (component) app.component(name, component);
                    });
                    
                    app.provide('globalState', globalState);
                    element.__vue_app = app.mount(element);
                }
            });
            
            // Product Catalog Components
            document.querySelectorAll('.yourstore-product-catalog').forEach(element => {
                if (!element.__vue_app && window.ProductCatalog) {
                    // Extract props from data attributes
                    const props = {
                        title: element.dataset.title || 'Our Products',
                        subtitle: element.dataset.subtitle || 'Discover our complete collection',
                        showFilters: element.dataset.showFilters === 'true',
                        showSort: element.dataset.showSort === 'true',
                        showPagination: element.dataset.showPagination === 'true',
                        productsPerPage: parseInt(element.dataset.productsPerPage) || 12,
                        defaultCategory: element.dataset.defaultCategory || ''
                    };
                    const app = Vue.createApp(window.ProductCatalog, props);
                    
                    // Register shared components
                    Object.entries(sharedComponents).forEach(([name, component]) => {
                        if (component) app.component(name, component);
                    });
                    
                    app.provide('globalState', globalState);
                    element.__vue_app = app.mount(element);
                }
            });
            
            console.log('YourStore Commerce: All Vue components mounted successfully');
        }
        
        // Check if all components are loaded
        function waitForComponents() {
            const requiredComponents = [
                'HeroSection',
                'FeaturedProducts',
                'ProductGrid',
                'CategoryShowcase',
                'NewsletterSignup',
                'SiteFooter'
            ];
            
            const loadedComponents = requiredComponents.filter(comp => window[comp]);
            
            console.log('YourStore: Component loading check:', {
                required: requiredComponents,
                loaded: loadedComponents,
                missing: requiredComponents.filter(comp => !window[comp]),
                windowKeys: Object.keys(window).filter(key => key.includes('Section') || key.includes('Products') || key.includes('Showcase') || key.includes('Newsletter') || key.includes('Footer'))
            });
            
            if (loadedComponents.length === requiredComponents.length) {
                console.log('YourStore: All components loaded, proceeding to mount');
                mountComponents();
            } else {
                const missing = requiredComponents.filter(comp => !window[comp]);
                console.warn(`YourStore: Still waiting for components: ${missing.join(', ')}`);
                console.log('YourStore: Current window object analysis:', {
                    Vue: typeof Vue,
                    HeroSection: typeof window.HeroSection,
                    FeaturedProducts: typeof window.FeaturedProducts,
                    CategoryShowcase: typeof window.CategoryShowcase,
                    NewsletterSignup: typeof window.NewsletterSignup,
                    SiteFooter: typeof window.SiteFooter
                });
                setTimeout(waitForComponents, 500); // Increased timeout for better debugging
            }
        }
        
        // Start component loading check
        waitForComponents();
        
        // Global utility functions
        window.YourStoreVue = {
            globalState,
            remountComponent: function(selector) {
                const elements = document.querySelectorAll(selector);
                elements.forEach(element => {
                    if (element.__vue_app) {
                        element.__vue_app.unmount();
                        element.__vue_app = null;
                    }
                });
                mountComponents();
            },
            updateGlobalState: function(updates) {
                Object.assign(globalState, updates);
            }
        };
    }
    
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initVueApp);
    } else {
        initVueApp();
    }
    
})();
<?php
/**
 * Plugin Name: YourStore Commerce
 * Plugin URI: https://yourstore.com
 * Description: Professional WooCommerce-based ecommerce platform with Vue.js CDN frontend components integrated via WordPress blocks and plugins.
 * Version: 1.0.0
 * Author: YourStore Team
 * Author URI: https://yourstore.com
 * Text Domain: yourstore-commerce
 * Domain Path: /languages
 * Requires at least: 6.5
 * Tested up to: 6.8
 * Requires PHP: 8.1
 * WC requires at least: 8.0
 * WC tested up to: 9.0
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 *
 * @package YourStore\Commerce
 */

declare(strict_types=1);

namespace YourStore\Commerce;

use YourStore\Commerce\Data;

// Prevent direct access
defined('ABSPATH') || exit;

// Define plugin constants
define('YOURSTORE_COMMERCE_VERSION', '1.0.0');
define('YOURSTORE_COMMERCE_PLUGIN_FILE', __FILE__);
define('YOURSTORE_COMMERCE_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('YOURSTORE_COMMERCE_PLUGIN_URL', plugin_dir_url(__FILE__));
define('YOURSTORE_COMMERCE_PLUGIN_BASENAME', plugin_basename(__FILE__));

// Autoloader
require_once YOURSTORE_COMMERCE_PLUGIN_DIR . 'includes/class-autoloader.php';

// Manual includes for critical classes to ensure they load

// Base abstract classes
require_once YOURSTORE_COMMERCE_PLUGIN_DIR . 'includes/abstracts/class-base-component.php';

// Hybrid components (blocks + shortcodes)
require_once YOURSTORE_COMMERCE_PLUGIN_DIR . 'includes/components/class-hero-section-component.php';
require_once YOURSTORE_COMMERCE_PLUGIN_DIR . 'includes/components/class-product-grid-component.php';
require_once YOURSTORE_COMMERCE_PLUGIN_DIR . 'includes/components/class-featured-products-component.php';

// Legacy blocks (will be converted progressively)
require_once YOURSTORE_COMMERCE_PLUGIN_DIR . 'includes/blocks/class-product-grid-block.php';
require_once YOURSTORE_COMMERCE_PLUGIN_DIR . 'includes/blocks/class-hero-section-block.php';
require_once YOURSTORE_COMMERCE_PLUGIN_DIR . 'includes/blocks/class-featured-products-block.php';
require_once YOURSTORE_COMMERCE_PLUGIN_DIR . 'includes/blocks/class-category-showcase-block.php';
require_once YOURSTORE_COMMERCE_PLUGIN_DIR . 'includes/blocks/class-newsletter-signup-block.php';
require_once YOURSTORE_COMMERCE_PLUGIN_DIR . 'includes/blocks/class-site-footer-block.php';
require_once YOURSTORE_COMMERCE_PLUGIN_DIR . 'includes/blocks/class-blocks-manager.php';
require_once YOURSTORE_COMMERCE_PLUGIN_DIR . 'includes/api/class-api-manager.php';
require_once YOURSTORE_COMMERCE_PLUGIN_DIR . 'includes/admin/class-admin-manager.php';
require_once YOURSTORE_COMMERCE_PLUGIN_DIR . 'includes/admin/class-mock-data-admin.php';
require_once YOURSTORE_COMMERCE_PLUGIN_DIR . 'includes/data/class-data-service.php';
require_once YOURSTORE_COMMERCE_PLUGIN_DIR . 'includes/data/class-mock-data-manager.php';
require_once YOURSTORE_COMMERCE_PLUGIN_DIR . 'includes/data/class-product-seeder.php';
require_once YOURSTORE_COMMERCE_PLUGIN_DIR . 'includes/class-api-service.php';

/**
 * Main plugin class
 */
final class YourStoreCommerce {
    
    /**
     * Plugin instance
     *
     * @var YourStoreCommerce|null
     */
    private static ?YourStoreCommerce $instance = null;
    
    /**
     * Get plugin instance
     *
     * @return YourStoreCommerce
     */
    public static function instance(): YourStoreCommerce {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     *
     * @return void
     */
    private function init_hooks(): void {
        add_action('plugins_loaded', [$this, 'init'], 10);
        add_action('init', [$this, 'load_textdomain'], 5);
        
        // Activation/Deactivation hooks
        register_activation_hook(__FILE__, [$this, 'activate']);
        register_deactivation_hook(__FILE__, [$this, 'deactivate']);
    }
    
    /**
     * Initialize plugin
     *
     * @return void
     */
    public function init(): void {
        // Check dependencies
        if (!$this->check_dependencies()) {
            return;
        }
        
        // Initialize components
        $this->init_components();
        
        // Load assets
        add_action('wp_enqueue_scripts', [$this, 'enqueue_frontend_scripts']);
        add_action('admin_enqueue_scripts', [$this, 'enqueue_admin_scripts']);
    }
    
    /**
     * Check plugin dependencies
     *
     * @return bool
     */
    private function check_dependencies(): bool {
        $missing_dependencies = [];
        
        // Check WooCommerce
        if (!class_exists('WooCommerce')) {
            $missing_dependencies[] = 'WooCommerce';
        }
        
        // Check WPGraphQL
        if (!class_exists('WPGraphQL')) {
            $missing_dependencies[] = 'WPGraphQL';
        }
        
        if (!empty($missing_dependencies)) {
            add_action('admin_notices', function() use ($missing_dependencies) {
                $message = sprintf(
                    /* translators: %s: comma-separated list of missing plugins */
                    esc_html__('YourStore Commerce requires the following plugins: %s', 'yourstore-commerce'),
                    implode(', ', $missing_dependencies)
                );
                
                printf('<div class="notice notice-error"><p>%s</p></div>', esc_html($message));
            });
            
            return false;
        }
        
        return true;
    }
    
    /**
     * Initialize plugin components
     *
     * @return void
     */
    private function init_components(): void {
        // Initialize blocks
        new Blocks\BlocksManager();
        
        // Initialize API endpoints
        new Api\ApiManager();
        
        // Initialize WooCommerce API Service
        new ApiService();
        
        // Initialize admin functionality
        if (is_admin()) {
            new Admin\AdminManager();
        }
        
        // Register shortcodes as backup
        $this->register_shortcodes();
    }
    
    /**
     * Register shortcodes for Vue components
     */
    private function register_shortcodes(): void {
        add_shortcode('yourstore_hero', function($atts) {
            $atts = shortcode_atts([
                'title' => 'Welcome to YourStore',
                'subtitle' => 'Discover amazing products at great prices',
                'cta_text' => 'Shop Now',
                'cta_url' => '/shop',
                'background_image' => '',
                'overlay_opacity' => 0.5,
            ], $atts);
            
            return sprintf(
                '<div class="yourstore-hero-section" data-title="%s" data-subtitle="%s" data-cta-text="%s" data-cta-url="%s" data-background-image="%s" data-overlay-opacity="%s"></div>',
                esc_attr($atts['title']),
                esc_attr($atts['subtitle']),
                esc_attr($atts['cta_text']),
                esc_attr($atts['cta_url']),
                esc_attr($atts['background_image']),
                esc_attr($atts['overlay_opacity'])
            );
        });
        
        add_shortcode('yourstore_featured_products', function($atts) {
            $atts = shortcode_atts([
                'title' => 'Featured Products',
                'subtitle' => 'Hand-picked selections from our team',
                'limit' => 8,
                'columns' => 4,
                'show_view_all' => 'true',
                'view_all_url' => '/shop',
            ], $atts);
            
            return sprintf(
                '<div class="yourstore-featured-products" data-title="%s" data-subtitle="%s" data-limit="%s" data-columns="%s" data-show-view-all="%s" data-view-all-url="%s"></div>',
                esc_attr($atts['title']),
                esc_attr($atts['subtitle']),
                esc_attr($atts['limit']),
                esc_attr($atts['columns']),
                esc_attr($atts['show_view_all']),
                esc_attr($atts['view_all_url'])
            );
        });
        
        add_shortcode('yourstore_category_showcase', function($atts) {
            $atts = shortcode_atts([
                'title' => 'Shop by Category',
                'subtitle' => 'Explore our diverse product categories',
                'layout' => 'grid',
                'columns' => 4,
            ], $atts);
            
            return sprintf(
                '<div class="yourstore-category-showcase" data-title="%s" data-subtitle="%s" data-layout="%s" data-columns="%s"></div>',
                esc_attr($atts['title']),
                esc_attr($atts['subtitle']),
                esc_attr($atts['layout']),
                esc_attr($atts['columns'])
            );
        });
        
        add_shortcode('yourstore_newsletter', function($atts) {
            $atts = shortcode_atts([
                'title' => 'Stay in the Loop',
                'subtitle' => 'Get the latest updates delivered to your inbox',
                'placeholder' => 'Enter your email address',
                'button_text' => 'Subscribe Now',
                'layout' => 'horizontal',
            ], $atts);
            
            return sprintf(
                '<div class="yourstore-newsletter-signup" data-title="%s" data-subtitle="%s" data-placeholder="%s" data-button-text="%s" data-layout="%s"></div>',
                esc_attr($atts['title']),
                esc_attr($atts['subtitle']),
                esc_attr($atts['placeholder']),
                esc_attr($atts['button_text']),
                esc_attr($atts['layout'])
            );
        });
        
        add_shortcode('yourstore_product_catalog', function($atts) {
            $atts = shortcode_atts([
                'title' => 'Our Products',
                'subtitle' => 'Discover our complete collection',
                'show_filters' => 'true',
                'show_sort' => 'true',
                'show_pagination' => 'true',
                'show_search' => 'true',
                'show_view_toggle' => 'true',
                'products_per_page' => '12',
                'default_category' => '',
                'grid_columns' => '4',
            ], $atts);
            
            return sprintf(
                '<div class="yourstore-product-catalog" data-title="%s" data-subtitle="%s" data-show-filters="%s" data-show-sort="%s" data-show-pagination="%s" data-show-search="%s" data-show-view-toggle="%s" data-products-per-page="%s" data-default-category="%s" data-grid-columns="%s"></div>',
                esc_attr($atts['title']),
                esc_attr($atts['subtitle']),
                esc_attr($atts['show_filters']),
                esc_attr($atts['show_sort']),
                esc_attr($atts['show_pagination']),
                esc_attr($atts['show_search']),
                esc_attr($atts['show_view_toggle']),
                esc_attr($atts['products_per_page']),
                esc_attr($atts['default_category']),
                esc_attr($atts['grid_columns'])
            );
        });
        
        // Add shortcode for seeding products easily
        add_shortcode('seed_mock_products', function() {
            if (!current_user_can('manage_woocommerce')) {
                return '<p style="color: red;">You need WooCommerce management permissions to seed products.</p>';
            }
            
            try {
                // Check if products already exist
                $existing_products = get_posts([
                    'post_type' => 'product',
                    'post_status' => 'publish',
                    'numberposts' => 1
                ]);
                
                if (!empty($existing_products)) {
                    return '<div style="background: orange; color: white; padding: 20px; margin: 10px;">
                        <h3>Products Already Exist!</h3>
                        <p>Your WooCommerce store already has products. Would you like to clear them first?</p>
                        <p>Use shortcode: <code>[clear_mock_products]</code> first, then run this again.</p>
                    </div>';
                }
                
                $seeder = new \YourStore\Commerce\Data\ProductSeeder();
                $results = $seeder->seed();
                
                return '<div style="background: green; color: white; padding: 20px; margin: 10px;">
                    <h3>🎉 Mock Products Seeded Successfully!</h3>
                    <p><strong>' . $results['products_created'] . '</strong> products created</p>
                    <p><strong>' . $results['categories_created'] . '</strong> categories created</p>
                    <p><strong>Next Steps:</strong></p>
                    <ul>
                        <li>✅ Check WooCommerce → Products in admin</li>
                        <li>✅ View your shop page to see real products</li>
                        <li>✅ Homepage will now show real WooCommerce data</li>
                    </ul>
                </div>';
            } catch (Exception $e) {
                return '<div style="background: red; color: white; padding: 20px; margin: 10px;">
                    <h3>❌ Error seeding products:</h3>
                    <p>' . esc_html($e->getMessage()) . '</p>
                    <p><strong>Troubleshooting:</strong></p>
                    <ul>
                        <li>Make sure WooCommerce is active</li>
                        <li>Check if you have admin permissions</li>
                        <li>Try refreshing and running again</li>
                    </ul>
                </div>';
            }
        });
        
        // Add shortcode to clear products if needed
        add_shortcode('clear_mock_products', function() {
            if (!current_user_can('manage_woocommerce')) {
                return '<p style="color: red;">You need WooCommerce management permissions.</p>';
            }
            
            try {
                // Delete all products
                $products = get_posts([
                    'post_type' => 'product',
                    'post_status' => 'any',
                    'numberposts' => -1
                ]);
                
                $deleted_count = 0;
                foreach ($products as $product) {
                    wp_delete_post($product->ID, true);
                    $deleted_count++;
                }
                
                return '<div style="background: blue; color: white; padding: 20px; margin: 10px;">
                    <h3>🧹 Products Cleared!</h3>
                    <p>Deleted <strong>' . $deleted_count . '</strong> products</p>
                    <p>Now you can run <code>[seed_mock_products]</code> to add fresh demo products.</p>
                </div>';
                
            } catch (Exception $e) {
                return '<div style="background: red; color: white; padding: 20px; margin: 10px;">
                    <h3>Error clearing products:</h3>
                    <p>' . esc_html($e->getMessage()) . '</p>
                </div>';
            }
        });
    }
    
    /**
     * Enqueue frontend scripts and styles
     *
     * @return void
     */
    public function enqueue_frontend_scripts(): void {
        // Debug: Add simple CSS to verify plugin is loading
        wp_add_inline_style('wp-block-library', '
            body:after { 
                content: "YourStore Commerce Plugin Loaded"; 
                position: fixed; 
                bottom: 10px; 
                right: 10px; 
                background: red; 
                color: white; 
                padding: 5px; 
                z-index: 9999; 
                font-size: 12px;
            }
        ');
        // Enqueue CDN resources
        wp_enqueue_script(
            'vue',
            'https://unpkg.com/vue@3/dist/vue.global.js',
            [],
            '3.4.0',
            true
        );
        
        wp_enqueue_script(
            'tailwindcss',
            'https://cdn.tailwindcss.com',
            [],
            '3.4.16',
            false // Load in head for CSS processing
        );
        
        wp_enqueue_style(
            'font-awesome',
            'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css',
            [],
            '6.0.0'
        );
        
        // Enqueue custom styles with cache busting
        wp_enqueue_style(
            'yourstore-commerce-styles',
            YOURSTORE_COMMERCE_PLUGIN_URL . 'assets/css/globals.css',
            [],
            YOURSTORE_COMMERCE_VERSION . '.' . time()
        );
        
        // Enqueue individual Vue.js components
        wp_enqueue_script(
            'yourstore-commerce-product-grid',
            YOURSTORE_COMMERCE_PLUGIN_URL . 'assets/js/components/product-grid.js',
            ['vue'],
            YOURSTORE_COMMERCE_VERSION . '.' . time(),
            true
        );
        
        wp_enqueue_script(
            'yourstore-commerce-hero-section',
            YOURSTORE_COMMERCE_PLUGIN_URL . 'assets/js/components/hero-section.js',
            ['vue'],
            YOURSTORE_COMMERCE_VERSION . '.' . time(),
            true
        );
        
        wp_enqueue_script(
            'yourstore-commerce-featured-products',
            YOURSTORE_COMMERCE_PLUGIN_URL . 'assets/js/components/featured-products.js',
            ['vue', 'yourstore-commerce-graphql-api'],
            YOURSTORE_COMMERCE_VERSION . '.' . time(),
            true
        );
        
        wp_enqueue_script(
            'yourstore-commerce-category-showcase',
            YOURSTORE_COMMERCE_PLUGIN_URL . 'assets/js/components/category-showcase.js',
            ['vue', 'yourstore-commerce-graphql-api'],
            YOURSTORE_COMMERCE_VERSION . '.' . time(),
            true
        );
        
        wp_enqueue_script(
            'yourstore-commerce-newsletter-signup',
            YOURSTORE_COMMERCE_PLUGIN_URL . 'assets/js/components/newsletter-signup.js',
            ['vue'],
            YOURSTORE_COMMERCE_VERSION . '.' . time(),
            true
        );
        
        wp_enqueue_script(
            'yourstore-commerce-site-footer',
            YOURSTORE_COMMERCE_PLUGIN_URL . 'assets/js/components/site-footer.js',
            ['vue'],
            YOURSTORE_COMMERCE_VERSION . '.' . time(),
            true
        );
        
        // Enqueue GraphQL API utility
        wp_enqueue_script(
            'yourstore-commerce-graphql-api',
            YOURSTORE_COMMERCE_PLUGIN_URL . 'assets/js/graphql-api.js',
            ['vue'],
            YOURSTORE_COMMERCE_VERSION . '.' . time(),
            true
        );
        
        // Enqueue GraphQL Utilities (authentication and advanced operations)
        wp_enqueue_script(
            'yourstore-commerce-graphql-utils',
            YOURSTORE_COMMERCE_PLUGIN_URL . 'assets/js/graphql-utils.js',
            ['yourstore-commerce-graphql-api'],
            YOURSTORE_COMMERCE_VERSION . '.' . time(),
            true
        );
        
        // Enqueue GraphQL Mutations and Queries
        wp_enqueue_script(
            'yourstore-commerce-graphql-mutations',
            YOURSTORE_COMMERCE_PLUGIN_URL . 'assets/js/graphql-mutations.js',
            ['yourstore-commerce-graphql-utils'],
            YOURSTORE_COMMERCE_VERSION . '.' . time(),
            true
        );
        
        // Enqueue Enhanced GraphQL Authentication System (TASK-025)
        wp_enqueue_script(
            'yourstore-commerce-graphql-auth-mutations',
            YOURSTORE_COMMERCE_PLUGIN_URL . 'assets/js/graphql-auth-mutations.js',
            ['yourstore-commerce-graphql-mutations'],
            YOURSTORE_COMMERCE_VERSION . '.' . time(),
            true
        );
        
        // Enqueue Cart Store (state management)
        wp_enqueue_script(
            'yourstore-commerce-cart-store',
            YOURSTORE_COMMERCE_PLUGIN_URL . 'assets/js/stores/cart-store.js',
            ['vue', 'yourstore-commerce-graphql-api'],
            YOURSTORE_COMMERCE_VERSION . '.' . time(),
            true
        );
        
        // Enqueue Auth Store (state management)
        wp_enqueue_script(
            'yourstore-commerce-auth-store',
            YOURSTORE_COMMERCE_PLUGIN_URL . 'assets/js/stores/auth-store.js',
            ['vue', 'yourstore-commerce-graphql-mutations'],
            YOURSTORE_COMMERCE_VERSION . '.' . time(),
            true
        );
        
        // Enqueue User Authentication Component
        wp_enqueue_script(
            'yourstore-commerce-user-authentication',
            YOURSTORE_COMMERCE_PLUGIN_URL . 'assets/js/components/user-authentication.js',
            ['vue', 'yourstore-commerce-auth-store'],
            YOURSTORE_COMMERCE_VERSION . '.' . time(),
            true
        );
        
        // Enqueue Header Auth Button Component
        wp_enqueue_script(
            'yourstore-commerce-header-auth-button',
            YOURSTORE_COMMERCE_PLUGIN_URL . 'assets/js/components/header-auth-button.js',
            ['vue', 'yourstore-commerce-auth-store', 'yourstore-commerce-user-authentication'],
            YOURSTORE_COMMERCE_VERSION . '.' . time(),
            true
        );
        
        // Enqueue Product Search Component
        wp_enqueue_script(
            'yourstore-commerce-product-search',
            YOURSTORE_COMMERCE_PLUGIN_URL . 'assets/js/components/product-search.js',
            ['vue', 'yourstore-commerce-graphql-mutations'],
            YOURSTORE_COMMERCE_VERSION . '.' . time(),
            true
        );
        
        // Enqueue Wishlist Store (state management)
        wp_enqueue_script(
            'yourstore-commerce-wishlist-store',
            YOURSTORE_COMMERCE_PLUGIN_URL . 'assets/js/stores/wishlist-store.js',
            ['vue', 'yourstore-commerce-graphql-mutations'],
            YOURSTORE_COMMERCE_VERSION . '.' . time(),
            true
        );
        
        // Enqueue Wishlist Component
        wp_enqueue_script(
            'yourstore-commerce-wishlist',
            YOURSTORE_COMMERCE_PLUGIN_URL . 'assets/js/components/wishlist.js',
            ['vue', 'yourstore-commerce-wishlist-store'],
            YOURSTORE_COMMERCE_VERSION . '.' . time(),
            true
        );
        
        // Enqueue Wishlist Button Component
        wp_enqueue_script(
            'yourstore-commerce-wishlist-button',
            YOURSTORE_COMMERCE_PLUGIN_URL . 'assets/js/components/wishlist-button.js',
            ['vue', 'yourstore-commerce-wishlist-store'],
            YOURSTORE_COMMERCE_VERSION . '.' . time(),
            true
        );
        
        // Enqueue Shopping Cart Component
        wp_enqueue_script(
            'yourstore-commerce-shopping-cart',
            YOURSTORE_COMMERCE_PLUGIN_URL . 'assets/js/components/shopping-cart.js',
            ['vue', 'yourstore-commerce-cart-store'],
            YOURSTORE_COMMERCE_VERSION . '.' . time(),
            true
        );
        
        wp_enqueue_script(
            'yourstore-commerce-product-catalog',
            YOURSTORE_COMMERCE_PLUGIN_URL . 'assets/js/components/product-catalog.js',
            ['vue', 'yourstore-commerce-api-utils', 'yourstore-commerce-graphql-api'],
            YOURSTORE_COMMERCE_VERSION . '.' . time(),
            true
        );
        
        // Enqueue Enhanced Product Card Component
        wp_enqueue_script(
            'yourstore-commerce-product-card',
            YOURSTORE_COMMERCE_PLUGIN_URL . 'assets/js/components/product-card.js',
            ['vue', 'yourstore-commerce-cart-store', 'yourstore-commerce-wishlist-store'],
            YOURSTORE_COMMERCE_VERSION . '.' . time(),
            true
        );
        
        // Enqueue API utilities
        wp_enqueue_script(
            'yourstore-commerce-api-utils',
            YOURSTORE_COMMERCE_PLUGIN_URL . 'assets/js/api-utils.js',
            [],
            YOURSTORE_COMMERCE_VERSION . '.' . time(),
            true
        );
        
        // Enqueue header functionality
        wp_enqueue_script(
            'yourstore-commerce-header',
            YOURSTORE_COMMERCE_PLUGIN_URL . 'assets/js/header.js',
            ['yourstore-commerce-api-utils'],
            YOURSTORE_COMMERCE_VERSION . '.' . time(),
            true
        );
        
        // Enqueue Vue app initializer (ensure ALL component dependencies are loaded first)
        wp_enqueue_script(
            'yourstore-commerce-app',
            YOURSTORE_COMMERCE_PLUGIN_URL . 'assets/js/app.js',
            [
                'vue', 
                'yourstore-commerce-hero-section', 
                'yourstore-commerce-featured-products', 
                'yourstore-commerce-category-showcase', 
                'yourstore-commerce-newsletter-signup', 
                'yourstore-commerce-site-footer'
            ],
            YOURSTORE_COMMERCE_VERSION . '.' . time(),
            true
        );
        
        // Enqueue component fix script for debugging disappearing components
        wp_enqueue_script(
            'yourstore-commerce-component-fix',
            YOURSTORE_COMMERCE_PLUGIN_URL . 'labs/fix-disappearing-components.js',
            ['yourstore-commerce-app'],
            YOURSTORE_COMMERCE_VERSION . '.' . time(),
            true
        );

        // Enqueue frontend functionality
        wp_enqueue_script(
            'yourstore-commerce-frontend',
            YOURSTORE_COMMERCE_PLUGIN_URL . 'assets/js/frontend.js',
            ['yourstore-commerce-app'],
            YOURSTORE_COMMERCE_VERSION . '.' . time(),
            true
        );
        
        // Localize script with comprehensive data
        wp_localize_script('yourstore-commerce-frontend', 'yourStoreCommerce', 
            Data\DataService::get_localized_data()
        );
    }
    
    /**
     * Enqueue admin scripts and styles
     *
     * @return void
     */
    public function enqueue_admin_scripts(): void {
        wp_enqueue_style(
            'yourstore-commerce-admin',
            YOURSTORE_COMMERCE_PLUGIN_URL . 'assets/css/admin/admin.css',
            [],
            YOURSTORE_COMMERCE_VERSION
        );
        
        wp_enqueue_script(
            'yourstore-commerce-admin',
            YOURSTORE_COMMERCE_PLUGIN_URL . 'assets/js/admin/admin.js',
            ['jquery'],
            YOURSTORE_COMMERCE_VERSION,
            true
        );
    }
    
    /**
     * Load plugin textdomain
     *
     * @return void
     */
    public function load_textdomain(): void {
        load_plugin_textdomain(
            'yourstore-commerce',
            false,
            dirname(YOURSTORE_COMMERCE_PLUGIN_BASENAME) . '/languages'
        );
    }
    
    /**
     * Plugin activation
     *
     * @return void
     */
    public function activate(): void {
        // Create custom tables if needed
        $this->create_tables();
        
        // Set default options
        $this->set_default_options();
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Plugin deactivation
     *
     * @return void
     */
    public function deactivate(): void {
        // Clean up temporary data
        wp_cache_flush();
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Create custom database tables
     *
     * @return void
     */
    private function create_tables(): void {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        // Example table for product views/analytics
        $table_name = $wpdb->prefix . 'yourstore_product_views';
        
        $sql = "CREATE TABLE $table_name (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            product_id bigint(20) NOT NULL,
            user_id bigint(20) DEFAULT NULL,
            session_id varchar(32) NOT NULL,
            viewed_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY product_id (product_id),
            KEY user_id (user_id),
            KEY session_id (session_id),
            KEY viewed_at (viewed_at)
        ) $charset_collate;";
        
        require_once ABSPATH . 'wp-admin/includes/upgrade.php';
        dbDelta($sql);
    }
    
    /**
     * Set default plugin options
     *
     * @return void
     */
    private function set_default_options(): void {
        $default_options = [
            'enable_vue_components' => true,
            'enable_product_analytics' => true,
            'cache_duration' => 3600,
            'load_vue_cdn' => true,
            'load_tailwind_cdn' => true,
        ];
        
        foreach ($default_options as $option_name => $default_value) {
            $full_option_name = 'yourstore_commerce_' . $option_name;
            if (get_option($full_option_name) === false) {
                update_option($full_option_name, $default_value);
            }
        }
    }
}

/**
 * Initialize the plugin
 *
 * @return YourStoreCommerce
 */
function yourstore_commerce(): YourStoreCommerce {
    return YourStoreCommerce::instance();
}

// Initialize plugin
yourstore_commerce();
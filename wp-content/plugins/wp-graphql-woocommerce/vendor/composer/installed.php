<?php return array(
    'root' => array(
        'name' => 'wp-graphql/wp-graphql-woocommerce',
        'pretty_version' => 'v0.20.0',
        'version' => '0.20.0.0',
        'reference' => '61b99ea2f3e64584118e28685ba62e209c453b1f',
        'type' => 'wordpress-plugin',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => false,
    ),
    'versions' => array(
        'firebase/php-jwt' => array(
            'pretty_version' => 'v6.10.1',
            'version' => '6.10.1.0',
            'reference' => '500501c2ce893c824c801da135d02661199f60c5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../firebase/php-jwt',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'wp-graphql/wp-graphql-woocommerce' => array(
            'pretty_version' => 'v0.20.0',
            'version' => '0.20.0.0',
            'reference' => '61b99ea2f3e64584118e28685ba62e209c453b1f',
            'type' => 'wordpress-plugin',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);

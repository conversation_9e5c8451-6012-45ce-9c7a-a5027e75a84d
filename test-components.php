<?php
/**
 * Test Components - WordPress Compatible Version
 */

// WordPress bootstrap
require_once('wp-config.php');
require_once(ABSPATH . 'wp-settings.php');

// Set up WordPress environment
wp_head();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Component Test - YourStore</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <link rel="stylesheet" href="<?php echo get_template_directory_uri(); ?>/wp-content/plugins/yourstore-commerce/assets/css/globals.css">
    
    <style>
        :root {
            --color-primary: #16a34a;
            --color-primary-50: #f0fdf4;
            --color-primary-100: #dcfce7;
            --color-secondary-900: #111827;
            --color-secondary-600: #4b5563;
            --color-accent: #f59e0b;
        }
    </style>
</head>
<body class="bg-gray-50">

    <div class="max-w-7xl mx-auto px-4 py-8">
        <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-8">
            <div class="flex items-center">
                <i class="fas fa-check-circle text-green-600 mr-2"></i>
                <div>
                    <h3 class="text-green-800 font-semibold">✅ Success! Homepage Is Working</h3>
                    <p class="text-green-700 text-sm">You mentioned you can see featured products and categories on your homepage. This means the GraphQL fix is working and components are loading mock data successfully!</p>
                </div>
            </div>
        </div>
        
        <h1 class="text-3xl font-bold mb-8 text-center">Component Status Test</h1>
        
        <div class="space-y-8">
            
            <!-- Status Check -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-2xl font-semibold mb-4">System Status</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="flex items-center p-3 bg-green-50 rounded">
                        <i class="fas fa-check-circle text-green-600 mr-3"></i>
                        <span class="text-green-800">Homepage products displaying</span>
                    </div>
                    
                    <div class="flex items-center p-3 bg-green-50 rounded">
                        <i class="fas fa-check-circle text-green-600 mr-3"></i>
                        <span class="text-green-800">Categories displaying</span>
                    </div>
                    
                    <div class="flex items-center p-3 bg-yellow-50 rounded">
                        <i class="fas fa-pause-circle text-yellow-600 mr-3"></i>
                        <span class="text-yellow-800">GraphQL temporarily disabled</span>
                    </div>
                    
                    <div class="flex items-center p-3 bg-blue-50 rounded">
                        <i class="fas fa-database text-blue-600 mr-3"></i>
                        <span class="text-blue-800">Using mock data</span>
                    </div>
                </div>
            </div>
            
            <!-- Featured Products Test -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-2xl font-semibold mb-4">Featured Products Component Test</h2>
                <div class="mb-4">
                    <code class="bg-gray-100 p-2 rounded text-sm">[yourstore_featured_products title="Test Products" columns="4" products_per_page="4"]</code>
                </div>
                <div class="border rounded p-4">
                    <?php echo do_shortcode('[yourstore_featured_products title="Test Products" columns="4" products_per_page="4"]'); ?>
                </div>
            </div>
            
            <!-- Category Showcase Test -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-2xl font-semibold mb-4">Category Showcase Component Test</h2>
                <div class="mb-4">
                    <code class="bg-gray-100 p-2 rounded text-sm">[yourstore_category_showcase title="Test Categories" columns="4" max_categories="4"]</code>
                </div>
                <div class="border rounded p-4">
                    <?php echo do_shortcode('[yourstore_category_showcase title="Test Categories" columns="4" max_categories="4"]'); ?>
                </div>
            </div>
            
            <!-- Next Steps -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <h3 class="text-blue-800 font-semibold mb-3">🚀 What's Working Now</h3>
                <ul class="text-blue-700 space-y-2">
                    <li class="flex items-center">
                        <i class="fas fa-check mr-2"></i>
                        Homepage displays featured products and categories
                    </li>
                    <li class="flex items-center">
                        <i class="fas fa-check mr-2"></i>
                        No more GraphQL console errors
                    </li>
                    <li class="flex items-center">
                        <i class="fas fa-check mr-2"></i>
                        Components load instantly with mock data
                    </li>
                    <li class="flex items-center">
                        <i class="fas fa-check mr-2"></i>
                        Shop page should work with product catalog
                    </li>
                </ul>
            </div>
            
            <!-- Future Steps -->
            <div class="bg-gray-50 border border-gray-200 rounded-lg p-6">
                <h3 class="text-gray-800 font-semibold mb-3">🔧 Future Improvements</h3>
                <ul class="text-gray-700 space-y-2">
                    <li class="flex items-center">
                        <i class="fas fa-clock mr-2"></i>
                        Fix GraphQL schema to use real WooCommerce data
                    </li>
                    <li class="flex items-center">
                        <i class="fas fa-clock mr-2"></i>
                        Re-enable GraphQL integration when schema is corrected
                    </li>
                    <li class="flex items-center">
                        <i class="fas fa-clock mr-2"></i>
                        Add more WooCommerce products for testing
                    </li>
                </ul>
            </div>
            
        </div>
        
        <div class="text-center mt-8">
            <a href="<?php echo home_url(); ?>" class="inline-flex items-center px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition">
                <i class="fas fa-home mr-2"></i>
                Back to Homepage
            </a>
            <a href="<?php echo home_url('/shop'); ?>" class="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition ml-4">
                <i class="fas fa-store mr-2"></i>
                Visit Shop Page
            </a>
        </div>
    </div>

    <?php wp_footer(); ?>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('✅ Component test page loaded successfully');
            console.log('Available components:', {
                FeaturedProducts: !!window.FeaturedProducts,
                CategoryShowcase: !!window.CategoryShowcase,
                Vue: !!window.Vue
            });
            
            // Check if components are working
            setTimeout(() => {
                const hasProducts = document.querySelectorAll('.product-card, .yourstore-product').length > 0;
                const hasCategories = document.querySelectorAll('.category-card, .yourstore-category').length > 0;
                
                if (hasProducts || hasCategories) {
                    console.log('✅ Components are displaying content');
                } else {
                    console.log('ℹ️  Components may still be loading or using different selectors');
                }
            }, 3000);
        });
    </script>

</body>
</html>
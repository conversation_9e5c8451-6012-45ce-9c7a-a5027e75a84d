<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GraphQL Test - YourStore Commerce</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .loading { color: #666; }
        .error { color: #d32f2f; }
        .product { border: 1px solid #ddd; padding: 10px; margin: 10px 0; }
        .category { background: #f5f5f5; padding: 5px; margin: 5px 0; }
    </style>
</head>
<body>
    <h1>GraphQL API Test</h1>
    
    <div id="app">
        <h2>Products</h2>
        <div v-if="productsLoading" class="loading">Loading products...</div>
        <div v-if="productsError" class="error">{{ productsError }}</div>
        <div v-for="product in products" :key="product.id" class="product">
            <h3>{{ product.name }}</h3>
            <p>Price: £{{ product.price }}</p>
            <p>Categories: {{ product.categories.join(', ') }}</p>
            <p>Stock: {{ product.stock_status }} ({{ product.in_stock ? 'Available' : 'Out of Stock' }})</p>
        </div>
        
        <h2>Categories</h2>
        <div v-if="categoriesLoading" class="loading">Loading categories...</div>
        <div v-if="categoriesError" class="error">{{ categoriesError }}</div>
        <div v-for="category in categories" :key="category.id" class="category">
            <strong>{{ category.name }}</strong> ({{ category.count }} products)
        </div>
    </div>

    <script>
        // GraphQL API
        const GraphQLAPI = {
            endpoint: 'https://wp.coolify.local/graphql',

            async query(query, variables = {}) {
                try {
                    const response = await fetch(this.endpoint, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/json',
                        },
                        body: JSON.stringify({
                            query: query,
                            variables: variables
                        })
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    const result = await response.json();

                    if (result.errors) {
                        console.error('GraphQL errors:', result.errors);
                        throw new Error(result.errors[0].message);
                    }

                    return result.data;
                } catch (error) {
                    console.error('GraphQL query failed:', error);
                    throw error;
                }
            },

            async getProducts() {
                const query = `
                    query GetProducts($first: Int) {
                        products(first: $first) {
                            edges {
                                node {
                                    id
                                    databaseId
                                    name
                                    slug
                                    ... on Product {
                                        shortDescription
                                        featuredImage {
                                            node {
                                                sourceUrl
                                            }
                                        }
                                        productCategories {
                                            nodes {
                                                name
                                                slug
                                            }
                                        }
                                    }
                                    ... on SimpleProduct {
                                        price
                                        regularPrice
                                        salePrice
                                        stockStatus
                                    }
                                }
                            }
                        }
                    }
                `;

                const data = await this.query(query, { first: 5 });
                return data.products.edges.map(edge => {
                    const product = edge.node;
                    const cleanPrice = (priceString) => {
                        if (!priceString) return 0;
                        return parseFloat(priceString.replace(/[£$€¥₹]/g, '')) || 0;
                    };
                    
                    return {
                        id: product.databaseId,
                        name: product.name,
                        slug: product.slug,
                        description: product.shortDescription,
                        price: cleanPrice(product.price),
                        regular_price: cleanPrice(product.regularPrice),
                        sale_price: product.salePrice ? cleanPrice(product.salePrice) : null,
                        image: product.featuredImage?.node?.sourceUrl || '',
                        categories: product.productCategories?.nodes?.map(cat => cat.name) || [],
                        stock_status: product.stockStatus?.toLowerCase() || 'instock',
                        in_stock: product.stockStatus === 'IN_STOCK'
                    };
                });
            },

            async getCategories() {
                const query = `
                    query GetCategories($first: Int) {
                        productCategories(first: $first) {
                            nodes {
                                id
                                databaseId
                                name
                                slug
                                count
                            }
                        }
                    }
                `;

                const data = await this.query(query, { first: 10 });
                return data.productCategories.nodes.map(category => ({
                    id: category.databaseId,
                    name: category.name,
                    slug: category.slug,
                    count: category.count || 0
                }));
            }
        };

        // Vue App
        const { createApp, ref, onMounted } = Vue;

        createApp({
            setup() {
                const products = ref([]);
                const categories = ref([]);
                const productsLoading = ref(false);
                const categoriesLoading = ref(false);
                const productsError = ref(null);
                const categoriesError = ref(null);

                const loadProducts = async () => {
                    productsLoading.value = true;
                    productsError.value = null;
                    try {
                        products.value = await GraphQLAPI.getProducts();
                        console.log('Loaded products:', products.value);
                    } catch (error) {
                        productsError.value = error.message;
                        console.error('Failed to load products:', error);
                    } finally {
                        productsLoading.value = false;
                    }
                };

                const loadCategories = async () => {
                    categoriesLoading.value = true;
                    categoriesError.value = null;
                    try {
                        categories.value = await GraphQLAPI.getCategories();
                        console.log('Loaded categories:', categories.value);
                    } catch (error) {
                        categoriesError.value = error.message;
                        console.error('Failed to load categories:', error);
                    } finally {
                        categoriesLoading.value = false;
                    }
                };

                onMounted(() => {
                    loadProducts();
                    loadCategories();
                });

                return {
                    products,
                    categories,
                    productsLoading,
                    categoriesLoading,
                    productsError,
                    categoriesError
                };
            }
        }).mount('#app');
    </script>
</body>
</html>
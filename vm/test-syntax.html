<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaScript Syntax Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="/wp-content/plugins/yourstore-commerce/assets/css/globals.css">
</head>
<body class="bg-gray-100 p-8">

    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-8 text-center">JavaScript Syntax Test</h1>
        
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-semibold mb-4">Loading JavaScript Components</h2>
            
            <div id="loading-status" class="space-y-2">
                <div class="flex items-center">
                    <i class="fas fa-spinner fa-spin mr-2"></i>
                    <span>Loading components...</span>
                </div>
            </div>
            
            <div id="component-status" class="mt-6 hidden">
                <h3 class="text-lg font-semibold mb-3">Component Status:</h3>
                <ul id="status-list" class="space-y-1"></ul>
            </div>
            
            <div id="error-log" class="mt-6 hidden">
                <h3 class="text-lg font-semibold mb-3 text-red-600">JavaScript Errors:</h3>
                <div id="error-list" class="bg-red-50 border border-red-200 rounded p-3"></div>
            </div>
        </div>
        
        <!-- Test Product Catalog Component -->
        <div class="bg-white rounded-lg shadow p-6 mt-6">
            <h2 class="text-xl font-semibold mb-4">Product Catalog Test</h2>
            <div class="yourstore-product-catalog"
                 data-title="Test Catalog"
                 data-subtitle="Testing syntax fix"
                 data-show-filters="true"
                 data-show-sort="true"
                 data-show-pagination="true"
                 data-products-per-page="6"
                 data-default-category="">
                <div class="text-center py-8">
                    <i class="fas fa-spinner fa-spin text-2xl text-gray-400"></i>
                    <p class="text-gray-500 mt-2">Loading component...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Error tracking -->
    <script>
        window.jsErrors = [];
        window.addEventListener('error', function(e) {
            window.jsErrors.push({
                message: e.message,
                filename: e.filename,
                lineno: e.lineno,
                colno: e.colno
            });
            
            const errorDiv = document.getElementById('error-log');
            const errorList = document.getElementById('error-list');
            
            errorDiv.classList.remove('hidden');
            errorList.innerHTML += `<div class="text-red-700 font-mono text-sm mb-2">
                <strong>Error:</strong> ${e.message}<br>
                <strong>File:</strong> ${e.filename}:${e.lineno}:${e.colno}
            </div>`;
        });
    </script>

    <!-- Load Components -->
    <script src="/wp-content/plugins/yourstore-commerce/assets/js/graphql-api.js"></script>
    <script src="/wp-content/plugins/yourstore-commerce/assets/js/api-utils.js"></script>
    <script src="/wp-content/plugins/yourstore-commerce/assets/js/components/product-catalog.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                const loadingDiv = document.getElementById('loading-status');
                const statusDiv = document.getElementById('component-status');
                const statusList = document.getElementById('status-list');
                
                loadingDiv.classList.add('hidden');
                statusDiv.classList.remove('hidden');
                
                const components = {
                    'YourStoreGraphQL': window.YourStoreGraphQL,
                    'ProductCatalog': window.ProductCatalog,
                    'Vue': window.Vue
                };
                
                Object.entries(components).forEach(([name, component]) => {
                    const li = document.createElement('li');
                    li.className = 'flex items-center';
                    li.innerHTML = `
                        <i class="fas ${component ? 'fa-check-circle text-green-500' : 'fa-times-circle text-red-500'} mr-2"></i>
                        <span>${name}: ${component ? 'Loaded' : 'Not Found'}</span>
                    `;
                    statusList.appendChild(li);
                });
                
                // Show any JavaScript errors
                if (window.jsErrors.length === 0) {
                    const li = document.createElement('li');
                    li.className = 'flex items-center text-green-600';
                    li.innerHTML = `<i class="fas fa-check-circle mr-2"></i><span>No JavaScript errors detected</span>`;
                    statusList.appendChild(li);
                }
                
                // Try to initialize product catalog
                if (window.ProductCatalog && window.Vue) {
                    const catalogElement = document.querySelector('.yourstore-product-catalog');
                    if (catalogElement) {
                        try {
                            const { createApp } = Vue;
                            const app = createApp({
                                components: { ProductCatalog: window.ProductCatalog },
                                setup() {
                                    return {
                                        title: catalogElement.dataset.title || '',
                                        subtitle: catalogElement.dataset.subtitle || '',
                                        showFilters: catalogElement.dataset.showFilters === 'true',
                                        showSort: catalogElement.dataset.showSort === 'true',
                                        showPagination: catalogElement.dataset.showPagination === 'true',
                                        productsPerPage: parseInt(catalogElement.dataset.productsPerPage) || 12,
                                        defaultCategory: catalogElement.dataset.defaultCategory || ''
                                    };
                                },
                                template: `<ProductCatalog 
                                    :title="title"
                                    :subtitle="subtitle"
                                    :show-filters="showFilters"
                                    :show-sort="showSort"
                                    :show-pagination="showPagination"
                                    :products-per-page="productsPerPage"
                                    :default-category="defaultCategory"
                                />`
                            });
                            app.mount(catalogElement);
                            
                            const li = document.createElement('li');
                            li.className = 'flex items-center text-green-600';
                            li.innerHTML = `<i class="fas fa-check-circle mr-2"></i><span>Product Catalog component mounted successfully</span>`;
                            statusList.appendChild(li);
                            
                        } catch (error) {
                            console.error('Failed to mount component:', error);
                            window.jsErrors.push({
                                message: 'Component mount error: ' + error.message,
                                filename: 'test-syntax.html',
                                lineno: 0,
                                colno: 0
                            });
                        }
                    }
                }
            }, 2000);
        });
    </script>

</body>
</html>
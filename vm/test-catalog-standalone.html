<?php
/**
 * Standalone Product Catalog Test Page
 * Access via: https://wp.coolify.local/test-catalog.php
 */

// WordPress environment bootstrap
define('WP_USE_THEMES', true);
require_once('wp-load.php');

get_header();
?>

<div class="catalog-test-page">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold mb-8">Product Catalog Test Page</h1>
        
        <!-- Test the shortcode -->
        <?php echo do_shortcode('[yourstore_product_catalog title="Test Product Catalog" subtitle="Testing WooCommerce integration" show_filters="true" show_sort="true" show_pagination="true" products_per_page="12"]'); ?>
    </div>
</div>

<?php get_footer(); ?>
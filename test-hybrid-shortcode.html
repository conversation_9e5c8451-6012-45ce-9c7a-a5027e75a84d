<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Hybrid Shortcode</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ccc; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; }
    </style>
</head>
<body>
    <h1>Test Hybrid Component Shortcode</h1>
    
    <div class="test-section">
        <h2>Hero Section Shortcode Test</h2>
        <p>Testing hybrid component in shortcode format:</p>
        
        <!-- Test the hero_section shortcode -->
        <div class="result">
            <h3>Shortcode:</h3>
            <code>[hero_section title="Test Hero" subtitle="This is a test subtitle" cta_text="Test Button"]</code>
        </div>
        
        <div class="result">
            <h3>Expected Output:</h3>
            <div class="yourstore-hero-section hero-section-wrapper" data-props='{"title":"Test Hero","subtitle":"This is a test subtitle","cta_text":"Test Button","cta_url":"\/shop","background_image":"","overlay_opacity":0.5,"text_alignment":"center","height":"auto","show_scroll_indicator":true,"enable_parallax":true,"component_id":"hero-1234567890"}'></div>
        </div>
    </div>
    
    <div class="test-section">
        <h2>WordPress Integration Test</h2>
        <p>To test this in WordPress:</p>
        <ol>
            <li>Create a new page or post</li>
            <li>Add the shortcode: <code>[hero_section title="Welcome to My Store" subtitle="Amazing products await"]</code></li>
            <li>View the page to see if the component loads</li>
            <li>Check browser console for any errors</li>
        </ol>
    </div>
    
    <div class="test-section">
        <h2>Product Grid Shortcode Test</h2>
        <p>Testing product grid shortcode:</p>
        
        <div class="result">
            <h3>Shortcode:</h3>
            <code>[product_grid limit="8" columns="3" categories="electronics"]</code>
        </div>
    </div>
    
    <script>
        // Simple test to see if the HTML structure is correct
        document.addEventListener('DOMContentLoaded', function() {
            const heroElement = document.querySelector('.yourstore-hero-section');
            if (heroElement) {
                console.log('Hero element found:', heroElement);
                console.log('Data props:', heroElement.dataset.props);
                
                if (heroElement.dataset.props) {
                    try {
                        const props = JSON.parse(heroElement.dataset.props);
                        console.log('Parsed props:', props);
                    } catch (e) {
                        console.error('Invalid JSON in data-props:', e);
                    }
                }
            }
        });
    </script>
</body>
</html>
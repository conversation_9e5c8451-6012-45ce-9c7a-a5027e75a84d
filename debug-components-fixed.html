<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YourStore Components Debug - Fixed</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .debug-section {
            border: 2px solid #ccc;
            margin: 20px 0;
            padding: 20px;
            background: #f9f9f9;
            border-radius: 8px;
        }
        .component-wrapper {
            border: 1px dashed #666;
            min-height: 100px;
            padding: 15px;
            margin: 10px 0;
            background: white;
            border-radius: 4px;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-loading { background-color: #fbbf24; }
        .status-success { background-color: #10b981; }
        .status-error { background-color: #ef4444; }
        
        .console-output {
            background: #1f2937;
            color: #10b981;
            padding: 16px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto p-4 max-w-6xl">
        <h1 class="text-3xl font-bold mb-6 text-center">YourStore Components Debug - Fixed</h1>
        
        <!-- System Status -->
        <div class="debug-section">
            <h2 class="text-xl font-bold mb-4">System Status</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="bg-white p-4 rounded shadow">
                    <h3 class="font-semibold mb-2">Vue.js</h3>
                    <p id="vue-status"><span class="status-indicator status-loading"></span>Loading...</p>
                </div>
                <div class="bg-white p-4 rounded shadow">
                    <h3 class="font-semibold mb-2">Components</h3>
                    <p id="components-status"><span class="status-indicator status-loading"></span>Checking...</p>
                </div>
                <div class="bg-white p-4 rounded shadow">
                    <h3 class="font-semibold mb-2">Mounting</h3>
                    <p id="mounting-status"><span class="status-indicator status-loading"></span>Scanning...</p>
                </div>
            </div>
        </div>

        <!-- Manual Vue Test -->
        <div class="debug-section">
            <h2 class="text-xl font-bold mb-4">Manual Vue Test (Proof Vue Works)</h2>
            <div id="manual-vue-test" class="component-wrapper">
                <p class="text-gray-500">Manual Vue component will load here...</p>
            </div>
        </div>

        <!-- Hero Section Test -->
        <div class="debug-section">
            <h2 class="text-xl font-bold mb-4">Hero Section Test</h2>
            
            <!-- Legacy Format -->
            <h3 class="text-lg font-semibold mb-2">Legacy Format (data-* attributes)</h3>
            <div class="component-wrapper yourstore-hero-section" 
                 data-title="Legacy Format Hero" 
                 data-subtitle="Testing legacy data-attribute format"
                 data-cta-text="Legacy CTA"
                 data-cta-url="/shop"
                 data-overlay-opacity="0.3">
                <p class="text-orange-500">Legacy Hero Component Should Load Here</p>
            </div>
            
            <!-- Hybrid Format -->
            <h3 class="text-lg font-semibold mb-2 mt-6">Hybrid Format (data-props JSON)</h3>
            <div class="component-wrapper yourstore-hero-section hero-section-wrapper" 
                 data-props='{"title":"Hybrid Hero","subtitle":"Testing data-props format","cta_text":"Hybrid CTA","cta_url":"/shop","overlay_opacity":0.5,"text_alignment":"center","show_scroll_indicator":true,"enable_parallax":true,"component_id":"hero-debug-test"}'>
                <p class="text-blue-500">Hybrid Hero Component Should Load Here</p>
            </div>
        </div>

        <!-- Component Loading Test -->
        <div class="debug-section">
            <h2 class="text-xl font-bold mb-4">Component Loading Test</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <h3 class="font-semibold mb-2">Available on Window Object:</h3>
                    <ul id="window-components" class="space-y-1 text-sm"></ul>
                </div>
                <div>
                    <h3 class="font-semibold mb-2">Script Loading Status:</h3>
                    <ul id="script-status" class="space-y-1 text-sm"></ul>
                </div>
            </div>
        </div>

        <!-- Console Output -->
        <div class="debug-section">
            <h2 class="text-xl font-bold mb-4">Console Output</h2>
            <div id="console-output" class="console-output">
                Console messages will appear here...
            </div>
        </div>

        <!-- Controls -->
        <div class="debug-section">
            <h2 class="text-xl font-bold mb-4">Debug Controls</h2>
            <div class="flex flex-wrap gap-4">
                <button id="reload-components" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors">
                    Reload Components
                </button>
                <button id="force-mount" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors">
                    Force Mount
                </button>
                <button id="clear-console" class="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 transition-colors">
                    Clear Console
                </button>
            </div>
        </div>
    </div>

    <script>
        // Console capture system
        const consoleOutput = document.getElementById('console-output');
        const originalLog = console.log;
        const originalWarn = console.warn;
        const originalError = console.error;

        function addToConsole(type, ...args) {
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            
            const timestamp = new Date().toLocaleTimeString();
            const line = `[${timestamp}] [${type.toUpperCase()}] ${message}\n`;
            
            consoleOutput.textContent += line;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }

        console.log = function(...args) {
            addToConsole('log', ...args);
            originalLog.apply(console, args);
        };

        console.warn = function(...args) {
            addToConsole('warn', ...args);
            originalWarn.apply(console, args);
        };

        console.error = function(...args) {
            addToConsole('error', ...args);
            originalError.apply(console, args);
        };

        // Status update functions
        function updateStatus(elementId, status, text) {
            const element = document.getElementById(elementId);
            if (element) {
                const indicator = element.querySelector('.status-indicator');
                const statusClass = status === 'success' ? 'status-success' : 
                                  status === 'error' ? 'status-error' : 'status-loading';
                
                indicator.className = `status-indicator ${statusClass}`;
                element.innerHTML = `<span class="status-indicator ${statusClass}"></span>${text}`;
            }
        }

        // Initialize Vue test
        function initManualVue() {
            console.log('Initializing manual Vue test...');
            
            if (typeof Vue === 'undefined') {
                console.error('Vue.js not loaded');
                updateStatus('vue-status', 'error', 'Vue.js not found');
                return;
            }

            updateStatus('vue-status', 'success', `Vue.js ${Vue.version || '3.x'} loaded`);

            const { createApp, ref } = Vue;
            
            const TestComponent = {
                setup() {
                    const message = ref('✅ Manual Vue component working!');
                    const timestamp = ref(new Date().toLocaleTimeString());
                    
                    return { message, timestamp };
                },
                template: `
                    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
                        <p class="font-bold">{{ message }}</p>
                        <p class="text-sm">Mounted at: {{ timestamp }}</p>
                        <p class="text-xs mt-2">This proves Vue.js is working correctly.</p>
                    </div>
                `
            };

            try {
                const app = createApp(TestComponent);
                app.mount('#manual-vue-test');
                console.log('Manual Vue component mounted successfully');
            } catch (error) {
                console.error('Failed to mount manual Vue component:', error);
                updateStatus('vue-status', 'error', 'Vue mounting failed');
            }
        }

        // Load YourStore components
        function loadYourStoreComponents() {
            console.log('Loading YourStore components...');
            
            const scriptUrls = [
                '/wp-content/plugins/yourstore-commerce/assets/js/components/hero-section.js',
                '/wp-content/plugins/yourstore-commerce/assets/js/components/featured-products.js',
                '/wp-content/plugins/yourstore-commerce/assets/js/components/category-showcase.js',
                '/wp-content/plugins/yourstore-commerce/assets/js/components/newsletter-signup.js',
                '/wp-content/plugins/yourstore-commerce/assets/js/components/site-footer.js',
                '/wp-content/plugins/yourstore-commerce/assets/js/app.js'
            ];

            let loadedCount = 0;
            const scriptStatus = document.getElementById('script-status');

            function updateScriptStatus(url, status, error = null) {
                const li = document.createElement('li');
                const statusIcon = status === 'success' ? '✅' : status === 'error' ? '❌' : '⏳';
                const fileName = url.split('/').pop();
                li.textContent = `${statusIcon} ${fileName}${error ? ` (${error})` : ''}`;
                scriptStatus.appendChild(li);
            }

            function loadScript(url) {
                return new Promise((resolve, reject) => {
                    updateScriptStatus(url, 'loading');
                    
                    const script = document.createElement('script');
                    script.src = url;
                    script.onload = () => {
                        console.log(`✅ Loaded: ${url}`);
                        updateScriptStatus(url, 'success');
                        loadedCount++;
                        resolve();
                    };
                    script.onerror = () => {
                        console.error(`❌ Failed to load: ${url}`);
                        updateScriptStatus(url, 'error', 'Load failed');
                        reject(new Error(`Failed to load ${url}`));
                    };
                    document.head.appendChild(script);
                });
            }

            // Load all scripts sequentially
            async function loadAllScripts() {
                for (const url of scriptUrls) {
                    try {
                        await loadScript(url);
                    } catch (error) {
                        console.error(`Script loading failed:`, error);
                    }
                }
                
                updateStatus('components-status', 'success', `${loadedCount}/${scriptUrls.length} scripts loaded`);
                
                // Check components after loading
                setTimeout(checkComponents, 1000);
            }

            loadAllScripts();
        }

        // Check available components
        function checkComponents() {
            console.log('Checking available components...');
            
            const expectedComponents = ['HeroSection', 'FeaturedProducts', 'CategoryShowcase', 'NewsletterSignup', 'SiteFooter'];
            const windowComponents = document.getElementById('window-components');
            
            windowComponents.innerHTML = '';
            
            const availableComponents = [];
            
            expectedComponents.forEach(componentName => {
                const li = document.createElement('li');
                const isAvailable = window[componentName];
                const icon = isAvailable ? '✅' : '❌';
                
                li.textContent = `${icon} ${componentName}`;
                if (isAvailable) {
                    availableComponents.push(componentName);
                    li.classList.add('text-green-600');
                } else {
                    li.classList.add('text-red-600');
                }
                
                windowComponents.appendChild(li);
            });

            console.log('Available components:', availableComponents);
            console.log('Missing components:', expectedComponents.filter(comp => !window[comp]));
            
            // Try to mount components
            if (availableComponents.length > 0) {
                updateStatus('mounting-status', 'success', `${availableComponents.length} components available`);
                setTimeout(attemptMounting, 500);
            } else {
                updateStatus('mounting-status', 'error', 'No components available');
            }
        }

        // Attempt to mount components
        function attemptMounting() {
            console.log('Attempting to mount components...');
            
            let mountedCount = 0;
            
            // Try to mount Hero Section components
            document.querySelectorAll('.yourstore-hero-section').forEach((element, index) => {
                if (element.__vue_app) {
                    console.log(`Hero ${index + 1} already mounted`);
                    return;
                }
                
                if (!window.HeroSection) {
                    console.warn('HeroSection component not available');
                    return;
                }
                
                try {
                    let props = {};
                    
                    // Handle hybrid format (data-props)
                    if (element.dataset.props) {
                        props = JSON.parse(element.dataset.props);
                        console.log(`Mounting Hero ${index + 1} with hybrid props:`, props);
                    } 
                    // Handle legacy format (individual data attributes)
                    else {
                        props = {
                            title: element.dataset.title || 'Default Title',
                            subtitle: element.dataset.subtitle || 'Default Subtitle',
                            ctaText: element.dataset.ctaText || 'Click Here',
                            ctaUrl: element.dataset.ctaUrl || '/shop',
                            overlayOpacity: parseFloat(element.dataset.overlayOpacity) || 0.5
                        };
                        console.log(`Mounting Hero ${index + 1} with legacy props:`, props);
                    }
                    
                    const app = Vue.createApp(window.HeroSection, props);
                    element.__vue_app = app.mount(element);
                    mountedCount++;
                    
                    console.log(`✅ Hero Section ${index + 1} mounted successfully`);
                    
                    // Replace placeholder content
                    element.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
                    element.style.minHeight = '200px';
                    
                } catch (error) {
                    console.error(`❌ Failed to mount Hero Section ${index + 1}:`, error);
                }
            });
            
            updateStatus('mounting-status', mountedCount > 0 ? 'success' : 'error', 
                         `${mountedCount} components mounted`);
            
            if (mountedCount > 0) {
                console.log(`🎉 Successfully mounted ${mountedCount} components!`);
            } else {
                console.warn('⚠️ No components were mounted');
            }
        }

        // Event handlers
        document.getElementById('reload-components').addEventListener('click', () => {
            location.reload();
        });

        document.getElementById('force-mount').addEventListener('click', () => {
            console.log('Force mounting triggered...');
            attemptMounting();
        });

        document.getElementById('clear-console').addEventListener('click', () => {
            consoleOutput.textContent = 'Console cleared...\n';
        });

        // Initialize everything
        function init() {
            console.log('🚀 Initializing YourStore Components Debug...');
            console.log('Current URL:', window.location.href);
            console.log('Document ready state:', document.readyState);
            
            // Initialize manual Vue test first
            initManualVue();
            
            // Then load YourStore components
            setTimeout(() => {
                loadYourStoreComponents();
            }, 1000);
        }

        // Start initialization
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', init);
        } else {
            init();
        }
    </script>
</body>
</html>
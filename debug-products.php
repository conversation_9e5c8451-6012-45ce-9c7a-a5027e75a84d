<?php
/**
 * Debug Products - Check what's available
 */

// WordPress bootstrap
require_once('wp-config.php');
require_once(ABSPATH . 'wp-settings.php');

// Check if WooCommerce is active
echo "<h2>WooCommerce Status</h2>";
if (class_exists('WooCommerce')) {
    echo "✅ WooCommerce is active<br>";
} else {
    echo "❌ WooCommerce is NOT active<br>";
}

// Check WPGraphQL
if (class_exists('WPGraphQL')) {
    echo "✅ WPGraphQL is active<br>";
} else {
    echo "❌ WPGraphQL is NOT active<br>";
}

// Check WPGraphQL WooCommerce
if (class_exists('WPGraphQL_WooCommerce')) {
    echo "✅ WPGraphQL WooCommerce is active<br>";
} else {
    echo "❌ WPGraphQL WooCommerce is NOT active<br>";
}

echo "<hr>";

// Check products in database
echo "<h2>Products in Database</h2>";
$products = get_posts([
    'post_type' => 'product',
    'post_status' => 'publish',
    'numberposts' => 10
]);

echo "Found " . count($products) . " products:<br><br>";

foreach ($products as $product) {
    echo "- " . $product->post_title . " (ID: " . $product->ID . ")<br>";
}

echo "<hr>";

// Check categories
echo "<h2>Product Categories</h2>";
$categories = get_terms([
    'taxonomy' => 'product_cat',
    'hide_empty' => false,
    'number' => 10
]);

if (is_wp_error($categories)) {
    echo "❌ Error getting categories: " . $categories->get_error_message() . "<br>";
} else {
    echo "Found " . count($categories) . " categories:<br><br>";
    foreach ($categories as $category) {
        echo "- " . $category->name . " (" . $category->count . " products)<br>";
    }
}

echo "<hr>";

// Check GraphQL endpoint
echo "<h2>GraphQL Endpoint Test</h2>";
$graphql_url = home_url('/graphql');
echo "GraphQL URL: " . $graphql_url . "<br>";

// Simple cURL test
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $graphql_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([
    'query' => '{ __schema { types { name } } }'
]));

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "HTTP Status: " . $http_code . "<br>";
if ($http_code == 200) {
    echo "✅ GraphQL endpoint is working<br>";
    $data = json_decode($response, true);
    if (isset($data['data'])) {
        echo "GraphQL schema types found: " . count($data['data']['__schema']['types']) . "<br>";
    }
} else {
    echo "❌ GraphQL endpoint not working<br>";
    echo "Response: " . $response . "<br>";
}

echo "<hr>";

// Check plugin status
echo "<h2>YourStore Commerce Plugin</h2>";
if (class_exists('YourStore\\Commerce\\YourStoreCommerce')) {
    echo "✅ YourStore Commerce plugin is active<br>";
} else {
    echo "❌ YourStore Commerce plugin is NOT active<br>";
}

// Check if shortcodes are registered
echo "<h2>Shortcodes Status</h2>";
global $shortcode_tags;
$ourstore_shortcodes = [
    'yourstore_featured_products',
    'yourstore_category_showcase', 
    'yourstore_product_catalog',
    'yourstore_newsletter'
];

foreach ($ourstore_shortcodes as $shortcode) {
    if (array_key_exists($shortcode, $shortcode_tags)) {
        echo "✅ [$shortcode] is registered<br>";
    } else {
        echo "❌ [$shortcode] is NOT registered<br>";
    }
}

?>
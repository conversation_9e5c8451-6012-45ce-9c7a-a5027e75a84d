<?php
/**
 * Public Product Catalog Test - No Permissions Required
 * Access via: https://wp.coolify.local/public-catalog-test.php
 */

// WordPress environment bootstrap
define('WP_USE_THEMES', false);
require_once('wp-load.php');

// Get header
wp_head();
?>
<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <?php wp_head(); ?>
</head>
<body <?php body_class(); ?>>

<div class="public-catalog-test">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold mb-4">Public Product Catalog Test</h1>
        <p class="text-gray-600 mb-8">This page tests the product catalog component using your WooCommerce products.</p>
        
        <!-- Product Catalog Component -->
        <div class="catalog-container">
            <?php 
            // Execute the shortcode
            echo do_shortcode('[yourstore_product_catalog title="WooCommerce Products" subtitle="Your store\'s product catalog" show_filters="true" show_sort="true" show_pagination="true" products_per_page="12"]'); 
            ?>
        </div>
        
        <div class="debug-info mt-8 p-4 bg-gray-100 rounded">
            <h3 class="font-bold mb-2">Debug Information:</h3>
            <p><strong>WooCommerce Status:</strong> <?php echo class_exists('WooCommerce') ? 'Active' : 'Not Active'; ?></p>
            <p><strong>Plugin Status:</strong> <?php echo class_exists('YourStore\Commerce\YourStoreCommerce') ? 'Active' : 'Not Active'; ?></p>
            <p><strong>Total Products:</strong> <?php 
                $products = get_posts([
                    'post_type' => 'product',
                    'post_status' => 'publish',
                    'numberposts' => -1
                ]);
                echo count($products);
            ?></p>
        </div>
    </div>
</div>

<?php wp_footer(); ?>
</body>
</html>
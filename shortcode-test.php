<?php
/**
 * Simple Shortcode Test
 * Tests if YourStore shortcodes are registered
 */

define('WP_USE_THEMES', false);
require_once('wp-load.php');

echo "<h1>Shortcode Registration Test</h1>";

// Test if shortcode is registered
$shortcode_exists = shortcode_exists('yourstore_product_catalog');
echo "<p><strong>Shortcode 'yourstore_product_catalog' registered:</strong> " . ($shortcode_exists ? 'YES' : 'NO') . "</p>";

// List all registered shortcodes
global $shortcode_tags;
$yourstore_shortcodes = array_filter(array_keys($shortcode_tags), function($key) {
    return strpos($key, 'yourstore') !== false;
});

echo "<p><strong>YourStore shortcodes found:</strong></p>";
echo "<ul>";
foreach($yourstore_shortcodes as $shortcode) {
    echo "<li>" . $shortcode . "</li>";
}
echo "</ul>";

// Test plugin class
echo "<p><strong>Plugin class exists:</strong> " . (class_exists('YourStore\Commerce\YourStoreCommerce') ? 'YES' : 'NO') . "</p>";

// Test WooCommerce
echo "<p><strong>WooCommerce active:</strong> " . (class_exists('WooCommerce') ? 'YES' : 'NO') . "</p>";

// Test shortcode execution
echo "<h2>Shortcode Test Output:</h2>";
echo "<div style='border: 2px solid #ccc; padding: 10px; background: #f9f9f9;'>";
echo do_shortcode('[yourstore_product_catalog title="Test" products_per_page="4"]');
echo "</div>";

// Check for JavaScript errors
echo "<script>
console.log('Testing shortcode page loaded');
if (typeof window.YourStoreCommerce !== 'undefined') {
    console.log('YourStoreCommerce global found');
} else {
    console.log('YourStoreCommerce global NOT found');
}
</script>";
?>
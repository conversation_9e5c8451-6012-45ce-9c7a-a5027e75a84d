<?php
/**
 * Direct Component Test - Bypass shortcode system
 */

define('WP_USE_THEMES', false);
require_once('wp-load.php');
wp_head();
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Direct Component Test</title>
    <!-- Load Vue and Tailwind -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <?php wp_head(); ?>
</head>
<body class="bg-gray-50">
    <div class="container mx-auto p-8">
        <h1 class="text-3xl font-bold mb-8">Direct Component Test</h1>
        
        <!-- Manually create the component div -->
        <div class="yourstore-product-catalog" 
             data-title="Direct Test" 
             data-subtitle="Testing component directly" 
             data-show-filters="true" 
             data-show-sort="true" 
             data-show-pagination="true" 
             data-products-per-page="12">
            <p>Loading component...</p>
        </div>
    </div>

    <!-- Debug info -->
    <div class="mt-8 p-4 bg-blue-100">
        <h3 class="font-bold">Debug Info:</h3>
        <p>Plugin active: <?php echo class_exists('YourStore\Commerce\YourStoreCommerce') ? 'YES' : 'NO'; ?></p>
        <p>WooCommerce active: <?php echo class_exists('WooCommerce') ? 'YES' : 'NO'; ?></p>
        <p>Product count: <?php 
            $products = get_posts(['post_type' => 'product', 'post_status' => 'publish', 'numberposts' => -1]);
            echo count($products);
        ?></p>
    </div>

    <?php wp_footer(); ?>
    
    <script>
        // Check if scripts loaded
        console.log('Vue available:', typeof Vue !== 'undefined');
        console.log('YourStoreAPI available:', typeof window.YourStoreAPI !== 'undefined');
        console.log('ProductCatalog available:', typeof window.ProductCatalog !== 'undefined');
        
        // Try to mount component manually if it exists
        if (typeof Vue !== 'undefined' && typeof window.ProductCatalog !== 'undefined') {
            const element = document.querySelector('.yourstore-product-catalog');
            if (element) {
                const props = {
                    title: 'Direct Test',
                    subtitle: 'Testing component directly',
                    showFilters: true,
                    showSort: true,
                    showPagination: true,
                    productsPerPage: 12
                };
                
                try {
                    const app = Vue.createApp(window.ProductCatalog, props);
                    app.mount(element);
                    console.log('Component mounted successfully!');
                } catch (error) {
                    console.error('Component mount error:', error);
                }
            }
        }
    </script>
</body>
</html>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YourStore Components Debug</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .debug-section {
            border: 2px solid #ccc;
            margin: 20px;
            padding: 20px;
            background: #f9f9f9;
        }
        .component-wrapper {
            border: 1px dashed #666;
            min-height: 100px;
            padding: 10px;
            margin: 10px 0;
            background: white;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto p-4">
        <h1 class="text-3xl font-bold mb-6">YourStore Components Debug Page</h1>
        
        <!-- Debug Info -->
        <div class="debug-section">
            <h2 class="text-xl font-bold mb-4">Debug Information</h2>
            <div id="debug-info">
                <p><strong>Vue.js Status:</strong> <span id="vue-status">Loading...</span></p>
                <p><strong>Components Loaded:</strong> <span id="components-status">Checking...</span></p>
                <p><strong>Elements Found:</strong> <span id="elements-count">Scanning...</span></p>
            </div>
        </div>

        <!-- Legacy Block Format Test -->
        <div class="debug-section">
            <h2 class="text-xl font-bold mb-4">Legacy Block Format Test</h2>
            <div class="component-wrapper yourstore-hero-section" 
                 data-title="Legacy Block Hero" 
                 data-subtitle="Testing legacy data-attribute format"
                 data-cta-text="Legacy CTA"
                 data-cta-url="/shop">
                <p class="text-red-500">Hero Component Should Load Here</p>
            </div>
        </div>

        <!-- Hybrid Component Format Test -->
        <div class="debug-section">
            <h2 class="text-xl font-bold mb-4">Hybrid Component Format Test</h2>
            <div class="component-wrapper yourstore-hero-section hero-section-wrapper" 
                 data-props='{"title":"Hybrid Hero","subtitle":"Testing data-props format","cta_text":"Hybrid CTA","cta_url":"/shop","overlay_opacity":0.5,"text_alignment":"center","show_scroll_indicator":true,"enable_parallax":true,"component_id":"hero-debug-test"}'>
                <p class="text-red-500">Hero Component Should Load Here</p>
            </div>
        </div>

        <!-- Manual Vue Component Test -->
        <div class="debug-section">
            <h2 class="text-xl font-bold mb-4">Manual Vue Test</h2>
            <div id="manual-vue-test" class="component-wrapper">
                <p class="text-red-500">Manual Vue Component Should Load Here</p>
            </div>
        </div>

        <!-- Console Output -->
        <div class="debug-section">
            <h2 class="text-xl font-bold mb-4">Console Output</h2>
            <div id="console-output" class="bg-black text-green-400 p-4 rounded text-sm font-mono max-h-64 overflow-y-auto">
                <p>Console messages will appear here...</p>
            </div>
        </div>
    </div>

    <!-- Load YourStore Components -->
    <script>
        // Capture console messages
        const consoleOutput = document.getElementById('console-output');
        const originalLog = console.log;
        const originalWarn = console.warn;
        const originalError = console.error;

        function addToConsole(type, message) {
            const p = document.createElement('p');
            p.className = type === 'error' ? 'text-red-400' : type === 'warn' ? 'text-yellow-400' : 'text-green-400';
            p.textContent = `[${type.toUpperCase()}] ${message}`;
            consoleOutput.appendChild(p);
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }

        console.log = function(...args) {
            addToConsole('log', args.join(' '));
            originalLog.apply(console, args);
        };

        console.warn = function(...args) {
            addToConsole('warn', args.join(' '));
            originalWarn.apply(console, args);
        };

        console.error = function(...args) {
            addToConsole('error', args.join(' '));
            originalError.apply(console, args);
        };

        // Debug function
        function updateDebugInfo() {
            // Vue.js status
            document.getElementById('vue-status').textContent = typeof Vue !== 'undefined' ? 'Loaded ✅' : 'Not Found ❌';
            
            // Components status
            const components = ['HeroSection', 'FeaturedProducts', 'CategoryShowcase', 'NewsletterSignup', 'SiteFooter'];
            const loaded = components.filter(comp => window[comp]);
            document.getElementById('components-status').textContent = `${loaded.length}/${components.length} loaded (${loaded.join(', ')})`;
            
            // Elements count
            const heroElements = document.querySelectorAll('.yourstore-hero-section').length;
            document.getElementById('elements-count').textContent = `${heroElements} hero elements found`;
        }

        // Test manual Vue component
        function testManualVue() {
            if (typeof Vue === 'undefined') {
                console.error('Vue.js not loaded');
                return;
            }

            const { createApp, ref } = Vue;
            
            const TestComponent = {
                setup() {
                    const message = ref('Manual Vue component working! 🎉');
                    return { message };
                },
                template: `
                    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
                        <p class="font-bold">{{ message }}</p>
                        <p class="text-sm">This proves Vue.js is working correctly.</p>
                    </div>
                `
            };

            try {
                const app = createApp(TestComponent);
                app.mount('#manual-vue-test');
                console.log('Manual Vue component mounted successfully');
            } catch (error) {
                console.error('Failed to mount manual Vue component:', error);
            }
        }

        // Initialize debugging
        setTimeout(() => {
            updateDebugInfo();
            testManualVue();
            
            // Try to load YourStore components
            const scriptUrls = [
                '/wp-content/plugins/yourstore-commerce/assets/js/components/hero-section.js',
                '/wp-content/plugins/yourstore-commerce/assets/js/components/featured-products.js',
                '/wp-content/plugins/yourstore-commerce/assets/js/components/category-showcase.js',
                '/wp-content/plugins/yourstore-commerce/assets/js/components/newsletter-signup.js',
                '/wp-content/plugins/yourstore-commerce/assets/js/components/site-footer.js',
                '/wp-content/plugins/yourstore-commerce/assets/js/app.js'
            ];

            function loadScript(url) {
                return new Promise((resolve, reject) => {
                    const script = document.createElement('script');
                    script.src = url;
                    script.onload = () => {
                        console.log(`Loaded: ${url}`);
                        resolve();
                    };
                    script.onerror = () => {
                        console.error(`Failed to load: ${url}`);
                        reject(new Error(`Failed to load ${url}`));
                    };
                    document.head.appendChild(script);
                });
            }

            // Load all scripts
            async function loadAllScripts() {
                for (const url of scriptUrls) {
                    try {
                        await loadScript(url);
                    } catch (error) {
                        console.error(`Script loading failed:`, error);
                    }
                }
                
                // Update debug info after loading
                setTimeout(() => {
                    updateDebugInfo();
                }, 1000);
            }

            loadAllScripts();

        }, 1000);

        // Update debug info every 2 seconds
        setInterval(updateDebugInfo, 2000);
    </script>
</body>
</html>
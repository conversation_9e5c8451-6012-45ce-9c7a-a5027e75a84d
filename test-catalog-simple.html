<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product Catalog Test</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body { font-family: 'Inter', sans-serif; }
        .font-heading { font-family: 'Plus Jakarta Sans', sans-serif; }
        .font-mono { font-family: 'JetBrains Mono', monospace; }
    </style>
</head>
<body class="bg-gray-50">
    <div class="container mx-auto p-8">
        <h1 class="text-3xl font-bold mb-8">Product Catalog Component Test</h1>
        
        <!-- Test Component Mount Point -->
        <div id="catalog-test" class="yourstore-product-catalog" 
             data-title="Test Catalog" 
             data-subtitle="Testing the component" 
             data-show-filters="true" 
             data-show-sort="true" 
             data-show-pagination="true" 
             data-products-per-page="12">
        </div>
    </div>

    <!-- Load YourStore API and Components -->
    <script>
        // Mock YourStoreCommerce config
        window.yourStoreCommerce = {
            ajaxUrl: '/wp-admin/admin-ajax.php',
            nonce: 'test-nonce',
            debug: true
        };
    </script>
    
    <!-- Load the API utils and Product Catalog component -->
    <script src="/wp-content/plugins/yourstore-commerce/assets/js/api-utils.js"></script>
    <script src="/wp-content/plugins/yourstore-commerce/assets/js/components/product-catalog.js"></script>
    
    <script>
        // Mount the component when everything loads
        document.addEventListener('DOMContentLoaded', function() {
            if (window.Vue && window.ProductCatalog) {
                const element = document.querySelector('.yourstore-product-catalog');
                if (element) {
                    const props = {
                        title: element.dataset.title || 'Test Catalog',
                        subtitle: element.dataset.subtitle || 'Testing component',
                        showFilters: element.dataset.showFilters === 'true',
                        showSort: element.dataset.showSort === 'true',
                        showPagination: element.dataset.showPagination === 'true',
                        productsPerPage: parseInt(element.dataset.productsPerPage) || 12
                    };
                    
                    const app = Vue.createApp(window.ProductCatalog, props);
                    app.mount(element);
                    console.log('Product Catalog mounted successfully');
                }
            } else {
                console.error('Vue or ProductCatalog component not loaded');
            }
        });
    </script>
</body>
</html>
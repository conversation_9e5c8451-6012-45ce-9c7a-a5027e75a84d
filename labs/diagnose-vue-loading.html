<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YourStore Vue Component Loading Diagnosis</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .diagnostic-section {
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        .success { border-color: #10b981; background-color: #f0fdf4; }
        .error { border-color: #ef4444; background-color: #fef2f2; }
        .warning { border-color: #f59e0b; background-color: #fffbeb; }
        .info { border-color: #3b82f6; background-color: #eff6ff; }
        .log-output {
            background: #1a1a1a;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            padding: 1rem;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
            font-size: 12px;
            line-height: 1.4;
        }
        .component-test-area {
            min-height: 150px;
            border: 2px dashed #d1d5db;
            border-radius: 8px;
            padding: 1rem;
            margin: 0.5rem 0;
            background: #f9fafb;
        }
    </style>
</head>
<body class="bg-gray-50 p-4">
    <div class="max-w-7xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-900 mb-6">YourStore Vue Component Loading Diagnosis</h1>
        
        <!-- System Status -->
        <div id="system-status" class="diagnostic-section info">
            <h2 class="text-xl font-semibold mb-4">🔍 System Status</h2>
            <div id="system-info" class="space-y-2 text-sm"></div>
        </div>
        
        <!-- Asset Loading Status -->
        <div id="asset-status" class="diagnostic-section">
            <h2 class="text-xl font-semibold mb-4">📦 Asset Loading Status</h2>
            <div id="asset-info" class="space-y-2 text-sm"></div>
        </div>
        
        <!-- Component Registration Status -->
        <div id="component-status" class="diagnostic-section">
            <h2 class="text-xl font-semibold mb-4">🧩 Component Registration Status</h2>
            <div id="component-info" class="space-y-2 text-sm"></div>
        </div>
        
        <!-- Live Component Tests -->
        <div class="diagnostic-section">
            <h2 class="text-xl font-semibold mb-4">🧪 Live Component Tests</h2>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
                
                <!-- Hero Section Test -->
                <div class="component-test-area">
                    <h3 class="font-medium mb-2">Hero Section Component</h3>
                    <div class="yourstore-hero-section" 
                         data-props='{"title":"Diagnostic Hero","subtitle":"Testing component loading","cta_text":"Test Button","cta_url":"#test"}'></div>
                </div>
                
                <!-- Featured Products Test -->
                <div class="component-test-area">
                    <h3 class="font-medium mb-2">Featured Products Component</h3>
                    <div class="yourstore-featured-products" 
                         data-props='{"title":"Diagnostic Products","limit":4,"columns":2}'></div>
                </div>
                
                <!-- Product Grid Test -->
                <div class="component-test-area">
                    <h3 class="font-medium mb-2">Product Grid Component</h3>
                    <div class="yourstore-product-grid" 
                         data-props='{"limit":6,"columns":3,"categories":["electronics"]}'></div>
                </div>
                
                <!-- Category Showcase Test -->
                <div class="component-test-area">
                    <h3 class="font-medium mb-2">Category Showcase Component</h3>
                    <div class="yourstore-category-showcase" 
                         data-props='{"title":"Diagnostic Categories","columns":3}'></div>
                </div>
                
            </div>
        </div>
        
        <!-- Diagnostic Log -->
        <div class="diagnostic-section">
            <h2 class="text-xl font-semibold mb-4">📋 Diagnostic Log</h2>
            <div id="diagnostic-log" class="log-output"></div>
        </div>
        
        <!-- Recommendations -->
        <div id="recommendations" class="diagnostic-section">
            <h2 class="text-xl font-semibold mb-4">💡 Recommendations</h2>
            <div id="recommendation-content" class="space-y-2"></div>
        </div>
    </div>

    <script>
        // Diagnostic utilities
        const log = document.getElementById('diagnostic-log');
        const systemInfo = document.getElementById('system-info');
        const assetInfo = document.getElementById('asset-info');
        const componentInfo = document.getElementById('component-info');
        const recommendationContent = document.getElementById('recommendation-content');
        
        // Logging function
        function logMessage(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const colors = {
                info: '#00ff00',
                warn: '#ffff00', 
                error: '#ff0000',
                success: '#00ff88'
            };
            
            log.innerHTML += `<div style="color: ${colors[type] || colors.info}">[${timestamp}] ${message}</div>`;
            log.scrollTop = log.scrollHeight;
            console.log(`[YourStore Diagnostic] ${message}`);
        }
        
        // System status check
        function checkSystemStatus() {
            logMessage('=== Starting System Diagnosis ===', 'info');
            
            const checks = [
                {
                    name: 'Vue.js Availability',
                    test: () => typeof Vue !== 'undefined',
                    details: () => typeof Vue !== 'undefined' ? `Version: ${Vue.version || 'Unknown'}` : 'Vue.js not loaded'
                },
                {
                    name: 'Document Ready State',
                    test: () => document.readyState === 'complete',
                    details: () => `State: ${document.readyState}`
                },
                {
                    name: 'Tailwind CSS',
                    test: () => {
                        const testEl = document.createElement('div');
                        testEl.className = 'bg-red-500';
                        document.body.appendChild(testEl);
                        const computed = window.getComputedStyle(testEl);
                        const hasRed = computed.backgroundColor.includes('rgb(239, 68, 68)') || computed.backgroundColor.includes('#ef4444');
                        document.body.removeChild(testEl);
                        return hasRed;
                    },
                    details: () => 'Tailwind CSS utility classes working'
                },
                {
                    name: 'Console Errors',
                    test: () => {
                        // This is a basic check - in real scenarios, you'd monitor console.error
                        return true;
                    },
                    details: () => 'Check browser console for JavaScript errors'
                }
            ];
            
            let passedChecks = 0;
            checks.forEach(check => {
                try {
                    const passed = check.test();
                    const icon = passed ? '✅' : '❌';
                    const status = passed ? 'PASS' : 'FAIL';
                    const details = check.details();
                    
                    systemInfo.innerHTML += `<div class="${passed ? 'text-green-600' : 'text-red-600'}">${icon} <strong>${check.name}:</strong> ${status} - ${details}</div>`;
                    
                    if (passed) passedChecks++;
                    logMessage(`${check.name}: ${status} - ${details}`, passed ? 'success' : 'error');
                } catch (error) {
                    systemInfo.innerHTML += `<div class="text-red-600">❌ <strong>${check.name}:</strong> ERROR - ${error.message}</div>`;
                    logMessage(`${check.name}: ERROR - ${error.message}`, 'error');
                }
            });
            
            // Update system status section class
            const systemSection = document.getElementById('system-status');
            if (passedChecks === checks.length) {
                systemSection.className = 'diagnostic-section success';
            } else if (passedChecks > 0) {
                systemSection.className = 'diagnostic-section warning';
            } else {
                systemSection.className = 'diagnostic-section error';
            }
        }
        
        // Asset loading check
        function checkAssetLoading() {
            logMessage('=== Checking Asset Loading ===', 'info');
            
            const expectedAssets = [
                '/wp-content/plugins/yourstore-commerce/assets/js/components/hero-section.js',
                '/wp-content/plugins/yourstore-commerce/assets/js/components/featured-products.js',
                '/wp-content/plugins/yourstore-commerce/assets/js/components/product-grid.js',
                '/wp-content/plugins/yourstore-commerce/assets/js/components/category-showcase.js',
                '/wp-content/plugins/yourstore-commerce/assets/js/app.js'
            ];
            
            let loadedAssets = 0;
            const assetPromises = expectedAssets.map(asset => {
                return new Promise((resolve) => {
                    const script = document.createElement('script');
                    script.src = asset;
                    script.onload = () => {
                        assetInfo.innerHTML += `<div class="text-green-600">✅ <strong>Loaded:</strong> ${asset}</div>`;
                        logMessage(`Asset loaded: ${asset}`, 'success');
                        loadedAssets++;
                        resolve(true);
                    };
                    script.onerror = () => {
                        assetInfo.innerHTML += `<div class="text-red-600">❌ <strong>Failed:</strong> ${asset}</div>`;
                        logMessage(`Asset failed: ${asset}`, 'error');
                        resolve(false);
                    };
                    document.head.appendChild(script);
                });
            });
            
            Promise.all(assetPromises).then(() => {
                const assetSection = document.getElementById('asset-status');
                if (loadedAssets === expectedAssets.length) {
                    assetSection.className = 'diagnostic-section success';
                } else if (loadedAssets > 0) {
                    assetSection.className = 'diagnostic-section warning';
                } else {
                    assetSection.className = 'diagnostic-section error';
                }
                
                assetInfo.innerHTML += `<div class="mt-4 p-2 bg-blue-50 rounded"><strong>Summary:</strong> ${loadedAssets}/${expectedAssets.length} assets loaded</div>`;
                logMessage(`Asset loading complete: ${loadedAssets}/${expectedAssets.length}`, loadedAssets === expectedAssets.length ? 'success' : 'warn');
                
                // Check components after assets load
                setTimeout(checkComponentRegistration, 1000);
            });
        }
        
        // Component registration check
        function checkComponentRegistration() {
            logMessage('=== Checking Component Registration ===', 'info');
            
            const expectedComponents = [
                'HeroSection',
                'FeaturedProducts',
                'ProductGrid',
                'CategoryShowcase',
                'NewsletterSignup',
                'SiteFooter'
            ];
            
            let registeredComponents = 0;
            expectedComponents.forEach(comp => {
                const isRegistered = typeof window[comp] !== 'undefined';
                const icon = isRegistered ? '✅' : '❌';
                const status = isRegistered ? 'Registered' : 'Missing';
                const color = isRegistered ? 'text-green-600' : 'text-red-600';
                
                componentInfo.innerHTML += `<div class="${color}">${icon} <strong>${comp}:</strong> ${status}</div>`;
                logMessage(`Component ${comp}: ${status}`, isRegistered ? 'success' : 'error');
                
                if (isRegistered) registeredComponents++;
            });
            
            // Check for any component-like objects on window
            const windowKeys = Object.keys(window).filter(key => 
                key.includes('Section') || 
                key.includes('Products') || 
                key.includes('Grid') || 
                key.includes('Showcase') || 
                key.includes('Newsletter') || 
                key.includes('Footer')
            );
            
            if (windowKeys.length > 0) {
                componentInfo.innerHTML += `<div class="mt-4 p-2 bg-blue-50 rounded"><strong>Found on window:</strong> ${windowKeys.join(', ')}</div>`;
                logMessage(`Additional component-like objects found: ${windowKeys.join(', ')}`, 'info');
            }
            
            componentInfo.innerHTML += `<div class="mt-4 p-2 bg-blue-50 rounded"><strong>Summary:</strong> ${registeredComponents}/${expectedComponents.length} components registered</div>`;
            
            const componentSection = document.getElementById('component-status');
            if (registeredComponents === expectedComponents.length) {
                componentSection.className = 'diagnostic-section success';
            } else if (registeredComponents > 0) {
                componentSection.className = 'diagnostic-section warning';
            } else {
                componentSection.className = 'diagnostic-section error';
            }
            
            // Test component mounting
            setTimeout(testComponentMounting, 1000);
        }
        
        // Component mounting test
        function testComponentMounting() {
            logMessage('=== Testing Component Mounting ===', 'info');
            
            const componentElements = document.querySelectorAll('[class*="yourstore-"]');
            let mountedComponents = 0;
            
            componentElements.forEach((element, index) => {
                const className = element.className;
                const hasProps = element.dataset.props;
                const componentType = className.split(' ')[0].replace('yourstore-', '');
                
                logMessage(`Testing component ${index + 1}: ${componentType}`, 'info');
                
                if (hasProps) {
                    try {
                        const props = JSON.parse(hasProps);
                        logMessage(`Component ${index + 1} props: ${JSON.stringify(props)}`, 'info');
                        
                        // Try to mount if component exists
                        const componentName = componentType.split('-').map(word => 
                            word.charAt(0).toUpperCase() + word.slice(1)
                        ).join('');
                        
                        if (window[componentName]) {
                            // Simulate mounting (in real scenario, Vue would handle this)
                            element.innerHTML = `<div class="p-4 bg-green-100 text-green-800 rounded">✅ ${componentName} component would mount here with props: ${JSON.stringify(props)}</div>`;
                            mountedComponents++;
                            logMessage(`Component ${componentName} ready to mount`, 'success');
                        } else {
                            element.innerHTML = `<div class="p-4 bg-red-100 text-red-800 rounded">❌ ${componentName} component not found on window object</div>`;
                            logMessage(`Component ${componentName} not available for mounting`, 'error');
                        }
                    } catch (error) {
                        element.innerHTML = `<div class="p-4 bg-red-100 text-red-800 rounded">❌ Invalid props JSON: ${error.message}</div>`;
                        logMessage(`Component ${index + 1} props error: ${error.message}`, 'error');
                    }
                } else {
                    element.innerHTML = `<div class="p-4 bg-yellow-100 text-yellow-800 rounded">⚠️ No props data found for ${componentType}</div>`;
                    logMessage(`Component ${index + 1} missing props data`, 'warn');
                }
            });
            
            logMessage(`Component mounting test complete: ${mountedComponents}/${componentElements.length} components ready`, 'info');
            
            // Generate recommendations
            generateRecommendations();
        }
        
        // Generate recommendations
        function generateRecommendations() {
            logMessage('=== Generating Recommendations ===', 'info');
            
            const recommendations = [];
            
            // Check Vue availability
            if (typeof Vue === 'undefined') {
                recommendations.push({
                    type: 'error',
                    title: 'Vue.js Not Available',
                    description: 'Vue.js is not loaded. Check that the CDN link is working and loading before component scripts.',
                    action: 'Verify Vue.js CDN link and loading order'
                });
            }
            
            // Check component registration
            const expectedComponents = ['HeroSection', 'FeaturedProducts', 'ProductGrid', 'CategoryShowcase'];
            const missingComponents = expectedComponents.filter(comp => typeof window[comp] === 'undefined');
            
            if (missingComponents.length > 0) {
                recommendations.push({
                    type: 'error',
                    title: 'Missing Vue Components',
                    description: `Components not found: ${missingComponents.join(', ')}`,
                    action: 'Check component script loading and ensure they register on window object'
                });
            }
            
            // Check for WordPress integration
            if (window.location.pathname.includes('/labs/')) {
                recommendations.push({
                    type: 'info',
                    title: 'Testing Environment',
                    description: 'You are testing outside WordPress. For full functionality, test within WordPress pages.',
                    action: 'Test on actual WordPress pages with YourStore theme active'
                });
            }
            
            // Success case
            if (recommendations.length === 0) {
                recommendations.push({
                    type: 'success',
                    title: 'All Systems Operational',
                    description: 'Vue.js and components are loading correctly.',
                    action: 'Test on live WordPress pages to verify full functionality'
                });
            }
            
            // Render recommendations
            recommendations.forEach(rec => {
                const typeColors = {
                    success: 'text-green-600 bg-green-50',
                    error: 'text-red-600 bg-red-50',
                    warning: 'text-yellow-600 bg-yellow-50',
                    info: 'text-blue-600 bg-blue-50'
                };
                
                const icons = {
                    success: '✅',
                    error: '❌',
                    warning: '⚠️',
                    info: 'ℹ️'
                };
                
                recommendationContent.innerHTML += `
                    <div class="p-4 rounded ${typeColors[rec.type]}">
                        <div class="font-semibold">${icons[rec.type]} ${rec.title}</div>
                        <div class="mt-1">${rec.description}</div>
                        <div class="mt-2 text-sm font-medium">Action: ${rec.action}</div>
                    </div>
                `;
            });
            
            logMessage('=== Diagnosis Complete ===', 'success');
        }
        
        // Initialize diagnosis
        function startDiagnosis() {
            logMessage('YourStore Vue Component Diagnosis Started', 'info');
            checkSystemStatus();
            setTimeout(checkAssetLoading, 500);
        }
        
        // Start when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', startDiagnosis);
        } else {
            startDiagnosis();
        }
    </script>
</body>
</html>

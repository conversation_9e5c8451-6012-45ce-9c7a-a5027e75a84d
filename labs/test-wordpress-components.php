<?php
/**
 * Test WordPress Components Loading
 * 
 * This file tests Vue components within the WordPress environment
 */

// Load WordPress
$wp_load_paths = [
    dirname(__FILE__, 5) . '/wp-load.php',
    dirname(__FILE__, 4) . '/wp-load.php',
    dirname(__FILE__, 3) . '/wp-load.php',
];

$wp_loaded = false;
foreach ($wp_load_paths as $path) {
    if (file_exists($path)) {
        require_once $path;
        $wp_loaded = true;
        break;
    }
}

if (!$wp_loaded) {
    die('WordPress environment not found.');
}

// Enqueue WordPress head scripts
wp_head();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WordPress Vue Components Test</title>
    <?php wp_head(); ?>
    <style>
        .test-section {
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            min-height: 200px;
        }
        .test-success { border-color: #10b981; background-color: #f0fdf4; }
        .test-error { border-color: #ef4444; background-color: #fef2f2; }
        .console-output {
            background: #1a1a1a;
            color: #00ff00;
            font-family: monospace;
            padding: 1rem;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
            font-size: 12px;
        }
    </style>
</head>
<body class="bg-gray-50 p-8">
    <div class="max-w-6xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-900 mb-8">WordPress Vue Components Test</h1>
        
        <!-- WordPress Info -->
        <div class="test-section bg-blue-50 border-blue-200">
            <h2 class="text-xl font-semibold mb-4">WordPress Environment</h2>
            <div class="space-y-2 text-sm">
                <div><strong>WordPress Version:</strong> <?php echo get_bloginfo('version'); ?></div>
                <div><strong>Site URL:</strong> <?php echo get_site_url(); ?></div>
                <div><strong>Theme:</strong> <?php echo wp_get_theme()->get('Name'); ?></div>
                <div><strong>YourStore Plugin Active:</strong> <?php echo is_plugin_active('yourstore-commerce/yourstore-commerce.php') ? '✅ Yes' : '❌ No'; ?></div>
            </div>
        </div>
        
        <!-- Component Tests -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            
            <!-- Hero Section Test -->
            <div class="test-section">
                <h3 class="font-medium mb-4">Hero Section Component</h3>
                <div class="yourstore-hero-section" 
                     data-props='{"title":"WordPress Test Hero","subtitle":"Testing in WordPress environment","cta_text":"Test CTA","cta_url":"#test"}'></div>
            </div>
            
            <!-- Featured Products Test -->
            <div class="test-section">
                <h3 class="font-medium mb-4">Featured Products Component</h3>
                <div class="yourstore-featured-products" 
                     data-props='{"title":"WordPress Test Products","limit":4,"columns":2}'></div>
            </div>
            
            <!-- Product Grid Test -->
            <div class="test-section">
                <h3 class="font-medium mb-4">Product Grid Component</h3>
                <div class="yourstore-product-grid" 
                     data-props='{"limit":6,"columns":3,"categories":["electronics"]}'></div>
            </div>
            
            <!-- Category Showcase Test -->
            <div class="test-section">
                <h3 class="font-medium mb-4">Category Showcase Component</h3>
                <div class="yourstore-category-showcase" 
                     data-props='{"title":"WordPress Test Categories","columns":3}'></div>
            </div>
            
            <!-- Newsletter Signup Test -->
            <div class="test-section">
                <h3 class="font-medium mb-4">Newsletter Signup Component</h3>
                <div class="yourstore-newsletter-signup" 
                     data-props='{"title":"WordPress Test Newsletter","subtitle":"Test subscription form"}'></div>
            </div>
            
            <!-- Site Footer Test -->
            <div class="test-section">
                <h3 class="font-medium mb-4">Site Footer Component</h3>
                <div class="yourstore-site-footer" 
                     data-props='{"companyName":"WordPress Test","tagline":"Testing footer component"}'></div>
            </div>
            
        </div>
        
        <!-- Console Output -->
        <div class="test-section">
            <h2 class="text-xl font-semibold mb-4">Console Output</h2>
            <div id="console-log" class="console-output"></div>
        </div>
        
        <!-- Component Status -->
        <div class="test-section">
            <h2 class="text-xl font-semibold mb-4">Component Status</h2>
            <div id="component-status" class="space-y-2"></div>
        </div>
    </div>

    <script>
        // Console capture
        const consoleLog = document.getElementById('console-log');
        const componentStatus = document.getElementById('component-status');
        
        const originalLog = console.log;
        const originalWarn = console.warn;
        const originalError = console.error;
        
        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? '#ff0000' : type === 'warn' ? '#ffff00' : '#00ff00';
            consoleLog.innerHTML += `<div style="color: ${color}">[${timestamp}] ${message}</div>`;
            consoleLog.scrollTop = consoleLog.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToConsole(args.join(' '), 'warn');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };
        
        // Component status check
        function checkComponents() {
            const expectedComponents = [
                'HeroSection',
                'FeaturedProducts',
                'ProductGrid',
                'CategoryShowcase',
                'NewsletterSignup',
                'SiteFooter'
            ];
            
            let statusHTML = '';
            let loadedCount = 0;
            
            expectedComponents.forEach(comp => {
                const isLoaded = typeof window[comp] !== 'undefined';
                const icon = isLoaded ? '✅' : '❌';
                const color = isLoaded ? 'text-green-600' : 'text-red-600';
                
                statusHTML += `<div class="${color}">${icon} <strong>${comp}:</strong> ${isLoaded ? 'Loaded' : 'Missing'}</div>`;
                
                if (isLoaded) loadedCount++;
            });
            
            statusHTML += `<div class="mt-4 p-2 bg-blue-50 rounded"><strong>Summary:</strong> ${loadedCount}/${expectedComponents.length} components loaded</div>`;
            
            // Check Vue availability
            statusHTML += `<div class="mt-2"><strong>Vue.js:</strong> ${typeof Vue !== 'undefined' ? '✅ Available (v' + (Vue.version || 'unknown') + ')' : '❌ Not Available'}</div>`;
            
            componentStatus.innerHTML = statusHTML;
            
            console.log(`Component check: ${loadedCount}/${expectedComponents.length} loaded`);
            
            // Test component mounting
            setTimeout(testMounting, 2000);
        }
        
        function testMounting() {
            console.log('Testing component mounting...');
            
            const componentElements = document.querySelectorAll('[class*="yourstore-"]');
            console.log(`Found ${componentElements.length} component elements`);
            
            componentElements.forEach((element, index) => {
                const className = element.className;
                const hasProps = element.dataset.props;
                const hasMounted = element.__vue_app;
                
                console.log(`Component ${index + 1}: ${className}, Props: ${hasProps ? 'Yes' : 'No'}, Mounted: ${hasMounted ? 'Yes' : 'No'}`);
            });
        }
        
        // Initialize
        console.log('=== WordPress Vue Components Test Started ===');
        console.log('WordPress Site:', '<?php echo get_site_url(); ?>');
        console.log('Plugin Active:', <?php echo is_plugin_active('yourstore-commerce/yourstore-commerce.php') ? 'true' : 'false'; ?>);
        
        // Check components periodically
        setTimeout(checkComponents, 1000);
        setInterval(checkComponents, 5000);
    </script>
    
    <?php wp_footer(); ?>
</body>
</html>

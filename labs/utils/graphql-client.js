/**
 * GraphQL Client Utility
 * CDN-compatible GraphQL client for YourStore Commerce
 * 
 * This utility provides a lightweight GraphQL client that works with
 * CDN-loaded libraries (no build tools required).
 */

// GraphQL Client Configuration
const GRAPHQL_CONFIG = {
    endpoint: '/graphql',
    timeout: 30000,
    retryAttempts: 3,
    retryDelay: 1000,
    enableCache: true,
    cacheTimeout: 5 * 60 * 1000 // 5 minutes
};

/**
 * Simple GraphQL Client Class
 */
class GraphQLClient {
    constructor(config = {}) {
        this.config = { ...GRAPHQL_CONFIG, ...config };
        this.cache = new Map();
        this.loadingStates = new Map();
        this.errorStates = new Map();
        
        // Bind methods to maintain context
        this.query = this.query.bind(this);
        this.mutate = this.mutate.bind(this);
        this.subscribe = this.subscribe.bind(this);
    }

    /**
     * Generate cache key for query
     */
    getCacheKey(query, variables = {}) {
        return btoa(JSON.stringify({ query: query.trim(), variables }));
    }

    /**
     * Check if cached result is still valid
     */
    isCacheValid(timestamp) {
        return Date.now() - timestamp < this.config.cacheTimeout;
    }

    /**
     * Get authentication headers
     */
    getAuthHeaders() {
        const headers = {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        };

        // Add WordPress nonce if available
        if (window.wpApiSettings && window.wpApiSettings.nonce) {
            headers['X-WP-Nonce'] = window.wpApiSettings.nonce;
        }

        // Add application password or JWT token if available
        const authToken = localStorage.getItem('graphql_auth_token') || 
                         sessionStorage.getItem('graphql_auth_token');
        if (authToken) {
            headers['Authorization'] = `Bearer ${authToken}`;
        }

        return headers;
    }

    /**
     * Make HTTP request with retry logic
     */
    async makeRequest(query, variables = {}, operationType = 'query') {
        const payload = {
            query: query.trim(),
            variables
        };

        for (let attempt = 1; attempt <= this.config.retryAttempts; attempt++) {
            try {
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);

                const response = await fetch(this.config.endpoint, {
                    method: 'POST',
                    headers: this.getAuthHeaders(),
                    body: JSON.stringify(payload),
                    signal: controller.signal
                });

                clearTimeout(timeoutId);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();

                // Handle GraphQL errors
                if (result.errors && result.errors.length > 0) {
                    const error = new Error(result.errors[0].message);
                    error.graphQLErrors = result.errors;
                    error.data = result.data;
                    throw error;
                }

                return result;

            } catch (error) {
                // Don't retry on abort or GraphQL errors
                if (error.name === 'AbortError' || error.graphQLErrors) {
                    throw error;
                }

                // Retry on network errors
                if (attempt < this.config.retryAttempts) {
                    await new Promise(resolve => 
                        setTimeout(resolve, this.config.retryDelay * attempt)
                    );
                    continue;
                }

                throw error;
            }
        }
    }

    /**
     * Execute GraphQL Query with caching
     */
    async query(query, variables = {}, options = {}) {
        const cacheKey = this.getCacheKey(query, variables);
        const useCache = options.cache !== false && this.config.enableCache;

        // Return cached result if valid
        if (useCache && this.cache.has(cacheKey)) {
            const cached = this.cache.get(cacheKey);
            if (this.isCacheValid(cached.timestamp)) {
                return cached.data;
            } else {
                this.cache.delete(cacheKey);
            }
        }

        // Prevent duplicate requests
        if (this.loadingStates.has(cacheKey)) {
            return this.loadingStates.get(cacheKey);
        }

        // Execute query
        const queryPromise = this.makeRequest(query, variables, 'query')
            .then(result => {
                // Cache successful results
                if (useCache && result.data) {
                    this.cache.set(cacheKey, {
                        data: result,
                        timestamp: Date.now()
                    });
                }

                this.loadingStates.delete(cacheKey);
                this.errorStates.delete(cacheKey);
                return result;
            })
            .catch(error => {
                this.loadingStates.delete(cacheKey);
                this.errorStates.set(cacheKey, error);
                throw error;
            });

        this.loadingStates.set(cacheKey, queryPromise);
        return queryPromise;
    }

    /**
     * Execute GraphQL Mutation (no caching)
     */
    async mutate(mutation, variables = {}) {
        try {
            const result = await this.makeRequest(mutation, variables, 'mutation');
            
            // Clear related cache entries after mutations
            if (this.config.enableCache) {
                this.clearRelatedCache(mutation);
            }

            return result;
        } catch (error) {
            console.error('GraphQL Mutation Error:', error);
            throw error;
        }
    }

    /**
     * Clear cache entries related to a mutation
     */
    clearRelatedCache(mutation) {
        const mutationLower = mutation.toLowerCase();
        
        // Define cache invalidation patterns
        const invalidationPatterns = [
            { pattern: 'product', keywords: ['addtocart', 'updatecart', 'removeitem'] },
            { pattern: 'user', keywords: ['login', 'register', 'updateuser'] },
            { pattern: 'order', keywords: ['createorder', 'updateorder'] }
        ];

        for (const { pattern, keywords } of invalidationPatterns) {
            if (keywords.some(keyword => mutationLower.includes(keyword))) {
                for (const [key] of this.cache) {
                    if (key.includes(pattern)) {
                        this.cache.delete(key);
                    }
                }
            }
        }
    }

    /**
     * Subscribe to loading states
     */
    subscribe(query, variables = {}, callback) {
        const cacheKey = this.getCacheKey(query, variables);
        
        const unsubscribe = () => {
            // Implementation for real-time subscriptions would go here
            // For now, this is a placeholder for future WebSocket support
        };

        return { unsubscribe };
    }

    /**
     * Clear all cache
     */
    clearCache() {
        this.cache.clear();
        this.loadingStates.clear();
        this.errorStates.clear();
    }

    /**
     * Get loading state for query
     */
    isLoading(query, variables = {}) {
        const cacheKey = this.getCacheKey(query, variables);
        return this.loadingStates.has(cacheKey);
    }

    /**
     * Get error state for query
     */
    getError(query, variables = {}) {
        const cacheKey = this.getCacheKey(query, variables);
        return this.errorStates.get(cacheKey) || null;
    }
}

/**
 * Predefined GraphQL Queries
 */
const GRAPHQL_QUERIES = {
    // Product Queries - Basic version without complex where args
    GET_PRODUCTS: `
        query GetProducts($first: Int, $after: String) {
            products(first: $first, after: $after) {
                pageInfo {
                    hasNextPage
                    hasPreviousPage
                    startCursor
                    endCursor
                }
                edges {
                    node {
                        id
                        databaseId
                        name
                        slug
                        description
                        shortDescription
                        ... on SimpleProduct {
                            featuredImage {
                                node {
                                    sourceUrl
                                    altText
                                }
                            }
                            price
                            regularPrice
                            salePrice
                            stockStatus
                            stockQuantity
                            onSale
                            featured
                            averageRating
                            reviewCount
                            galleryImages {
                                nodes {
                                    sourceUrl
                                    altText
                                }
                            }
                            productCategories {
                                nodes {
                                    name
                                    slug
                                }
                            }
                        }
                        ... on VariableProduct {
                            featuredImage {
                                node {
                                    sourceUrl
                                    altText
                                }
                            }
                            price
                            regularPrice
                            salePrice
                            stockStatus
                            stockQuantity
                            onSale
                            featured
                            averageRating
                            reviewCount
                            galleryImages {
                                nodes {
                                    sourceUrl
                                    altText
                                }
                            }
                            productCategories {
                                nodes {
                                    name
                                    slug
                                }
                            }
                        }
                        ... on ExternalProduct {
                            price
                            regularPrice
                            salePrice
                            onSale
                            featured
                            averageRating
                            reviewCount
                            galleryImages {
                                nodes {
                                    sourceUrl
                                    altText
                                }
                            }
                            productCategories {
                                nodes {
                                    name
                                    slug
                                }
                            }
                        }
                        ... on GroupProduct {
                            price
                            onSale
                            featured
                            averageRating
                            reviewCount
                            galleryImages {
                                nodes {
                                    sourceUrl
                                    altText
                                }
                            }
                            productCategories {
                                nodes {
                                    name
                                    slug
                                }
                            }
                        }
                    }
                }
            }
        }
    `,

    GET_PRODUCT_BY_ID: `
        query GetProductById($id: ID!) {
            product(id: $id, idType: DATABASE_ID) {
                id
                databaseId
                name
                slug
                description
                shortDescription
                ... on SimpleProduct {
                    price
                    regularPrice
                    salePrice
                    stockStatus
                    stockQuantity
                    onSale
                    featured
                    averageRating
                    reviewCount
                    galleryImages {
                        nodes {
                            sourceUrl
                            altText
                        }
                    }
                    productCategories {
                        nodes {
                            name
                            slug
                        }
                    }
                    productTags {
                        nodes {
                            name
                            slug
                        }
                    }
                    attributes {
                        nodes {

                            ... on GlobalProductAttribute {
                                name
                                options
                            }
                        }
                    }
                    reviews {
                        nodes {
                            id
                            content
                            date
                            status
                            author {
                                node {
                                    name
                                }
                            }
                            ... on Comment {
                                commentId
                            }
                        }
                    }
                    related {
                        nodes {
                            id
                            name
                            ... on SimpleProduct {
                                featuredImage {
                                    node {
                                        sourceUrl
                                    }
                                }
                                price
                            }
                            ... on VariableProduct {
                                featuredImage {
                                    node {
                                        sourceUrl
                                    }
                                }
                                price
                            }
                            ... on ExternalProduct {
                                featuredImage {
                                    node {
                                        sourceUrl
                                    }
                                }
                                price
                            }
                            ... on GroupProduct {
                                featuredImage {
                                    node {
                                        sourceUrl
                                    }
                                }
                                price
                            }
                        }
                    }
                }
                ... on VariableProduct {
                    price
                    regularPrice
                    salePrice
                    stockStatus
                    stockQuantity
                    onSale
                    featured
                    averageRating
                    reviewCount
                    galleryImages {
                        nodes {
                            sourceUrl
                            altText
                        }
                    }
                    productCategories {
                        nodes {
                            name
                            slug
                        }
                    }
                    productTags {
                        nodes {
                            name
                            slug
                        }
                    }
                    attributes {
                        nodes {
                            ... on GlobalProductAttribute {
                                name
                                options
                            }
                        }
                    }
                    variations {
                        nodes {
                            id
                            databaseId
                            name
                            price
                            attributes {
                                nodes {
                                    name
                                    value
                                }
                            }
                        }
                    }
                    reviews {
                        nodes {
                            id
                            content
                            date
                            status
                            author {
                                node {
                                    name
                                }
                            }
                            ... on Comment {
                                commentId
                            }
                        }
                    }
                    related {
                        nodes {
                            id
                            name
                            ... on SimpleProduct {
                                featuredImage {
                                    node {
                                        sourceUrl
                                    }
                                }
                                price
                            }
                            ... on VariableProduct {
                                featuredImage {
                                    node {
                                        sourceUrl
                                    }
                                }
                                price
                            }
                            ... on ExternalProduct {
                                featuredImage {
                                    node {
                                        sourceUrl
                                    }
                                }
                                price
                            }
                            ... on GroupProduct {
                                featuredImage {
                                    node {
                                        sourceUrl
                                    }
                                }
                                price
                            }
                        }
                    }
                }
                ... on ExternalProduct {
                    price
                    regularPrice
                    salePrice
                    onSale
                    featured
                    averageRating
                    reviewCount
                    galleryImages {
                        nodes {
                            sourceUrl
                            altText
                        }
                    }
                    productCategories {
                        nodes {
                            name
                            slug
                        }
                    }
                    productTags {
                        nodes {
                            name
                            slug
                        }
                    }
                }
                ... on GroupProduct {
                    price
                    onSale
                    featured
                    averageRating
                    reviewCount
                    galleryImages {
                        nodes {
                            sourceUrl
                            altText
                        }
                    }
                    productCategories {
                        nodes {
                            name
                            slug
                        }
                    }
                    productTags {
                        nodes {
                            name
                            slug
                        }
                    }
                }
            }
        }
    `,

    GET_CATEGORIES: `
        query GetCategories($first: Int) {
            productCategories(first: $first) {
                nodes {
                    id
                    databaseId
                    name
                    slug
                    description
                    count
                    image {
                        sourceUrl
                        altText
                    }
                    parent {
                        node {
                            name
                            slug
                        }
                    }
                    children {
                        nodes {
                            name
                            slug
                            count
                        }
                    }
                }
            }
        }
    `,

    SEARCH_PRODUCTS: `
        query SearchProducts($search: String!, $first: Int) {
            products(first: $first, where: { search: $search }) {
                nodes {
                    id
                    databaseId
                    name
                    slug
                    ... on SimpleProduct {
                        featuredImage {
                            node {
                                sourceUrl
                                altText
                            }
                        }
                        price
                        onSale
                        averageRating
                        productCategories {
                            nodes {
                                name
                            }
                        }
                    }
                    ... on VariableProduct {
                        featuredImage {
                            node {
                                sourceUrl
                                altText
                            }
                        }
                        price
                        onSale
                        averageRating
                        productCategories {
                            nodes {
                                name
                            }
                        }
                    }
                    ... on ExternalProduct {
                        featuredImage {
                            node {
                                sourceUrl
                                altText
                            }
                        }
                        price
                        onSale
                        averageRating
                        productCategories {
                            nodes {
                                name
                            }
                        }
                    }
                    ... on GroupProduct {
                        featuredImage {
                            node {
                                sourceUrl
                                altText
                            }
                        }
                        price
                        onSale
                        averageRating
                        productCategories {
                            nodes {
                                name
                            }
                        }
                    }
                }
            }
        }
    `,

    // WooCommerce specific review query
    GET_PRODUCT_REVIEWS: `
        query GetProductReviews($productId: Int!, $first: Int) {
            comments(where: { contentId: $productId, contentType: PRODUCT }, first: $first) {
                nodes {
                    id
                    databaseId
                    content
                    date
                    status
                    author {
                        node {
                            name
                            email
                        }
                    }
                    parent {
                        node {
                            id
                        }
                    }
                }
            }
        }
    `,

    // Alternative approach for product reviews
    GET_PRODUCT_WITH_COMMENTS: `
        query GetProductWithComments($id: ID!) {
            product(id: $id, idType: DATABASE_ID) {
                id
                name
                averageRating
                reviewCount
                commentCount
                commentStatus
            }
        }
    `
};

/**
 * Predefined GraphQL Mutations
 */
const GRAPHQL_MUTATIONS = {
    ADD_TO_CART: `
        mutation AddToCart($input: AddToCartInput!) {
            addToCart(input: $input) {
                cartItem {
                    key
                    product {
                        node {
                            id
                            databaseId
                            name
                        }
                    }
                    variation {
                        node {
                            id
                            name
                        }
                    }
                    quantity
                    total
                    subtotal
                }
                cart {
                    contents {
                        itemCount
                        productCount
                    }
                    total
                    subtotal
                    isEmpty
                }
            }
        }
    `,

    GET_CART: `
        query GetCart {
            cart {
                contents {
                    itemCount
                    productCount
                    nodes {
                        key
                        product {
                            node {
                                id
                                databaseId
                                name
                            }
                        }
                        quantity
                        total
                        subtotal
                    }
                }
                total
                subtotal
                isEmpty
            }
        }
    `,

    UPDATE_CART_ITEM: `
        mutation UpdateCartItem($input: UpdateItemQuantitiesInput!) {
            updateItemQuantities(input: $input) {
                updated {
                    key
                    quantity
                }
                cart {
                    total
                    subtotal
                    contents {
                        itemCount
                    }
                }
            }
        }
    `,

    REMOVE_CART_ITEMS: `
        mutation RemoveCartItems($input: RemoveItemsFromCartInput!) {
            removeItemsFromCart(input: $input) {
                cartItems {
                    key
                    product {
                        node {
                            name
                        }
                    }
                }
                cart {
                    total
                    subtotal
                    contents {
                        itemCount
                    }
                    isEmpty
                }
            }
        }
    `,

    CLEAR_CART: `
        mutation ClearCart($input: EmptyCartInput!) {
            emptyCart(input: $input) {
                cart {
                    total
                    subtotal
                    contents {
                        itemCount
                    }
                    isEmpty
                }
            }
        }
    `,

    REMOVE_CART_ITEM: `
        mutation RemoveCartItem($input: RemoveItemsFromCartInput!) {
            removeItemsFromCart(input: $input) {
                cartItems {
                    key
                    product {
                        node {
                            name
                        }
                    }
                }
                cart {
                    contents {
                        itemCount
                    }
                    total
                }
            }
        }
    `,

    // WordPress Core authentication (if WPGraphQL JWT plugin is installed)
    LOGIN_USER_JWT: `
        mutation LoginUserJWT($input: LoginInput!) {
            login(input: $input) {
                authToken
                refreshToken
                user {
                    id
                    databaseId
                    name
                    email
                    firstName
                    lastName
                }
            }
        }
    `,

    // Alternative login mutation format (some WPGraphQL setups)
    LOGIN_USER_ALT: `
        mutation LoginUser($username: String!, $password: String!) {
            loginUser(input: { username: $username, password: $password }) {
                jwtAuthToken
                user {
                    id
                    name
                    email
                }
            }
        }
    `,

    // WordPress user registration (if enabled)
    REGISTER_USER: `
        mutation RegisterUser($input: RegisterUserInput!) {
            registerUser(input: $input) {
                user {
                    id
                    databaseId
                    name
                    email
                }
            }
        }
    `,

    // Send password reset
    SEND_PASSWORD_RESET: `
        mutation SendPasswordResetEmail($input: SendPasswordResetEmailInput!) {
            sendPasswordResetEmail(input: $input) {
                success
            }
        }
    `,

    // Schema introspection for authentication capabilities
    INTROSPECT_AUTH: `
        query IntrospectAuth {
            __schema {
                mutationType {
                    name
                    fields {
                        name
                        args {
                            name
                            type {
                                name
                                kind
                                ofType {
                                    name
                                }
                            }
                        }
                    }
                }
                types {
                    name
                    kind
                    fields {
                        name
                        type {
                            name
                        }
                    }
                }
            }
        }
    `
};

/**
 * Global GraphQL Client Instance
 */
window.GraphQLClient = GraphQLClient;
window.GRAPHQL_QUERIES = GRAPHQL_QUERIES;
window.GRAPHQL_MUTATIONS = GRAPHQL_MUTATIONS;

// Create default client instance
window.graphqlClient = new GraphQLClient();

/**
 * Utility Functions for Vue.js Integration
 */
window.GraphQLUtils = {
    /**
     * Create reactive GraphQL query for Vue components
     */
    useQuery(query, variables = {}, options = {}) {
        return {
            data: null,
            loading: true,
            error: null,
            
            async execute() {
                try {
                    this.loading = true;
                    this.error = null;
                    const result = await window.graphqlClient.query(query, variables, options);
                    this.data = result.data;
                    return result;
                } catch (error) {
                    this.error = error;
                    console.error('GraphQL Query Error:', error);
                    throw error;
                } finally {
                    this.loading = false;
                }
            }
        };
    },

    /**
     * Create reactive GraphQL mutation for Vue components
     */
    useMutation(mutation) {
        return {
            loading: false,
            error: null,
            
            async execute(variables = {}) {
                try {
                    this.loading = true;
                    this.error = null;
                    const result = await window.graphqlClient.mutate(mutation, variables);
                    return result;
                } catch (error) {
                    this.error = error;
                    console.error('GraphQL Mutation Error:', error);
                    throw error;
                } finally {
                    this.loading = false;
                }
            }
        };
    },

    /**
     * Format GraphQL errors for user display
     */
    formatError(error) {
        if (error.graphQLErrors && error.graphQLErrors.length > 0) {
            return error.graphQLErrors[0].message;
        }
        return error.message || 'An unexpected error occurred';
    },

    /**
     * Check if user is authenticated
     */
    isAuthenticated() {
        return !!(localStorage.getItem('graphql_auth_token') || 
                 sessionStorage.getItem('graphql_auth_token'));
    },

    /**
     * Set authentication token
     */
    setAuthToken(token, persistent = false) {
        const storage = persistent ? localStorage : sessionStorage;
        storage.setItem('graphql_auth_token', token);
    },

    /**
     * Clear authentication token
     */
    clearAuthToken() {
        localStorage.removeItem('graphql_auth_token');
        sessionStorage.removeItem('graphql_auth_token');
    },

    /**
     * Detect available authentication methods in GraphQL schema
     */
    async detectAuthMethods() {
        try {
            const result = await window.graphqlClient.query(window.GRAPHQL_MUTATIONS.INTROSPECT_AUTH, {});
            const mutationType = result.data.__schema.mutationType;
            const allTypes = result.data.__schema.types;
            
            // Find auth-related mutations
            const authMutations = mutationType.fields.filter(field => 
                field.name.toLowerCase().includes('login') || 
                field.name.toLowerCase().includes('register') ||
                field.name.toLowerCase().includes('auth')
            );
            
            // Find auth-related input types
            const authInputTypes = allTypes.filter(type => 
                type.name && (
                    type.name.toLowerCase().includes('login') ||
                    type.name.toLowerCase().includes('register') ||
                    type.name.toLowerCase().includes('auth')
                ) && type.kind === 'INPUT_OBJECT'
            );
            
            return {
                mutations: authMutations,
                inputTypes: authInputTypes,
                hasGraphQLAuth: authMutations.length > 0,
                hasJWTSupport: authInputTypes.some(type => type.name.includes('Login'))
            };
            
        } catch (error) {
            console.warn('Could not detect auth methods:', error);
            return {
                mutations: [],
                inputTypes: [],
                hasGraphQLAuth: false,
                hasJWTSupport: false
            };
        }
    },

    /**
     * WordPress REST API authentication fallback
     */
    async authenticateViaREST(username, password) {
        try {
            // Try to authenticate via WordPress REST API
            const response = await fetch('/wp-json/wp/v2/users/me', {
                method: 'GET',
                headers: {
                    'Authorization': 'Basic ' + btoa(`${username}:${password}`),
                    'Content-Type': 'application/json'
                }
            });
            
            if (!response.ok) {
                throw new Error(`Authentication failed: ${response.status} ${response.statusText}`);
            }
            
            const userData = await response.json();
            
            // Store basic auth token for future requests
            this.setAuthToken(`Basic ${btoa(`${username}:${password}`)}`, false);
            
            return {
                success: true,
                method: 'WordPress REST API Basic Auth',
                user: userData
            };
            
        } catch (error) {
            throw new Error(`REST API authentication failed: ${error.message}`);
        }
    },

    /**
     * Register user via WordPress REST API
     */
    async registerViaREST(userData) {
        try {
            const response = await fetch('/wp-json/wp/v2/users', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    username: userData.username,
                    email: userData.email,
                    password: userData.password,
                    first_name: userData.firstName || '',
                    last_name: userData.lastName || ''
                })
            });
            
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(errorData.message || `Registration failed: ${response.status} ${response.statusText}`);
            }
            
            const newUser = await response.json();
            
            return {
                success: true,
                method: 'WordPress REST API',
                user: newUser
            };
            
        } catch (error) {
            throw new Error(`REST API registration failed: ${error.message}`);
        }
    }
};

console.log('✅ GraphQL Client loaded successfully!');
console.log('🔌 Available: window.graphqlClient, window.GraphQLUtils, window.GRAPHQL_QUERIES, window.GRAPHQL_MUTATIONS');
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Vue Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
        }
        .test-box {
            border: 2px solid #ccc;
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
        }
        .success { border-color: #28a745; background: #d4edda; }
        .error { border-color: #dc3545; background: #f8d7da; }
        .loading { border-color: #ffc107; background: #fff3cd; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Simple Vue Test</h1>
        
        <div id="step1" class="test-box loading">
            <h3>Step 1: Loading Vue.js from CDN</h3>
            <p>Status: <span id="vue-status">Loading...</span></p>
        </div>
        
        <div id="step2" class="test-box loading">
            <h3>Step 2: Creating Simple Vue App</h3>
            <p>Status: <span id="app-status">Waiting...</span></p>
            <div id="vue-app">
                <p>{{ message }}</p>
                <button @click="updateMessage">Click Me</button>
            </div>
        </div>
        
        <div id="step3" class="test-box loading">
            <h3>Step 3: Testing Component Mounting</h3>
            <p>Status: <span id="mount-status">Waiting...</span></p>
            <div id="test-component"></div>
        </div>
        
        <div class="test-box">
            <h3>Console Output</h3>
            <div id="console-output" style="background: #f8f9fa; padding: 10px; font-family: monospace; font-size: 12px; max-height: 200px; overflow-y: auto;"></div>
        </div>
    </div>

    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script>
        const consoleOutput = document.getElementById('console-output');
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            consoleOutput.innerHTML += `[${timestamp}] ${message}<br>`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
            console.log(`[VueTest] ${message}`);
        }
        
        function updateStatus(stepId, statusId, message, success = true) {
            const step = document.getElementById(stepId);
            const status = document.getElementById(statusId);
            
            step.className = `test-box ${success ? 'success' : 'error'}`;
            status.textContent = message;
            
            log(`${stepId}: ${message}`);
        }
        
        // Step 1: Check Vue.js
        setTimeout(() => {
            if (typeof Vue !== 'undefined') {
                updateStatus('step1', 'vue-status', `Vue.js loaded successfully (version ${Vue.version || 'unknown'})`, true);
                testVueApp();
            } else {
                updateStatus('step1', 'vue-status', 'Vue.js failed to load', false);
                log('ERROR: Vue.js is not available');
            }
        }, 1000);
        
        // Step 2: Test Vue App
        function testVueApp() {
            try {
                const { createApp } = Vue;
                
                const app = createApp({
                    data() {
                        return {
                            message: 'Hello from Vue!'
                        }
                    },
                    methods: {
                        updateMessage() {
                            this.message = 'Button clicked! Vue is working!';
                            log('Vue button clicked - reactivity working');
                        }
                    }
                });
                
                app.mount('#vue-app');
                updateStatus('step2', 'app-status', 'Vue app created and mounted successfully', true);
                testComponentMounting();
                
            } catch (error) {
                updateStatus('step2', 'app-status', `Vue app failed: ${error.message}`, false);
                log(`ERROR: Vue app creation failed - ${error.message}`);
            }
        }
        
        // Step 3: Test Component Mounting
        function testComponentMounting() {
            try {
                const { createApp } = Vue;
                
                // Create a simple component
                const TestComponent = {
                    template: `
                        <div style="padding: 10px; background: #e3f2fd; border-radius: 4px;">
                            <h4>Test Component</h4>
                            <p>Count: {{ count }}</p>
                            <button @click="increment">Increment</button>
                        </div>
                    `,
                    data() {
                        return {
                            count: 0
                        }
                    },
                    methods: {
                        increment() {
                            this.count++;
                            log(`Component count incremented to ${this.count}`);
                        }
                    },
                    mounted() {
                        log('Test component mounted successfully');
                    }
                };
                
                const componentApp = createApp(TestComponent);
                componentApp.mount('#test-component');
                
                updateStatus('step3', 'mount-status', 'Component mounted successfully', true);
                
                // Test if component stays mounted
                setTimeout(() => {
                    const element = document.getElementById('test-component');
                    if (element.innerHTML.trim() === '') {
                        updateStatus('step3', 'mount-status', 'Component disappeared after mounting!', false);
                        log('ERROR: Component content disappeared');
                    } else {
                        log('SUCCESS: Component remained stable after 3 seconds');
                    }
                }, 3000);
                
            } catch (error) {
                updateStatus('step3', 'mount-status', `Component mounting failed: ${error.message}`, false);
                log(`ERROR: Component mounting failed - ${error.message}`);
            }
        }
        
        // Global error handling
        window.addEventListener('error', (event) => {
            log(`GLOBAL ERROR: ${event.message} at ${event.filename}:${event.lineno}`);
        });
        
        window.addEventListener('unhandledrejection', (event) => {
            log(`UNHANDLED PROMISE REJECTION: ${event.reason}`);
        });
        
        log('Simple Vue Test initialized');
    </script>
</body>
</html>

<?php
/**
 * Test Shortcode Registration
 * 
 * This file tests if YourStore Commerce shortcodes are properly registered
 * and can be executed.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    // For testing outside WordPress, simulate WordPress environment
    define('ABSPATH', dirname(__FILE__, 5) . '/');
    
    // Basic WordPress simulation for testing
    if (!function_exists('add_shortcode')) {
        function add_shortcode($tag, $callback) {
            global $shortcode_tags;
            $shortcode_tags[$tag] = $callback;
        }
    }
    
    if (!function_exists('shortcode_exists')) {
        function shortcode_exists($tag) {
            global $shortcode_tags;
            return isset($shortcode_tags[$tag]);
        }
    }
    
    if (!function_exists('do_shortcode')) {
        function do_shortcode($content) {
            global $shortcode_tags;
            // Simple shortcode processing for testing
            return $content;
        }
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YourStore Shortcode Registration Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .test-section {
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        .test-success { border-color: #10b981; background-color: #f0fdf4; }
        .test-error { border-color: #ef4444; background-color: #fef2f2; }
        .test-warning { border-color: #f59e0b; background-color: #fffbeb; }
    </style>
</head>
<body class="bg-gray-50 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-900 mb-8">YourStore Shortcode Registration Test</h1>
        
        <?php
        // Test shortcode registration
        $expected_shortcodes = [
            'hero_section' => 'Hero Section Component',
            'product_grid' => 'Product Grid Component', 
            'featured_products' => 'Featured Products Component',
            'category_showcase' => 'Category Showcase Component',
            'newsletter_signup' => 'Newsletter Signup Component',
            'site_footer' => 'Site Footer Component'
        ];
        
        echo '<div class="test-section">';
        echo '<h2 class="text-xl font-semibold mb-4">Shortcode Registration Status</h2>';
        
        $registered_count = 0;
        $total_count = count($expected_shortcodes);
        
        foreach ($expected_shortcodes as $shortcode => $description) {
            $is_registered = shortcode_exists($shortcode);
            $status_class = $is_registered ? 'text-green-600' : 'text-red-600';
            $icon = $is_registered ? '✅' : '❌';
            
            if ($is_registered) {
                $registered_count++;
            }
            
            echo "<div class='mb-2 {$status_class}'>";
            echo "<strong>{$icon} [{$shortcode}]</strong> - {$description}";
            echo $is_registered ? ' (Registered)' : ' (Not Registered)';
            echo "</div>";
        }
        
        echo '<div class="mt-4 p-4 bg-blue-50 rounded">';
        echo "<strong>Summary:</strong> {$registered_count}/{$total_count} shortcodes registered";
        echo '</div>';
        echo '</div>';
        
        // Test shortcode execution
        echo '<div class="test-section">';
        echo '<h2 class="text-xl font-semibold mb-4">Shortcode Execution Test</h2>';
        
        if ($registered_count > 0) {
            echo '<div class="space-y-4">';
            
            // Test hero section shortcode
            if (shortcode_exists('hero_section')) {
                echo '<div class="border p-4 rounded">';
                echo '<h3 class="font-medium mb-2">Hero Section Test:</h3>';
                echo '<div class="bg-gray-100 p-2 rounded text-sm font-mono">';
                echo '[hero_section title="Test Hero" subtitle="Testing shortcode execution"]';
                echo '</div>';
                echo '<div class="mt-2 p-2 bg-white rounded border">';
                $hero_output = do_shortcode('[hero_section title="Test Hero" subtitle="Testing shortcode execution"]');
                echo htmlspecialchars($hero_output);
                echo '</div>';
                echo '</div>';
            }
            
            // Test product grid shortcode
            if (shortcode_exists('product_grid')) {
                echo '<div class="border p-4 rounded">';
                echo '<h3 class="font-medium mb-2">Product Grid Test:</h3>';
                echo '<div class="bg-gray-100 p-2 rounded text-sm font-mono">';
                echo '[product_grid limit="4" columns="2"]';
                echo '</div>';
                echo '<div class="mt-2 p-2 bg-white rounded border">';
                $grid_output = do_shortcode('[product_grid limit="4" columns="2"]');
                echo htmlspecialchars($grid_output);
                echo '</div>';
                echo '</div>';
            }
            
            echo '</div>';
        } else {
            echo '<div class="text-red-600">';
            echo 'No shortcodes are registered. Please check plugin activation and component initialization.';
            echo '</div>';
        }
        echo '</div>';
        
        // Debug information
        echo '<div class="test-section">';
        echo '<h2 class="text-xl font-semibold mb-4">Debug Information</h2>';
        
        echo '<div class="space-y-2 text-sm">';
        echo '<div><strong>WordPress ABSPATH:</strong> ' . (defined('ABSPATH') ? ABSPATH : 'Not defined') . '</div>';
        echo '<div><strong>Plugin Directory:</strong> ' . (defined('YOURSTORE_COMMERCE_PLUGIN_DIR') ? YOURSTORE_COMMERCE_PLUGIN_DIR : 'Not defined') . '</div>';
        echo '<div><strong>Current File:</strong> ' . __FILE__ . '</div>';
        echo '<div><strong>PHP Version:</strong> ' . PHP_VERSION . '</div>';
        
        // Check if WordPress functions exist
        $wp_functions = ['add_shortcode', 'shortcode_exists', 'do_shortcode', 'wp_enqueue_script'];
        echo '<div><strong>WordPress Functions:</strong></div>';
        foreach ($wp_functions as $func) {
            $exists = function_exists($func);
            $status = $exists ? '✅' : '❌';
            echo "<div class='ml-4'>{$status} {$func}</div>";
        }
        
        // Check global shortcode tags
        if (isset($GLOBALS['shortcode_tags'])) {
            $all_shortcodes = array_keys($GLOBALS['shortcode_tags']);
            $yourstore_shortcodes = array_filter($all_shortcodes, function($tag) {
                return strpos($tag, 'hero_') === 0 || 
                       strpos($tag, 'product_') === 0 || 
                       strpos($tag, 'featured_') === 0 || 
                       strpos($tag, 'category_') === 0 || 
                       strpos($tag, 'newsletter_') === 0 || 
                       strpos($tag, 'site_') === 0;
            });
            
            echo '<div class="mt-4">';
            echo '<strong>All YourStore Shortcodes Found:</strong>';
            if (!empty($yourstore_shortcodes)) {
                echo '<div class="ml-4">' . implode(', ', $yourstore_shortcodes) . '</div>';
            } else {
                echo '<div class="ml-4 text-red-600">None found</div>';
            }
            echo '</div>';
            
            echo '<div class="mt-2">';
            echo '<strong>Total Registered Shortcodes:</strong> ' . count($all_shortcodes);
            echo '</div>';
        }
        
        echo '</div>';
        echo '</div>';
        
        // Recommendations
        echo '<div class="test-section">';
        echo '<h2 class="text-xl font-semibold mb-4">Recommendations</h2>';
        
        if ($registered_count === 0) {
            echo '<div class="text-red-600 space-y-2">';
            echo '<div>❌ <strong>No shortcodes registered.</strong> Possible issues:</div>';
            echo '<ul class="ml-6 list-disc space-y-1">';
            echo '<li>YourStore Commerce plugin is not activated</li>';
            echo '<li>Plugin initialization failed</li>';
            echo '<li>Component classes are not loading properly</li>';
            echo '<li>WordPress hooks are not firing</li>';
            echo '</ul>';
            echo '</div>';
        } elseif ($registered_count < $total_count) {
            echo '<div class="text-yellow-600 space-y-2">';
            echo '<div>⚠️ <strong>Partial registration.</strong> Some components may have issues:</div>';
            echo '<ul class="ml-6 list-disc space-y-1">';
            echo '<li>Check error logs for component initialization failures</li>';
            echo '<li>Verify all component files exist and are properly formatted</li>';
            echo '<li>Check for PHP syntax errors in component classes</li>';
            echo '</ul>';
            echo '</div>';
        } else {
            echo '<div class="text-green-600 space-y-2">';
            echo '<div>✅ <strong>All shortcodes registered successfully!</strong></div>';
            echo '<div>Next steps:</div>';
            echo '<ul class="ml-6 list-disc space-y-1">';
            echo '<li>Test shortcode execution on actual WordPress pages</li>';
            echo '<li>Verify Vue.js components are mounting properly</li>';
            echo '<li>Check frontend styling and functionality</li>';
            echo '</ul>';
            echo '</div>';
        }
        
        echo '</div>';
        ?>
        
        <div class="mt-8 p-4 bg-blue-50 rounded">
            <h3 class="font-semibold mb-2">How to Use This Test:</h3>
            <ol class="list-decimal list-inside space-y-1 text-sm">
                <li>Access this file through your WordPress site (e.g., /wp-content/plugins/yourstore-commerce/labs/test-shortcode-registration.php)</li>
                <li>Check that all expected shortcodes are registered</li>
                <li>If shortcodes are missing, check WordPress error logs</li>
                <li>Test shortcode execution to ensure they generate proper HTML</li>
                <li>Verify Vue.js components mount on the frontend</li>
            </ol>
        </div>
    </div>
</body>
</html>

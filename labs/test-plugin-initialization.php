<?php
/**
 * Test Plugin Initialization
 * 
 * This file tests if the YourStore Commerce plugin is properly initialized
 * and all components are loading correctly.
 */

// WordPress environment check
if (!defined('ABSPATH')) {
    // Try to load WordPress
    $wp_load_paths = [
        dirname(__FILE__, 5) . '/wp-load.php',
        dirname(__FILE__, 4) . '/wp-load.php',
        dirname(__FILE__, 3) . '/wp-load.php',
    ];
    
    $wp_loaded = false;
    foreach ($wp_load_paths as $path) {
        if (file_exists($path)) {
            require_once $path;
            $wp_loaded = true;
            break;
        }
    }
    
    if (!$wp_loaded) {
        die('WordPress environment not found. Please run this file through WordPress.');
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YourStore Plugin Initialization Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .test-section {
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        .test-success { border-color: #10b981; background-color: #f0fdf4; }
        .test-error { border-color: #ef4444; background-color: #fef2f2; }
        .test-warning { border-color: #f59e0b; background-color: #fffbeb; }
        .test-info { border-color: #3b82f6; background-color: #eff6ff; }
    </style>
</head>
<body class="bg-gray-50 p-8">
    <div class="max-w-6xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-900 mb-8">YourStore Plugin Initialization Test</h1>
        
        <?php
        // Test 1: WordPress Environment
        echo '<div class="test-section test-info">';
        echo '<h2 class="text-xl font-semibold mb-4">🌐 WordPress Environment</h2>';
        echo '<div class="space-y-2 text-sm">';
        echo '<div><strong>WordPress Version:</strong> ' . get_bloginfo('version') . '</div>';
        echo '<div><strong>PHP Version:</strong> ' . PHP_VERSION . '</div>';
        echo '<div><strong>Active Theme:</strong> ' . wp_get_theme()->get('Name') . '</div>';
        echo '<div><strong>Site URL:</strong> ' . get_site_url() . '</div>';
        echo '<div><strong>Admin URL:</strong> ' . admin_url() . '</div>';
        echo '</div>';
        echo '</div>';
        
        // Test 2: Plugin Status
        echo '<div class="test-section">';
        echo '<h2 class="text-xl font-semibold mb-4">🔌 Plugin Status</h2>';
        
        $plugin_file = 'yourstore-commerce/yourstore-commerce.php';
        $is_plugin_active = is_plugin_active($plugin_file);
        $plugin_data = get_plugin_data(WP_PLUGIN_DIR . '/' . $plugin_file);
        
        $section_class = $is_plugin_active ? 'test-success' : 'test-error';
        echo '<script>document.currentScript.parentElement.className += " ' . $section_class . '";</script>';
        
        echo '<div class="space-y-2 text-sm">';
        echo '<div class="' . ($is_plugin_active ? 'text-green-600' : 'text-red-600') . '">';
        echo '<strong>Plugin Active:</strong> ' . ($is_plugin_active ? '✅ Yes' : '❌ No');
        echo '</div>';
        
        if (!empty($plugin_data['Name'])) {
            echo '<div><strong>Plugin Name:</strong> ' . $plugin_data['Name'] . '</div>';
            echo '<div><strong>Version:</strong> ' . $plugin_data['Version'] . '</div>';
            echo '<div><strong>Description:</strong> ' . $plugin_data['Description'] . '</div>';
        }
        
        echo '</div>';
        echo '</div>';
        
        // Test 3: Plugin Constants
        echo '<div class="test-section">';
        echo '<h2 class="text-xl font-semibold mb-4">📋 Plugin Constants</h2>';
        
        $constants = [
            'YOURSTORE_COMMERCE_VERSION',
            'YOURSTORE_COMMERCE_PLUGIN_FILE',
            'YOURSTORE_COMMERCE_PLUGIN_DIR',
            'YOURSTORE_COMMERCE_PLUGIN_URL',
            'YOURSTORE_COMMERCE_PLUGIN_BASENAME'
        ];
        
        $defined_constants = 0;
        echo '<div class="space-y-2 text-sm">';
        foreach ($constants as $constant) {
            $is_defined = defined($constant);
            $icon = $is_defined ? '✅' : '❌';
            $color = $is_defined ? 'text-green-600' : 'text-red-600';
            $value = $is_defined ? constant($constant) : 'Not defined';
            
            echo "<div class='{$color}'>{$icon} <strong>{$constant}:</strong> {$value}</div>";
            if ($is_defined) $defined_constants++;
        }
        echo '</div>';
        
        $section_class = $defined_constants === count($constants) ? 'test-success' : 
                        ($defined_constants > 0 ? 'test-warning' : 'test-error');
        echo '<script>document.currentScript.parentElement.className += " ' . $section_class . '";</script>';
        echo '</div>';
        
        // Test 4: Class Autoloading
        echo '<div class="test-section">';
        echo '<h2 class="text-xl font-semibold mb-4">🏗️ Class Autoloading</h2>';
        
        $classes_to_test = [
            'YourStore\\Commerce\\Abstracts\\BaseComponent',
            'YourStore\\Commerce\\Components\\HeroSectionComponent',
            'YourStore\\Commerce\\Components\\ProductGridComponent',
            'YourStore\\Commerce\\Components\\FeaturedProductsComponent',
            'YourStore\\Commerce\\Blocks\\BlocksManager',
            'YourStore\\Commerce\\Blocks\\CategoryShowcaseBlock',
            'YourStore\\Commerce\\Blocks\\NewsletterSignupBlock',
            'YourStore\\Commerce\\Blocks\\SiteFooterBlock'
        ];
        
        $loaded_classes = 0;
        echo '<div class="space-y-2 text-sm">';
        foreach ($classes_to_test as $class) {
            $class_exists = class_exists($class);
            $icon = $class_exists ? '✅' : '❌';
            $color = $class_exists ? 'text-green-600' : 'text-red-600';
            $status = $class_exists ? 'Loaded' : 'Not Found';
            
            echo "<div class='{$color}'>{$icon} <strong>{$class}:</strong> {$status}</div>";
            if ($class_exists) $loaded_classes++;
        }
        echo '</div>';
        
        $section_class = $loaded_classes === count($classes_to_test) ? 'test-success' : 
                        ($loaded_classes > 0 ? 'test-warning' : 'test-error');
        echo '<script>document.currentScript.parentElement.className += " ' . $section_class . '";</script>';
        echo '</div>';
        
        // Test 5: Shortcode Registration
        echo '<div class="test-section">';
        echo '<h2 class="text-xl font-semibold mb-4">📝 Shortcode Registration</h2>';
        
        $expected_shortcodes = [
            'hero_section',
            'product_grid', 
            'featured_products'
        ];
        
        $registered_shortcodes = 0;
        echo '<div class="space-y-2 text-sm">';
        foreach ($expected_shortcodes as $shortcode) {
            $is_registered = shortcode_exists($shortcode);
            $icon = $is_registered ? '✅' : '❌';
            $color = $is_registered ? 'text-green-600' : 'text-red-600';
            $status = $is_registered ? 'Registered' : 'Not Registered';
            
            echo "<div class='{$color}'>{$icon} <strong>[{$shortcode}]:</strong> {$status}</div>";
            if ($is_registered) $registered_shortcodes++;
        }
        
        // Show all registered shortcodes
        global $shortcode_tags;
        if (!empty($shortcode_tags)) {
            $all_shortcodes = array_keys($shortcode_tags);
            $yourstore_shortcodes = array_filter($all_shortcodes, function($tag) {
                return strpos($tag, 'hero_') === 0 || 
                       strpos($tag, 'product_') === 0 || 
                       strpos($tag, 'featured_') === 0 || 
                       strpos($tag, 'category_') === 0 || 
                       strpos($tag, 'newsletter_') === 0;
            });
            
            if (!empty($yourstore_shortcodes)) {
                echo '<div class="mt-4 p-2 bg-blue-50 rounded">';
                echo '<strong>All YourStore Shortcodes:</strong> ' . implode(', ', $yourstore_shortcodes);
                echo '</div>';
            }
        }
        echo '</div>';
        
        $section_class = $registered_shortcodes === count($expected_shortcodes) ? 'test-success' : 
                        ($registered_shortcodes > 0 ? 'test-warning' : 'test-error');
        echo '<script>document.currentScript.parentElement.className += " ' . $section_class . '";</script>';
        echo '</div>';
        
        // Test 6: WordPress Hooks
        echo '<div class="test-section">';
        echo '<h2 class="text-xl font-semibold mb-4">🪝 WordPress Hooks</h2>';
        
        $hooks_to_check = [
            'init',
            'wp_enqueue_scripts',
            'admin_enqueue_scripts'
        ];
        
        echo '<div class="space-y-2 text-sm">';
        foreach ($hooks_to_check as $hook) {
            $has_actions = has_action($hook);
            $icon = $has_actions ? '✅' : '❌';
            $color = $has_actions ? 'text-green-600' : 'text-red-600';
            
            echo "<div class='{$color}'>{$icon} <strong>{$hook}:</strong> " . ($has_actions ? 'Has actions' : 'No actions') . "</div>";
        }
        echo '</div>';
        echo '</div>';
        
        // Test 7: File System
        echo '<div class="test-section">';
        echo '<h2 class="text-xl font-semibold mb-4">📁 File System</h2>';
        
        $files_to_check = [
            'Plugin Main File' => WP_PLUGIN_DIR . '/yourstore-commerce/yourstore-commerce.php',
            'BaseComponent' => WP_PLUGIN_DIR . '/yourstore-commerce/includes/abstracts/class-base-component.php',
            'Hero Component' => WP_PLUGIN_DIR . '/yourstore-commerce/includes/components/class-hero-section-component.php',
            'Product Grid Component' => WP_PLUGIN_DIR . '/yourstore-commerce/includes/components/class-product-grid-component.php',
            'Featured Products Component' => WP_PLUGIN_DIR . '/yourstore-commerce/includes/components/class-featured-products-component.php',
            'BlocksManager' => WP_PLUGIN_DIR . '/yourstore-commerce/includes/blocks/class-blocks-manager.php',
            'App.js' => WP_PLUGIN_DIR . '/yourstore-commerce/assets/js/app.js',
            'Hero Section JS' => WP_PLUGIN_DIR . '/yourstore-commerce/assets/js/components/hero-section.js'
        ];
        
        $existing_files = 0;
        echo '<div class="space-y-2 text-sm">';
        foreach ($files_to_check as $name => $path) {
            $file_exists = file_exists($path);
            $icon = $file_exists ? '✅' : '❌';
            $color = $file_exists ? 'text-green-600' : 'text-red-600';
            $status = $file_exists ? 'Exists' : 'Missing';
            
            echo "<div class='{$color}'>{$icon} <strong>{$name}:</strong> {$status}</div>";
            if ($file_exists) $existing_files++;
        }
        echo '</div>';
        
        $section_class = $existing_files === count($files_to_check) ? 'test-success' : 
                        ($existing_files > 0 ? 'test-warning' : 'test-error');
        echo '<script>document.currentScript.parentElement.className += " ' . $section_class . '";</script>';
        echo '</div>';
        
        // Test 8: Error Log Check
        echo '<div class="test-section">';
        echo '<h2 class="text-xl font-semibold mb-4">📋 Recent Error Log</h2>';
        
        $error_log_path = WP_CONTENT_DIR . '/debug.log';
        if (file_exists($error_log_path)) {
            $log_content = file_get_contents($error_log_path);
            $recent_lines = array_slice(explode("\n", $log_content), -20);
            $yourstore_errors = array_filter($recent_lines, function($line) {
                return strpos($line, 'YourStore') !== false || strpos($line, 'yourstore') !== false;
            });
            
            if (!empty($yourstore_errors)) {
                echo '<div class="bg-red-50 p-4 rounded text-sm">';
                echo '<strong>Recent YourStore-related log entries:</strong>';
                echo '<pre class="mt-2 text-xs">' . implode("\n", array_slice($yourstore_errors, -10)) . '</pre>';
                echo '</div>';
            } else {
                echo '<div class="text-green-600">✅ No recent YourStore-related errors found</div>';
            }
        } else {
            echo '<div class="text-gray-600">ℹ️ Debug log file not found (this is normal if WP_DEBUG_LOG is disabled)</div>';
        }
        echo '</div>';
        
        // Summary and Recommendations
        echo '<div class="test-section test-info">';
        echo '<h2 class="text-xl font-semibold mb-4">💡 Summary & Recommendations</h2>';
        
        if (!$is_plugin_active) {
            echo '<div class="text-red-600 mb-4">❌ <strong>Critical Issue:</strong> YourStore Commerce plugin is not active. Please activate it in the WordPress admin.</div>';
        } elseif ($defined_constants < count($constants)) {
            echo '<div class="text-red-600 mb-4">❌ <strong>Critical Issue:</strong> Plugin constants not defined. Plugin may not be loading properly.</div>';
        } elseif ($loaded_classes < count($classes_to_test)) {
            echo '<div class="text-yellow-600 mb-4">⚠️ <strong>Warning:</strong> Some plugin classes are not loading. Check autoloader and file paths.</div>';
        } elseif ($registered_shortcodes < count($expected_shortcodes)) {
            echo '<div class="text-yellow-600 mb-4">⚠️ <strong>Warning:</strong> Some shortcodes are not registered. Check component initialization.</div>';
        } else {
            echo '<div class="text-green-600 mb-4">✅ <strong>Success:</strong> Plugin appears to be loading correctly!</div>';
        }
        
        echo '<div class="space-y-2 text-sm">';
        echo '<div><strong>Next Steps:</strong></div>';
        echo '<ul class="list-disc list-inside ml-4 space-y-1">';
        echo '<li>Test shortcodes on actual WordPress pages</li>';
        echo '<li>Check Vue.js component loading in browser</li>';
        echo '<li>Verify frontend styling and functionality</li>';
        echo '<li>Test block editor integration</li>';
        echo '</ul>';
        echo '</div>';
        echo '</div>';
        ?>
        
        <div class="mt-8 p-4 bg-blue-50 rounded">
            <h3 class="font-semibold mb-2">How to Use This Test:</h3>
            <ol class="list-decimal list-inside space-y-1 text-sm">
                <li>Ensure YourStore Commerce plugin is activated</li>
                <li>Check that all tests pass (green checkmarks)</li>
                <li>If tests fail, check the error log and file permissions</li>
                <li>Test shortcodes on actual WordPress pages</li>
                <li>Use browser developer tools to check Vue.js component loading</li>
            </ol>
        </div>
    </div>
</body>
</html>

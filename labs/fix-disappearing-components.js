/**
 * Fix for Disappearing Components Issue
 * 
 * This script provides enhanced error handling and debugging
 * for Vue component mounting issues.
 */

(function() {
    'use strict';
    
    console.log('YourStore: Loading component fix script...');
    
    // Enhanced error handling
    const originalConsoleError = console.error;
    console.error = function(...args) {
        originalConsoleError.apply(console, args);
        
        // Check if this is a Vue-related error
        const errorMessage = args.join(' ');
        if (errorMessage.includes('Vue') || errorMessage.includes('mount') || errorMessage.includes('component')) {
            console.log('YourStore: Vue-related error detected, attempting recovery...');
            setTimeout(attemptComponentRecovery, 1000);
        }
    };
    
    // Monitor for component disappearing
    function monitorComponents() {
        const componentSelectors = [
            '.yourstore-hero-section',
            '.yourstore-featured-products',
            '.yourstore-category-showcase',
            '.yourstore-newsletter-signup'
        ];
        
        componentSelectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach((element, index) => {
                // Store original content
                if (!element._originalContent) {
                    element._originalContent = element.innerHTML;
                }
                
                // Monitor for content changes
                const observer = new MutationObserver((mutations) => {
                    mutations.forEach((mutation) => {
                        if (mutation.type === 'childList') {
                            // Check if content was removed
                            if (element.innerHTML.trim() === '' && element._originalContent.trim() !== '') {
                                console.warn(`YourStore: ${selector} content disappeared, attempting recovery...`);
                                attemptElementRecovery(element, selector);
                            }
                        }
                    });
                });
                
                observer.observe(element, {
                    childList: true,
                    subtree: true
                });
                
                // Store observer for cleanup
                element._contentObserver = observer;
            });
        });
    }
    
    // Attempt to recover a specific element
    function attemptElementRecovery(element, selector) {
        console.log(`YourStore: Attempting recovery for ${selector}`);
        
        // Check if Vue app exists and is still mounted
        if (element.__vue_app) {
            try {
                console.log('YourStore: Vue app exists, checking if it\'s still active...');
                
                // Try to access the Vue app
                const app = element.__vue_app;
                if (app && app._instance) {
                    console.log('YourStore: Vue app appears active, but content is missing');
                    
                    // Try to trigger a re-render
                    if (app._instance.proxy && app._instance.proxy.$forceUpdate) {
                        app._instance.proxy.$forceUpdate();
                        console.log('YourStore: Forced Vue component update');
                    }
                } else {
                    console.log('YourStore: Vue app instance is invalid, remounting...');
                    remountComponent(element, selector);
                }
            } catch (error) {
                console.error('YourStore: Error checking Vue app:', error);
                remountComponent(element, selector);
            }
        } else {
            console.log('YourStore: No Vue app found, attempting mount...');
            remountComponent(element, selector);
        }
    }
    
    // Remount a component
    function remountComponent(element, selector) {
        console.log(`YourStore: Remounting component for ${selector}`);
        
        // Clean up existing Vue app
        if (element.__vue_app) {
            try {
                element.__vue_app.unmount();
            } catch (e) {
                console.warn('YourStore: Error unmounting existing app:', e);
            }
            element.__vue_app = null;
        }
        
        // Determine component type and mount
        if (selector.includes('hero') && window.HeroSection) {
            mountHeroSection(element);
        } else if (selector.includes('featured-products') && window.FeaturedProducts) {
            mountFeaturedProducts(element);
        } else if (selector.includes('category-showcase') && window.CategoryShowcase) {
            mountCategoryShowcase(element);
        } else if (selector.includes('newsletter') && window.NewsletterSignup) {
            mountNewsletterSignup(element);
        } else {
            console.error(`YourStore: Cannot remount ${selector} - component not available`);
        }
    }
    
    // Mount individual components with error handling
    function mountHeroSection(element) {
        try {
            let props = {};
            
            if (element.dataset.props) {
                props = JSON.parse(element.dataset.props);
            } else {
                props = {
                    title: element.dataset.title || 'Welcome to YourStore',
                    subtitle: element.dataset.subtitle || 'Discover amazing products',
                    ctaText: element.dataset.ctaText || 'Shop Now',
                    ctaUrl: element.dataset.ctaUrl || '/shop'
                };
            }
            
            const app = Vue.createApp(window.HeroSection, props);
            app.config.errorHandler = (err, instance, info) => {
                console.error('YourStore: Hero Section error:', err, info);
            };
            
            element.__vue_app = app.mount(element);
            console.log('YourStore: Hero Section remounted successfully');
            
        } catch (error) {
            console.error('YourStore: Error remounting Hero Section:', error);
        }
    }
    
    function mountFeaturedProducts(element) {
        try {
            let props = {};
            
            if (element.dataset.props) {
                props = JSON.parse(element.dataset.props);
            } else {
                props = {
                    title: element.dataset.title || 'Featured Products',
                    limit: parseInt(element.dataset.limit) || 8,
                    columns: parseInt(element.dataset.columns) || 4
                };
            }
            
            const app = Vue.createApp(window.FeaturedProducts, props);
            app.config.errorHandler = (err, instance, info) => {
                console.error('YourStore: Featured Products error:', err, info);
            };
            
            element.__vue_app = app.mount(element);
            console.log('YourStore: Featured Products remounted successfully');
            
        } catch (error) {
            console.error('YourStore: Error remounting Featured Products:', error);
        }
    }
    
    function mountCategoryShowcase(element) {
        try {
            let props = {};
            
            if (element.dataset.props) {
                props = JSON.parse(element.dataset.props);
            } else {
                props = {
                    title: element.dataset.title || 'Shop by Category',
                    columns: parseInt(element.dataset.columns) || 4
                };
            }
            
            const app = Vue.createApp(window.CategoryShowcase, props);
            app.config.errorHandler = (err, instance, info) => {
                console.error('YourStore: Category Showcase error:', err, info);
            };
            
            element.__vue_app = app.mount(element);
            console.log('YourStore: Category Showcase remounted successfully');
            
        } catch (error) {
            console.error('YourStore: Error remounting Category Showcase:', error);
        }
    }
    
    function mountNewsletterSignup(element) {
        try {
            let props = {};
            
            if (element.dataset.props) {
                props = JSON.parse(element.dataset.props);
            } else {
                props = {
                    title: element.dataset.title || 'Stay Updated',
                    subtitle: element.dataset.subtitle || 'Get the latest news'
                };
            }
            
            const app = Vue.createApp(window.NewsletterSignup, props);
            app.config.errorHandler = (err, instance, info) => {
                console.error('YourStore: Newsletter Signup error:', err, info);
            };
            
            element.__vue_app = app.mount(element);
            console.log('YourStore: Newsletter Signup remounted successfully');
            
        } catch (error) {
            console.error('YourStore: Error remounting Newsletter Signup:', error);
        }
    }
    
    // Attempt general component recovery
    function attemptComponentRecovery() {
        console.log('YourStore: Attempting general component recovery...');
        
        const componentSelectors = [
            '.yourstore-hero-section',
            '.yourstore-featured-products',
            '.yourstore-category-showcase',
            '.yourstore-newsletter-signup'
        ];
        
        componentSelectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(element => {
                if (!element.__vue_app || element.innerHTML.trim() === '') {
                    console.log(`YourStore: Recovering ${selector}...`);
                    attemptElementRecovery(element, selector);
                }
            });
        });
    }
    
    // Initialize monitoring when DOM is ready
    function initializeMonitoring() {
        console.log('YourStore: Initializing component monitoring...');
        
        // Wait for components to be available
        const checkComponents = () => {
            const requiredComponents = ['HeroSection', 'FeaturedProducts', 'CategoryShowcase', 'NewsletterSignup'];
            const availableComponents = requiredComponents.filter(comp => window[comp]);
            
            if (availableComponents.length === requiredComponents.length) {
                console.log('YourStore: All components available, starting monitoring...');
                monitorComponents();
                
                // Perform initial recovery check
                setTimeout(attemptComponentRecovery, 2000);
                
                // Set up periodic recovery checks
                setInterval(attemptComponentRecovery, 10000);
                
            } else {
                console.log(`YourStore: Waiting for components... (${availableComponents.length}/${requiredComponents.length})`);
                setTimeout(checkComponents, 500);
            }
        };
        
        checkComponents();
    }
    
    // Start when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeMonitoring);
    } else {
        initializeMonitoring();
    }
    
    // Expose recovery function globally for manual use
    window.YourStoreRecovery = {
        attemptRecovery: attemptComponentRecovery,
        remountAll: function() {
            const selectors = ['.yourstore-hero-section', '.yourstore-featured-products', '.yourstore-category-showcase', '.yourstore-newsletter-signup'];
            selectors.forEach(selector => {
                document.querySelectorAll(selector).forEach(element => {
                    remountComponent(element, selector);
                });
            });
        }
    };
    
    console.log('YourStore: Component fix script loaded successfully');
    
})();

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Component Disappearing Issue</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .debug-section {
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        .error { border-color: #ef4444; background-color: #fef2f2; }
        .warning { border-color: #f59e0b; background-color: #fffbeb; }
        .success { border-color: #10b981; background-color: #f0fdf4; }
        .console-output {
            background: #1a1a1a;
            color: #00ff00;
            font-family: monospace;
            padding: 1rem;
            border-radius: 4px;
            max-height: 400px;
            overflow-y: auto;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body class="bg-gray-50 p-4">
    <div class="max-w-6xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-900 mb-6">Debug Component Disappearing Issue</h1>
        
        <!-- Error Monitoring -->
        <div id="error-section" class="debug-section error">
            <h2 class="text-xl font-semibold mb-4">🚨 JavaScript Errors</h2>
            <div id="error-log" class="space-y-2"></div>
        </div>
        
        <!-- Console Output -->
        <div class="debug-section">
            <h2 class="text-xl font-semibold mb-4">📋 Console Output</h2>
            <div id="console-output" class="console-output"></div>
        </div>
        
        <!-- Component Status -->
        <div id="component-section" class="debug-section">
            <h2 class="text-xl font-semibold mb-4">🧩 Component Status</h2>
            <div id="component-status" class="space-y-2"></div>
        </div>
        
        <!-- DOM Monitoring -->
        <div class="debug-section">
            <h2 class="text-xl font-semibold mb-4">👁️ DOM Changes</h2>
            <div id="dom-changes" class="space-y-2 max-h-60 overflow-y-auto"></div>
        </div>
        
        <!-- Network Monitoring -->
        <div class="debug-section">
            <h2 class="text-xl font-semibold mb-4">🌐 Network Activity</h2>
            <div id="network-activity" class="space-y-2 max-h-60 overflow-y-auto"></div>
        </div>
        
        <!-- Test Components -->
        <div class="debug-section">
            <h2 class="text-xl font-semibold mb-4">🧪 Test Components</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="border-2 border-dashed border-gray-300 p-4 rounded">
                    <h3 class="font-medium mb-2">Hero Section Test</h3>
                    <div id="hero-test" class="yourstore-hero-section min-h-[200px] bg-blue-100 flex items-center justify-center">
                        <div class="text-center">
                            <h1 class="text-2xl font-bold">Test Hero Section</h1>
                            <p class="text-gray-600">This should remain visible</p>
                        </div>
                    </div>
                </div>
                
                <div class="border-2 border-dashed border-gray-300 p-4 rounded">
                    <h3 class="font-medium mb-2">Featured Products Test</h3>
                    <div id="products-test" class="yourstore-featured-products min-h-[200px] bg-green-100 flex items-center justify-center">
                        <div class="text-center">
                            <h2 class="text-xl font-bold">Test Products</h2>
                            <p class="text-gray-600">This should remain visible</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Recommendations -->
        <div id="recommendations" class="debug-section">
            <h2 class="text-xl font-semibold mb-4">💡 Recommendations</h2>
            <div id="recommendation-content" class="space-y-2"></div>
        </div>
    </div>

    <script>
        // Debug utilities
        const errorLog = document.getElementById('error-log');
        const consoleOutput = document.getElementById('console-output');
        const componentStatus = document.getElementById('component-status');
        const domChanges = document.getElementById('dom-changes');
        const networkActivity = document.getElementById('network-activity');
        const recommendationContent = document.getElementById('recommendation-content');
        
        let errorCount = 0;
        let componentCheckInterval;
        let domObserver;
        
        // Capture console output
        const originalLog = console.log;
        const originalWarn = console.warn;
        const originalError = console.error;
        
        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? '#ff0000' : type === 'warn' ? '#ffff00' : '#00ff00';
            consoleOutput.innerHTML += `<span style="color: ${color}">[${timestamp}] ${message}</span>\n`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToConsole(args.join(' '), 'warn');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole(args.join(' '), 'error');
            logError(args.join(' '));
        };
        
        // Error logging
        function logError(message) {
            errorCount++;
            const errorDiv = document.createElement('div');
            errorDiv.className = 'p-2 bg-red-100 border border-red-300 rounded text-red-800 text-sm';
            errorDiv.innerHTML = `<strong>Error ${errorCount}:</strong> ${message}`;
            errorLog.appendChild(errorDiv);
        }
        
        // Global error handler
        window.addEventListener('error', (event) => {
            logError(`${event.message} at ${event.filename}:${event.lineno}`);
        });
        
        window.addEventListener('unhandledrejection', (event) => {
            logError(`Unhandled Promise Rejection: ${event.reason}`);
        });
        
        // Component monitoring
        function checkComponents() {
            const components = [
                'HeroSection',
                'FeaturedProducts',
                'ProductGrid',
                'CategoryShowcase',
                'NewsletterSignup',
                'SiteFooter'
            ];
            
            let statusHTML = '';
            let loadedCount = 0;
            
            components.forEach(comp => {
                const isLoaded = typeof window[comp] !== 'undefined';
                const icon = isLoaded ? '✅' : '❌';
                const color = isLoaded ? 'text-green-600' : 'text-red-600';
                
                statusHTML += `<div class="${color}">${icon} <strong>${comp}:</strong> ${isLoaded ? 'Available' : 'Missing'}</div>`;
                
                if (isLoaded) loadedCount++;
            });
            
            // Check Vue availability
            const vueAvailable = typeof Vue !== 'undefined';
            statusHTML += `<div class="${vueAvailable ? 'text-green-600' : 'text-red-600'}">${vueAvailable ? '✅' : '❌'} <strong>Vue.js:</strong> ${vueAvailable ? 'Available' : 'Missing'}</div>`;
            
            // Check component elements
            const heroElements = document.querySelectorAll('.yourstore-hero-section');
            const productElements = document.querySelectorAll('.yourstore-featured-products');
            const categoryElements = document.querySelectorAll('.yourstore-category-showcase');
            
            statusHTML += `<div class="mt-4 p-2 bg-blue-50 rounded">`;
            statusHTML += `<strong>DOM Elements Found:</strong><br>`;
            statusHTML += `Hero sections: ${heroElements.length}<br>`;
            statusHTML += `Product sections: ${productElements.length}<br>`;
            statusHTML += `Category sections: ${categoryElements.length}<br>`;
            statusHTML += `</div>`;
            
            // Check if elements have content
            statusHTML += `<div class="mt-2 p-2 bg-yellow-50 rounded">`;
            statusHTML += `<strong>Element Content Status:</strong><br>`;
            heroElements.forEach((el, i) => {
                const hasContent = el.innerHTML.trim().length > 0;
                const hasVueApp = el.__vue_app;
                statusHTML += `Hero ${i + 1}: Content=${hasContent ? 'Yes' : 'No'}, Vue App=${hasVueApp ? 'Yes' : 'No'}<br>`;
            });
            statusHTML += `</div>`;
            
            componentStatus.innerHTML = statusHTML;
            
            console.log(`Component check: ${loadedCount}/${components.length} loaded, Vue: ${vueAvailable}`);
        }
        
        // DOM change monitoring
        function setupDOMObserver() {
            domObserver = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    if (mutation.type === 'childList') {
                        mutation.removedNodes.forEach((node) => {
                            if (node.nodeType === Node.ELEMENT_NODE) {
                                const timestamp = new Date().toLocaleTimeString();
                                const nodeInfo = `${node.tagName || 'Unknown'}.${node.className || 'no-class'}`;
                                domChanges.innerHTML += `<div class="text-red-600">[${timestamp}] REMOVED: ${nodeInfo}</div>`;
                                console.warn(`DOM element removed: ${nodeInfo}`);
                            }
                        });
                        
                        mutation.addedNodes.forEach((node) => {
                            if (node.nodeType === Node.ELEMENT_NODE) {
                                const timestamp = new Date().toLocaleTimeString();
                                const nodeInfo = `${node.tagName || 'Unknown'}.${node.className || 'no-class'}`;
                                domChanges.innerHTML += `<div class="text-green-600">[${timestamp}] ADDED: ${nodeInfo}</div>`;
                            }
                        });
                    }
                    
                    if (mutation.type === 'attributes') {
                        const timestamp = new Date().toLocaleTimeString();
                        const nodeInfo = `${mutation.target.tagName}.${mutation.target.className || 'no-class'}`;
                        domChanges.innerHTML += `<div class="text-blue-600">[${timestamp}] ATTR CHANGED: ${nodeInfo} (${mutation.attributeName})</div>`;
                    }
                });
                
                domChanges.scrollTop = domChanges.scrollHeight;
            });
            
            domObserver.observe(document.body, {
                childList: true,
                subtree: true,
                attributes: true,
                attributeFilter: ['class', 'style', 'data-props']
            });
        }
        
        // Network monitoring
        const originalFetch = window.fetch;
        window.fetch = function(...args) {
            const timestamp = new Date().toLocaleTimeString();
            const url = args[0];
            networkActivity.innerHTML += `<div class="text-blue-600">[${timestamp}] FETCH: ${url}</div>`;
            
            return originalFetch.apply(this, args)
                .then(response => {
                    const status = response.ok ? 'SUCCESS' : 'ERROR';
                    const color = response.ok ? 'text-green-600' : 'text-red-600';
                    networkActivity.innerHTML += `<div class="${color}">[${timestamp}] ${status}: ${url} (${response.status})</div>`;
                    networkActivity.scrollTop = networkActivity.scrollHeight;
                    return response;
                })
                .catch(error => {
                    networkActivity.innerHTML += `<div class="text-red-600">[${timestamp}] FAILED: ${url} - ${error.message}</div>`;
                    networkActivity.scrollTop = networkActivity.scrollHeight;
                    throw error;
                });
        };
        
        // Generate recommendations
        function generateRecommendations() {
            const recommendations = [];
            
            if (errorCount > 0) {
                recommendations.push({
                    type: 'error',
                    title: 'JavaScript Errors Detected',
                    description: `${errorCount} error(s) found. Check the error log above.`,
                    action: 'Fix JavaScript errors to prevent component failures'
                });
            }
            
            const vueAvailable = typeof Vue !== 'undefined';
            if (!vueAvailable) {
                recommendations.push({
                    type: 'error',
                    title: 'Vue.js Not Available',
                    description: 'Vue.js is not loaded, components cannot initialize',
                    action: 'Check Vue.js CDN link and loading order'
                });
            }
            
            const heroElements = document.querySelectorAll('.yourstore-hero-section');
            if (heroElements.length === 0) {
                recommendations.push({
                    type: 'warning',
                    title: 'No Component Elements Found',
                    description: 'No YourStore component elements detected in DOM',
                    action: 'Check if shortcodes are executing properly'
                });
            }
            
            if (recommendations.length === 0) {
                recommendations.push({
                    type: 'success',
                    title: 'No Issues Detected',
                    description: 'All systems appear to be functioning normally',
                    action: 'Monitor the console for any runtime errors'
                });
            }
            
            // Render recommendations
            recommendations.forEach(rec => {
                const typeColors = {
                    success: 'text-green-600 bg-green-50 border-green-200',
                    error: 'text-red-600 bg-red-50 border-red-200',
                    warning: 'text-yellow-600 bg-yellow-50 border-yellow-200'
                };
                
                const icons = {
                    success: '✅',
                    error: '❌',
                    warning: '⚠️'
                };
                
                recommendationContent.innerHTML += `
                    <div class="p-4 rounded border ${typeColors[rec.type]}">
                        <div class="font-semibold">${icons[rec.type]} ${rec.title}</div>
                        <div class="mt-1">${rec.description}</div>
                        <div class="mt-2 text-sm font-medium">Action: ${rec.action}</div>
                    </div>
                `;
            });
        }
        
        // Initialize debugging
        function startDebugging() {
            console.log('=== Component Disappearing Debug Started ===');
            
            // Setup monitoring
            setupDOMObserver();
            
            // Check components periodically
            checkComponents();
            componentCheckInterval = setInterval(checkComponents, 2000);
            
            // Generate initial recommendations
            setTimeout(generateRecommendations, 3000);
            
            console.log('Debug monitoring active. Watch for component changes...');
        }
        
        // Cleanup
        window.addEventListener('beforeunload', () => {
            if (componentCheckInterval) clearInterval(componentCheckInterval);
            if (domObserver) domObserver.disconnect();
        });
        
        // Start when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', startDebugging);
        } else {
            startDebugging();
        }
    </script>
</body>
</html>

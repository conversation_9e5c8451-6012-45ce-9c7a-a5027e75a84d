<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YourStore Vue Components Debug</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .debug-section {
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        .debug-success { border-color: #10b981; background-color: #f0fdf4; }
        .debug-error { border-color: #ef4444; background-color: #fef2f2; }
        .debug-warning { border-color: #f59e0b; background-color: #fffbeb; }
        .component-test {
            min-height: 200px;
            border: 1px dashed #d1d5db;
            padding: 1rem;
            margin: 0.5rem 0;
            border-radius: 4px;
        }
    </style>
</head>
<body class="bg-gray-50 p-8">
    <div class="max-w-6xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-900 mb-8">YourStore Vue Components Debug</h1>
        
        <!-- Debug Information -->
        <div id="debug-info" class="debug-section">
            <h2 class="text-xl font-semibold mb-4">Debug Information</h2>
            <div id="debug-output" class="space-y-2 font-mono text-sm"></div>
        </div>
        
        <!-- Component Loading Status -->
        <div id="component-status" class="debug-section">
            <h2 class="text-xl font-semibold mb-4">Component Loading Status</h2>
            <div id="status-output" class="space-y-2"></div>
        </div>
        
        <!-- Test Components -->
        <div class="debug-section">
            <h2 class="text-xl font-semibold mb-4">Component Tests</h2>
            
            <!-- Hero Section Test -->
            <div class="component-test">
                <h3 class="text-lg font-medium mb-2">Hero Section Component</h3>
                <div class="yourstore-hero-section" 
                     data-props='{"title":"Debug Hero","subtitle":"Testing Vue component loading","cta_text":"Test CTA","cta_url":"#test"}'></div>
            </div>
            
            <!-- Featured Products Test -->
            <div class="component-test">
                <h3 class="text-lg font-medium mb-2">Featured Products Component</h3>
                <div class="yourstore-featured-products" 
                     data-props='{"title":"Debug Featured Products","limit":4,"columns":2}'></div>
            </div>
            
            <!-- Product Grid Test -->
            <div class="component-test">
                <h3 class="text-lg font-medium mb-2">Product Grid Component</h3>
                <div class="yourstore-product-grid" 
                     data-props='{"limit":6,"columns":3,"categories":["electronics"]}'></div>
            </div>
            
            <!-- Category Showcase Test -->
            <div class="component-test">
                <h3 class="text-lg font-medium mb-2">Category Showcase Component</h3>
                <div class="yourstore-category-showcase" 
                     data-props='{"title":"Debug Categories","columns":3}'></div>
            </div>
            
            <!-- Newsletter Signup Test -->
            <div class="component-test">
                <h3 class="text-lg font-medium mb-2">Newsletter Signup Component</h3>
                <div class="yourstore-newsletter-signup" 
                     data-props='{"title":"Debug Newsletter","subtitle":"Test subscription form"}'></div>
            </div>
        </div>
        
        <!-- Console Output -->
        <div id="console-output" class="debug-section">
            <h2 class="text-xl font-semibold mb-4">Console Output</h2>
            <div id="console-log" class="bg-black text-green-400 p-4 rounded font-mono text-sm max-h-96 overflow-y-auto"></div>
        </div>
    </div>

    <script>
        // Debug utilities
        const debugOutput = document.getElementById('debug-output');
        const statusOutput = document.getElementById('status-output');
        const consoleLog = document.getElementById('console-log');
        
        // Capture console output
        const originalLog = console.log;
        const originalWarn = console.warn;
        const originalError = console.error;
        
        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? 'text-red-400' : type === 'warn' ? 'text-yellow-400' : 'text-green-400';
            consoleLog.innerHTML += `<div class="${color}">[${timestamp}] ${message}</div>`;
            consoleLog.scrollTop = consoleLog.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToConsole(args.join(' '), 'warn');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };
        
        // Debug information
        function updateDebugInfo() {
            const info = [
                `Vue.js Available: ${typeof Vue !== 'undefined' ? '✅ Yes' : '❌ No'}`,
                `Vue Version: ${typeof Vue !== 'undefined' ? Vue.version || 'Unknown' : 'N/A'}`,
                `Document Ready State: ${document.readyState}`,
                `Window Location: ${window.location.href}`,
                `User Agent: ${navigator.userAgent.substring(0, 100)}...`
            ];
            
            debugOutput.innerHTML = info.map(item => `<div>${item}</div>`).join('');
        }
        
        // Component status check
        function checkComponentStatus() {
            const components = [
                'HeroSection',
                'FeaturedProducts',
                'ProductGrid',
                'CategoryShowcase',
                'NewsletterSignup',
                'SiteFooter'
            ];
            
            const status = components.map(comp => {
                const available = typeof window[comp] !== 'undefined';
                const icon = available ? '✅' : '❌';
                const type = available ? 'text-green-600' : 'text-red-600';
                return `<div class="${type}">${icon} ${comp}: ${available ? 'Available' : 'Missing'}</div>`;
            });
            
            statusOutput.innerHTML = status.join('');
            
            // Check for any component-related objects on window
            const windowKeys = Object.keys(window).filter(key => 
                key.includes('Section') || 
                key.includes('Products') || 
                key.includes('Grid') || 
                key.includes('Showcase') || 
                key.includes('Newsletter') || 
                key.includes('Footer')
            );
            
            if (windowKeys.length > 0) {
                statusOutput.innerHTML += `<div class="mt-4 text-blue-600"><strong>Found on window:</strong> ${windowKeys.join(', ')}</div>`;
            }
        }
        
        // Test component mounting
        function testComponentMounting() {
            console.log('=== Starting Component Mounting Test ===');
            
            // Check if Vue is available
            if (typeof Vue === 'undefined') {
                console.error('Vue.js is not available!');
                return;
            }
            
            console.log('Vue.js is available, version:', Vue.version || 'Unknown');
            
            // Try to mount a simple Vue app
            try {
                const { createApp, ref } = Vue;
                const testApp = createApp({
                    setup() {
                        const message = ref('Vue.js is working!');
                        return { message };
                    },
                    template: '<div class="p-4 bg-green-100 text-green-800 rounded">{{ message }}</div>'
                });
                
                // Create a test element
                const testElement = document.createElement('div');
                testElement.id = 'vue-test';
                document.body.appendChild(testElement);
                
                testApp.mount('#vue-test');
                console.log('✅ Vue.js basic mounting test successful');
                
            } catch (error) {
                console.error('❌ Vue.js mounting test failed:', error);
            }
        }
        
        // Initialize debug
        function initDebug() {
            console.log('=== YourStore Vue Components Debug Started ===');
            updateDebugInfo();
            checkComponentStatus();
            testComponentMounting();
            
            // Recheck every 2 seconds for the first 10 seconds
            let checks = 0;
            const interval = setInterval(() => {
                checks++;
                console.log(`=== Status Check #${checks} ===`);
                checkComponentStatus();
                
                if (checks >= 5) {
                    clearInterval(interval);
                    console.log('=== Debug monitoring complete ===');
                }
            }, 2000);
        }
        
        // Start when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initDebug);
        } else {
            initDebug();
        }
        
        // Also try to load components from the actual plugin paths
        console.log('=== Attempting to load YourStore components ===');
        
        // Try to load the actual component files
        const componentPaths = [
            '/wp-content/plugins/yourstore-commerce/assets/js/components/hero-section.js',
            '/wp-content/plugins/yourstore-commerce/assets/js/components/featured-products.js',
            '/wp-content/plugins/yourstore-commerce/assets/js/components/product-grid.js',
            '/wp-content/plugins/yourstore-commerce/assets/js/components/category-showcase.js',
            '/wp-content/plugins/yourstore-commerce/assets/js/components/newsletter-signup.js',
            '/wp-content/plugins/yourstore-commerce/assets/js/app.js'
        ];
        
        componentPaths.forEach(path => {
            const script = document.createElement('script');
            script.src = path;
            script.onload = () => console.log(`✅ Loaded: ${path}`);
            script.onerror = () => console.error(`❌ Failed to load: ${path}`);
            document.head.appendChild(script);
        });
    </script>
</body>
</html>

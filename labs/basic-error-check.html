<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Basic Error Check</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .error-box {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #f5c6cb;
        }
        .success-box {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #c3e6cb;
        }
        .info-box {
            background: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #bee5eb;
        }
        .code {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 14px;
            border: 1px solid #e9ecef;
            margin: 10px 0;
        }
        .test-section {
            border: 2px dashed #ccc;
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        #results {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Basic Error Check</h1>
        <p>This simple page checks for fundamental JavaScript issues without complex dependencies.</p>
        
        <div class="info-box">
            <strong>Status:</strong> <span id="status">Checking...</span>
        </div>
        
        <div class="test-section">
            <h3>Step 1: Basic JavaScript Test</h3>
            <button onclick="testBasicJS()">Test Basic JavaScript</button>
            <div id="basic-js-result"></div>
        </div>
        
        <div class="test-section">
            <h3>Step 2: Console Error Check</h3>
            <button onclick="checkConsoleErrors()">Check Console Errors</button>
            <div id="console-errors-result"></div>
        </div>
        
        <div class="test-section">
            <h3>Step 3: Network Resources Check</h3>
            <button onclick="checkNetworkResources()">Check Network Resources</button>
            <div id="network-result"></div>
        </div>
        
        <div class="test-section">
            <h3>Step 4: WordPress Plugin Assets</h3>
            <button onclick="checkPluginAssets()">Check Plugin Assets</button>
            <div id="plugin-assets-result"></div>
        </div>
        
        <div id="results"></div>
        
        <div class="info-box">
            <h3>Manual Checks:</h3>
            <p>1. Open browser Developer Tools (F12)</p>
            <p>2. Go to Console tab</p>
            <p>3. Look for red error messages</p>
            <p>4. Go to Network tab and reload page</p>
            <p>5. Look for failed requests (red status codes)</p>
        </div>
    </div>

    <script>
        // Immediately update status to show JS is working
        document.getElementById('status').textContent = 'JavaScript is working';
        document.getElementById('status').style.color = 'green';
        
        let errorLog = [];
        
        // Capture all errors
        window.addEventListener('error', function(e) {
            errorLog.push({
                type: 'JavaScript Error',
                message: e.message,
                source: e.filename,
                line: e.lineno,
                column: e.colno,
                stack: e.error ? e.error.stack : 'No stack trace'
            });
            console.error('Captured error:', e);
        });
        
        window.addEventListener('unhandledrejection', function(e) {
            errorLog.push({
                type: 'Unhandled Promise Rejection',
                message: e.reason,
                stack: e.reason && e.reason.stack ? e.reason.stack : 'No stack trace'
            });
            console.error('Captured promise rejection:', e);
        });
        
        function testBasicJS() {
            const resultDiv = document.getElementById('basic-js-result');
            
            try {
                // Test basic operations
                const testArray = [1, 2, 3];
                const testObject = { test: 'value' };
                const testFunction = () => 'Hello World';
                
                // Test DOM manipulation
                const testElement = document.createElement('div');
                testElement.textContent = 'Test Element';
                
                // Test JSON
                const testJSON = JSON.stringify({ test: 'data' });
                const parsedJSON = JSON.parse(testJSON);
                
                resultDiv.innerHTML = '<div class="success-box">✅ Basic JavaScript is working correctly</div>';
                
            } catch (error) {
                resultDiv.innerHTML = `<div class="error-box">❌ Basic JavaScript Error: ${error.message}</div>`;
                console.error('Basic JS test failed:', error);
            }
        }
        
        function checkConsoleErrors() {
            const resultDiv = document.getElementById('console-errors-result');
            
            if (errorLog.length === 0) {
                resultDiv.innerHTML = '<div class="success-box">✅ No JavaScript errors detected</div>';
            } else {
                let errorHTML = '<div class="error-box">❌ JavaScript Errors Found:<br>';
                errorLog.forEach((error, index) => {
                    errorHTML += `<strong>Error ${index + 1}:</strong> ${error.type}<br>`;
                    errorHTML += `Message: ${error.message}<br>`;
                    if (error.source) errorHTML += `Source: ${error.source}:${error.line}:${error.column}<br>`;
                    errorHTML += '<br>';
                });
                errorHTML += '</div>';
                resultDiv.innerHTML = errorHTML;
            }
        }
        
        function checkNetworkResources() {
            const resultDiv = document.getElementById('network-result');
            
            // Test loading common resources
            const resources = [
                '/wp-content/plugins/yourstore-commerce/assets/css/globals.css',
                '/wp-content/plugins/yourstore-commerce/assets/js/app.js',
                'https://unpkg.com/vue@3/dist/vue.global.js',
                'https://cdn.tailwindcss.com'
            ];
            
            let results = [];
            let completed = 0;
            
            resultDiv.innerHTML = '<div class="info-box">🔄 Testing network resources...</div>';
            
            resources.forEach((url, index) => {
                fetch(url, { method: 'HEAD' })
                    .then(response => {
                        results[index] = {
                            url: url,
                            status: response.status,
                            ok: response.ok
                        };
                    })
                    .catch(error => {
                        results[index] = {
                            url: url,
                            status: 'Failed',
                            ok: false,
                            error: error.message
                        };
                    })
                    .finally(() => {
                        completed++;
                        if (completed === resources.length) {
                            displayNetworkResults(results, resultDiv);
                        }
                    });
            });
        }
        
        function displayNetworkResults(results, resultDiv) {
            let html = '';
            let hasErrors = false;
            
            results.forEach(result => {
                if (result.ok) {
                    html += `<div class="success-box">✅ ${result.url} - Status: ${result.status}</div>`;
                } else {
                    html += `<div class="error-box">❌ ${result.url} - Status: ${result.status}${result.error ? ' - ' + result.error : ''}</div>`;
                    hasErrors = true;
                }
            });
            
            if (!hasErrors) {
                html = '<div class="success-box">✅ All network resources are accessible</div>' + html;
            }
            
            resultDiv.innerHTML = html;
        }
        
        function checkPluginAssets() {
            const resultDiv = document.getElementById('plugin-assets-result');
            
            // Check if Vue is available
            const vueAvailable = typeof Vue !== 'undefined';
            
            // Check if YourStore components are available
            const components = ['HeroSection', 'FeaturedProducts', 'CategoryShowcase', 'NewsletterSignup', 'SiteFooter'];
            const availableComponents = components.filter(comp => typeof window[comp] !== 'undefined');
            
            // Check if plugin elements exist in DOM
            const pluginElements = document.querySelectorAll('[class*="yourstore-"]');
            
            let html = '';
            
            if (vueAvailable) {
                html += '<div class="success-box">✅ Vue.js is available</div>';
            } else {
                html += '<div class="error-box">❌ Vue.js is not available</div>';
            }
            
            html += `<div class="${availableComponents.length > 0 ? 'success-box' : 'error-box'}">
                ${availableComponents.length > 0 ? '✅' : '❌'} YourStore Components: ${availableComponents.length}/${components.length} available
                <br>Available: ${availableComponents.join(', ') || 'None'}
            </div>`;
            
            html += `<div class="${pluginElements.length > 0 ? 'success-box' : 'info-box'}">
                ${pluginElements.length > 0 ? '✅' : 'ℹ️'} Plugin DOM Elements: ${pluginElements.length} found
            </div>`;
            
            // Check for app.js specific issues
            if (typeof window.YourStoreVue !== 'undefined') {
                html += '<div class="success-box">✅ YourStore app.js loaded successfully</div>';
            } else {
                html += '<div class="error-box">❌ YourStore app.js not loaded or failed to initialize</div>';
            }
            
            resultDiv.innerHTML = html;
        }
        
        // Auto-run basic checks after page load
        setTimeout(() => {
            testBasicJS();
            checkConsoleErrors();
        }, 1000);
        
        console.log('Basic Error Check page loaded successfully');
        console.log('If you see this message, basic JavaScript is working');
    </script>
</body>
</html>

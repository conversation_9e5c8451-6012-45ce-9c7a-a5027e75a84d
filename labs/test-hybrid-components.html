<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hybrid Component Testing - YourStore Commerce</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            DEFAULT: '#16a34a',
                            50: '#f0fdf4',
                            600: '#16a34a',
                            700: '#15803d'
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <div id="app">
        <div class="min-h-screen py-8">
            <div class="max-w-6xl mx-auto px-4">
                <!-- Header -->
                <div class="text-center mb-12">
                    <h1 class="text-4xl font-bold text-gray-900 mb-4">
                        <i class="fas fa-cogs text-primary mr-3"></i>
                        Hybrid Component Testing
                    </h1>
                    <p class="text-lg text-gray-600 max-w-3xl mx-auto">
                        Testing framework for YourStore Commerce Hybrid Components (Blocks + Shortcodes)
                    </p>
                </div>

                <!-- Test Status Dashboard -->
                <div class="bg-white rounded-lg shadow-lg mb-8 p-6">
                    <h2 class="text-2xl font-bold text-gray-900 mb-4">
                        <i class="fas fa-chart-line text-primary mr-2"></i>
                        Test Results Dashboard
                    </h2>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                        <div class="bg-green-50 p-4 rounded-lg border border-green-200">
                            <h3 class="font-semibold text-green-800">Components Mounted</h3>
                            <p class="text-2xl font-bold text-green-600" id="mounted-count">0</p>
                        </div>
                        <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                            <h3 class="font-semibold text-blue-800">Attribute Processing</h3>
                            <p class="text-2xl font-bold text-blue-600" id="attributes-ok">0</p>
                        </div>
                        <div class="bg-purple-50 p-4 rounded-lg border border-purple-200">
                            <h3 class="font-semibold text-purple-800">Performance Score</h3>
                            <p class="text-2xl font-bold text-purple-600" id="performance-score">0ms</p>
                        </div>
                        <div class="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                            <h3 class="font-semibold text-yellow-800">Parity Check</h3>
                            <p class="text-2xl font-bold text-yellow-600" id="parity-status">Pending</p>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <h4 class="font-semibold mb-2">Detailed Test Results:</h4>
                        <ul id="detailed-results" class="space-y-1 text-sm bg-gray-50 p-4 rounded max-h-40 overflow-y-auto"></ul>
                    </div>
                </div>

                <!-- Component Tests -->
                <div class="space-y-12">
                    
                    <!-- Hero Section Tests -->
                    <section class="test-section bg-white rounded-lg shadow-lg p-6">
                        <h2 class="text-2xl font-bold mb-6 text-gray-900">
                            <i class="fas fa-rocket text-primary mr-2"></i>
                            Hero Section Component
                        </h2>
                        
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                            
                            <!-- Block-generated component -->
                            <div class="border-2 border-blue-200 rounded-lg p-4 bg-blue-50">
                                <h3 class="font-semibold mb-2 text-blue-800">
                                    <i class="fas fa-cube mr-2"></i>
                                    Block-Generated Component
                                </h3>
                                <p class="text-sm text-blue-600 mb-4">Simulates Gutenberg block output</p>
                                <div class="yourstore-hero-section hero-section-wrapper" 
                                     data-props='{"title":"Block Hero","subtitle":"Generated via Gutenberg Block","cta_text":"Block CTA","cta_url":"/shop","background_image":"","overlay_opacity":0.5,"text_alignment":"center","height":"auto","show_scroll_indicator":true,"enable_parallax":true,"component_id":"hero-block-test"}'></div>
                            </div>
                            
                            <!-- Shortcode-generated component -->
                            <div class="border-2 border-green-200 rounded-lg p-4 bg-green-50">
                                <h3 class="font-semibold mb-2 text-green-800">
                                    <i class="fas fa-code mr-2"></i>
                                    Shortcode-Generated Component
                                </h3>
                                <p class="text-sm text-green-600 mb-4">Simulates shortcode output</p>
                                <div class="yourstore-hero-section hero-section-wrapper" 
                                     data-props='{"title":"Shortcode Hero","subtitle":"Generated via Shortcode [hero_section]","cta_text":"Shortcode CTA","cta_url":"/shop","background_image":"","overlay_opacity":0.5,"text_alignment":"center","height":"auto","show_scroll_indicator":true,"enable_parallax":true,"component_id":"hero-shortcode-test"}'></div>
                            </div>
                            
                        </div>
                        
                        <!-- Usage Examples -->
                        <div class="mt-8 p-4 bg-gray-100 rounded-lg">
                            <h4 class="font-semibold mb-3">
                                <i class="fas fa-book mr-2"></i>
                                Usage Examples
                            </h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                <div>
                                    <h5 class="font-medium text-blue-800 mb-2">Block Editor (Visual)</h5>
                                    <code class="block bg-blue-100 p-2 rounded text-xs">
                                        // Use Gutenberg block interface<br>
                                        // Title: "Welcome"<br>
                                        // Subtitle: "Discover amazing products"
                                    </code>
                                </div>
                                <div>
                                    <h5 class="font-medium text-green-800 mb-2">Shortcode Usage</h5>
                                    <code class="block bg-green-100 p-2 rounded text-xs">
                                        [hero_section title="Welcome" subtitle="Discover amazing products" cta_text="Shop Now"]
                                    </code>
                                </div>
                            </div>
                        </div>
                    </section>
                    
                    <!-- Product Grid Tests -->
                    <section class="test-section bg-white rounded-lg shadow-lg p-6">
                        <h2 class="text-2xl font-bold mb-6 text-gray-900">
                            <i class="fas fa-th-large text-primary mr-2"></i>
                            Product Grid Component
                        </h2>
                        
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                            
                            <!-- Block-generated component -->
                            <div class="border-2 border-blue-200 rounded-lg p-4 bg-blue-50">
                                <h3 class="font-semibold mb-2 text-blue-800">
                                    <i class="fas fa-cube mr-2"></i>
                                    Block-Generated Component
                                </h3>
                                <p class="text-sm text-blue-600 mb-4">Simulates Gutenberg block output</p>
                                <div class="yourstore-product-grid product-grid-wrapper" 
                                     data-props='{"limit":8,"columns":3,"categories":["electronics"],"show_filters":true,"show_pagination":true,"orderby":"popularity","layout":"grid","card_style":"modern","component_id":"product-grid-block-test"}'></div>
                            </div>
                            
                            <!-- Shortcode-generated component -->
                            <div class="border-2 border-green-200 rounded-lg p-4 bg-green-50">
                                <h3 class="font-semibold mb-2 text-green-800">
                                    <i class="fas fa-code mr-2"></i>
                                    Shortcode-Generated Component
                                </h3>
                                <p class="text-sm text-green-600 mb-4">Simulates shortcode output</p>
                                <div class="yourstore-product-grid product-grid-wrapper" 
                                     data-props='{"limit":8,"columns":3,"categories":["electronics"],"show_filters":true,"show_pagination":true,"orderby":"popularity","layout":"grid","card_style":"modern","component_id":"product-grid-shortcode-test"}'></div>
                            </div>
                            
                        </div>
                        
                        <!-- Usage Examples -->
                        <div class="mt-8 p-4 bg-gray-100 rounded-lg">
                            <h4 class="font-semibold mb-3">
                                <i class="fas fa-book mr-2"></i>
                                Usage Examples
                            </h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                <div>
                                    <h5 class="font-medium text-blue-800 mb-2">Block Editor (Visual)</h5>
                                    <code class="block bg-blue-100 p-2 rounded text-xs">
                                        // Use Gutenberg block interface<br>
                                        // Limit: 8 products<br>
                                        // Columns: 3<br>
                                        // Categories: Electronics
                                    </code>
                                </div>
                                <div>
                                    <h5 class="font-medium text-green-800 mb-2">Shortcode Usage</h5>
                                    <code class="block bg-green-100 p-2 rounded text-xs">
                                        [product_grid limit="8" columns="3" categories="electronics" orderby="popularity"]
                                    </code>
                                </div>
                            </div>
                        </div>
                    </section>
                    
                    <!-- Future Component Placeholders -->
                    <section class="bg-white rounded-lg shadow-lg p-6 border-2 border-dashed border-gray-300">
                        <h2 class="text-2xl font-bold mb-4 text-gray-500">
                            <i class="fas fa-plus-circle mr-2"></i>
                            Future Hybrid Components
                        </h2>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div class="p-4 bg-green-100 rounded-lg text-center border border-green-300">
                                <i class="fas fa-th-large text-3xl text-green-600 mb-2"></i>
                                <h3 class="font-semibold text-green-700">Product Grid</h3>
                                <p class="text-sm text-green-600">TASK-045 ✅ COMPLETE</p>
                            </div>
                            <div class="p-4 bg-gray-50 rounded-lg text-center">
                                <i class="fas fa-star text-3xl text-gray-400 mb-2"></i>
                                <h3 class="font-semibold text-gray-600">Featured Products</h3>
                                <p class="text-sm text-gray-500">TASK-046 (Next)</p>
                            </div>
                            <div class="p-4 bg-gray-50 rounded-lg text-center">
                                <i class="fas fa-envelope text-3xl text-gray-400 mb-2"></i>
                                <h3 class="font-semibold text-gray-600">Newsletter Signup</h3>
                                <p class="text-sm text-gray-500">TASK-048</p>
                            </div>
                        </div>
                    </section>
                    
                </div>
                
                <!-- Test Controls -->
                <div class="bg-white rounded-lg shadow-lg p-6 mt-8">
                    <h2 class="text-2xl font-bold mb-4 text-gray-900">
                        <i class="fas fa-play-circle text-primary mr-2"></i>
                        Test Controls
                    </h2>
                    <div class="flex flex-wrap gap-4">
                        <button @click="runAllTests" class="bg-primary text-white px-4 py-2 rounded hover:bg-primary-700 transition-colors">
                            <i class="fas fa-play mr-2"></i>
                            Run All Tests
                        </button>
                        <button @click="resetTests" class="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 transition-colors">
                            <i class="fas fa-redo mr-2"></i>
                            Reset Tests
                        </button>
                        <button @click="exportResults" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors">
                            <i class="fas fa-download mr-2"></i>
                            Export Results
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Vue.js Testing Logic -->
    <script>
        const { createApp } = Vue;
        
        createApp({
            data() {
                return {
                    testResults: [],
                    startTime: null,
                    testStatus: {
                        mounted: 0,
                        attributesOk: 0,
                        performance: 0,
                        parity: 'Pending'
                    }
                }
            },
            
            mounted() {
                this.runAllTests();
            },
            
            methods: {
                runAllTests() {
                    this.resetTests();
                    this.startTime = performance.now();
                    
                    // Run tests with delays to simulate real conditions
                    setTimeout(() => this.testComponentMounting(), 100);
                    setTimeout(() => this.testAttributeProcessing(), 200);
                    setTimeout(() => this.testFunctionalityParity(), 300);
                    setTimeout(() => this.testPerformance(), 400);
                    setTimeout(() => this.displayResults(), 500);
                },
                
                resetTests() {
                    this.testResults = [];
                    this.testStatus = {
                        mounted: 0,
                        attributesOk: 0,
                        performance: 0,
                        parity: 'Pending'
                    };
                    this.updateDashboard();
                },
                
                testComponentMounting() {
                    // Test Hero Section components
                    const heroComponents = document.querySelectorAll('.yourstore-hero-section');
                    const productGridComponents = document.querySelectorAll('.yourstore-product-grid');
                    const totalComponents = heroComponents.length + productGridComponents.length;
                    
                    this.testStatus.mounted = totalComponents;
                    
                    // Test Hero components
                    heroComponents.forEach((comp, index) => {
                        if (comp.dataset.props) {
                            this.testResults.push(`✓ Hero Component ${index + 1}: Props available`);
                            
                            // Verify CSS classes
                            if (comp.classList.contains('hero-section-wrapper')) {
                                this.testResults.push(`✓ Hero Component ${index + 1}: CSS classes correct`);
                            } else {
                                this.testResults.push(`⚠ Hero Component ${index + 1}: Missing wrapper class`);
                            }
                        } else {
                            this.testResults.push(`✗ Hero Component ${index + 1}: No props found`);
                        }
                    });
                    
                    // Test Product Grid components
                    productGridComponents.forEach((comp, index) => {
                        if (comp.dataset.props) {
                            this.testResults.push(`✓ Product Grid Component ${index + 1}: Props available`);
                            
                            // Verify CSS classes
                            if (comp.classList.contains('product-grid-wrapper')) {
                                this.testResults.push(`✓ Product Grid Component ${index + 1}: CSS classes correct`);
                            } else {
                                this.testResults.push(`⚠ Product Grid Component ${index + 1}: Missing wrapper class`);
                            }
                        } else {
                            this.testResults.push(`✗ Product Grid Component ${index + 1}: No props found`);
                        }
                    });
                    
                    this.testResults.push(`✓ Found ${heroComponents.length} hero components, ${productGridComponents.length} product grid components`);
                },
                
                testAttributeProcessing() {
                    let validAttributes = 0;
                    
                    // Test Hero Section components
                    const heroComponents = document.querySelectorAll('.yourstore-hero-section');
                    heroComponents.forEach((comp, index) => {
                        try {
                            const props = JSON.parse(comp.dataset.props || '{}');
                            
                            // Test required attributes for Hero Section
                            const requiredAttrs = ['title', 'subtitle', 'cta_text', 'cta_url'];
                            const hasRequired = requiredAttrs.every(attr => props.hasOwnProperty(attr));
                            
                            if (hasRequired) {
                                validAttributes++;
                                this.testResults.push(`✓ Hero Component ${index + 1}: All required attributes present`);
                                
                                // Test attribute types
                                if (typeof props.overlay_opacity === 'number' && props.overlay_opacity >= 0 && props.overlay_opacity <= 1) {
                                    this.testResults.push(`✓ Hero Component ${index + 1}: Overlay opacity validation passed`);
                                }
                                
                                if (['left', 'center', 'right'].includes(props.text_alignment)) {
                                    this.testResults.push(`✓ Hero Component ${index + 1}: Text alignment validation passed`);
                                }
                                
                                if (typeof props.show_scroll_indicator === 'boolean') {
                                    this.testResults.push(`✓ Hero Component ${index + 1}: Boolean attributes correct`);
                                }
                            } else {
                                this.testResults.push(`✗ Hero Component ${index + 1}: Missing required attributes`);
                            }
                        } catch (e) {
                            this.testResults.push(`✗ Hero Component ${index + 1}: Invalid JSON in props`);
                        }
                    });
                    
                    // Test Product Grid components
                    const productGridComponents = document.querySelectorAll('.yourstore-product-grid');
                    productGridComponents.forEach((comp, index) => {
                        try {
                            const props = JSON.parse(comp.dataset.props || '{}');
                            
                            // Test required attributes for Product Grid
                            const requiredAttrs = ['limit', 'columns', 'orderby', 'layout'];
                            const hasRequired = requiredAttrs.every(attr => props.hasOwnProperty(attr));
                            
                            if (hasRequired) {
                                validAttributes++;
                                this.testResults.push(`✓ Product Grid Component ${index + 1}: All required attributes present`);
                                
                                // Test attribute types and values
                                if (typeof props.limit === 'number' && props.limit >= 1 && props.limit <= 100) {
                                    this.testResults.push(`✓ Product Grid Component ${index + 1}: Limit validation passed`);
                                }
                                
                                if (typeof props.columns === 'number' && props.columns >= 1 && props.columns <= 6) {
                                    this.testResults.push(`✓ Product Grid Component ${index + 1}: Columns validation passed`);
                                }
                                
                                if (['grid', 'list', 'masonry'].includes(props.layout)) {
                                    this.testResults.push(`✓ Product Grid Component ${index + 1}: Layout validation passed`);
                                }
                                
                                if (typeof props.show_filters === 'boolean') {
                                    this.testResults.push(`✓ Product Grid Component ${index + 1}: Boolean attributes correct`);
                                }
                            } else {
                                this.testResults.push(`✗ Product Grid Component ${index + 1}: Missing required attributes`);
                            }
                        } catch (e) {
                            this.testResults.push(`✗ Product Grid Component ${index + 1}: Invalid JSON in props`);
                        }
                    });
                    
                    this.testStatus.attributesOk = validAttributes;
                    this.testResults.push(`✓ ${validAttributes} total components with valid attributes`);
                },
                
                testFunctionalityParity() {
                    const sections = document.querySelectorAll('.test-section');
                    let parityPassed = 0;
                    let totalChecks = 0;
                    
                    sections.forEach(section => {
                        // Test Hero Section parity
                        const heroBlockComponent = section.querySelector('.border-blue-200 .yourstore-hero-section');
                        const heroShortcodeComponent = section.querySelector('.border-green-200 .yourstore-hero-section');
                        
                        if (heroBlockComponent && heroShortcodeComponent) {
                            totalChecks++;
                            
                            try {
                                const blockProps = JSON.parse(heroBlockComponent.dataset.props || '{}');
                                const shortcodeProps = JSON.parse(heroShortcodeComponent.dataset.props || '{}');
                                
                                // Remove unique identifiers for comparison
                                delete blockProps.component_id;
                                delete shortcodeProps.component_id;
                                delete blockProps.title; // Allow different titles for distinction
                                delete shortcodeProps.title;
                                delete blockProps.subtitle; // Allow different subtitles for distinction  
                                delete shortcodeProps.subtitle;
                                
                                const blockStr = JSON.stringify(blockProps);
                                const shortcodeStr = JSON.stringify(shortcodeProps);
                                
                                if (blockStr === shortcodeStr) {
                                    parityPassed++;
                                    this.testResults.push(`✓ Hero Section: Block/Shortcode functional parity maintained`);
                                } else {
                                    this.testResults.push(`⚠ Hero Section: Minor differences detected (expected for demo)`);
                                    this.testResults.push(`  Block props: ${Object.keys(blockProps).length} attributes`);
                                    this.testResults.push(`  Shortcode props: ${Object.keys(shortcodeProps).length} attributes`);
                                }
                            } catch (e) {
                                this.testResults.push(`✗ Hero Section: Error comparing props - ${e.message}`);
                            }
                        }
                        
                        // Test Product Grid parity
                        const gridBlockComponent = section.querySelector('.border-blue-200 .yourstore-product-grid');
                        const gridShortcodeComponent = section.querySelector('.border-green-200 .yourstore-product-grid');
                        
                        if (gridBlockComponent && gridShortcodeComponent) {
                            totalChecks++;
                            
                            try {
                                const blockProps = JSON.parse(gridBlockComponent.dataset.props || '{}');
                                const shortcodeProps = JSON.parse(gridShortcodeComponent.dataset.props || '{}');
                                
                                // Remove unique identifiers for comparison
                                delete blockProps.component_id;
                                delete shortcodeProps.component_id;
                                
                                const blockStr = JSON.stringify(blockProps);
                                const shortcodeStr = JSON.stringify(shortcodeProps);
                                
                                if (blockStr === shortcodeStr) {
                                    parityPassed++;
                                    this.testResults.push(`✓ Product Grid: Block/Shortcode functional parity maintained`);
                                } else {
                                    this.testResults.push(`⚠ Product Grid: Minor differences detected`);
                                    this.testResults.push(`  Block props: ${Object.keys(blockProps).length} attributes`);
                                    this.testResults.push(`  Shortcode props: ${Object.keys(shortcodeProps).length} attributes`);
                                }
                            } catch (e) {
                                this.testResults.push(`✗ Product Grid: Error comparing props - ${e.message}`);
                            }
                        }
                    });
                    
                    this.testStatus.parity = totalChecks > 0 ? `${parityPassed}/${totalChecks}` : 'N/A';
                },
                
                testPerformance() {
                    const endTime = performance.now();
                    const totalTime = Math.round(endTime - this.startTime);
                    this.testStatus.performance = totalTime;
                    
                    if (totalTime < 100) {
                        this.testResults.push(`✓ Excellent performance: ${totalTime}ms`);
                    } else if (totalTime < 500) {
                        this.testResults.push(`⚠ Good performance: ${totalTime}ms`);
                    } else {
                        this.testResults.push(`✗ Performance needs optimization: ${totalTime}ms`);
                    }
                    
                    // Test memory usage (approximate)
                    if (window.performance && window.performance.memory) {
                        const memUsed = Math.round(window.performance.memory.usedJSHeapSize / 1024 / 1024);
                        this.testResults.push(`ℹ Memory usage: ${memUsed}MB`);
                    }
                },
                
                displayResults() {
                    this.updateDashboard();
                    
                    // Add summary
                    const totalTests = this.testResults.filter(r => r.startsWith('✓')).length;
                    const warnings = this.testResults.filter(r => r.startsWith('⚠')).length;
                    const errors = this.testResults.filter(r => r.startsWith('✗')).length;
                    
                    this.testResults.unshift(`📊 Test Summary: ${totalTests} passed, ${warnings} warnings, ${errors} errors`);
                },
                
                updateDashboard() {
                    document.getElementById('mounted-count').textContent = this.testStatus.mounted;
                    document.getElementById('attributes-ok').textContent = this.testStatus.attributesOk;
                    document.getElementById('performance-score').textContent = this.testStatus.performance + 'ms';
                    document.getElementById('parity-status').textContent = this.testStatus.parity;
                    
                    // Update detailed results
                    const container = document.getElementById('detailed-results');
                    container.innerHTML = this.testResults.map(result => `<li>${result}</li>`).join('');
                },
                
                exportResults() {
                    const results = {
                        timestamp: new Date().toISOString(),
                        status: this.testStatus,
                        details: this.testResults,
                        environment: {
                            userAgent: navigator.userAgent,
                            viewport: `${window.innerWidth}x${window.innerHeight}`,
                            vue_version: Vue.version || '3.x'
                        }
                    };
                    
                    const blob = new Blob([JSON.stringify(results, null, 2)], { type: 'application/json' });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `hybrid-component-test-results-${Date.now()}.json`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                    
                    this.testResults.push(`📁 Test results exported successfully`);
                    this.updateDashboard();
                }
            }
        }).mount('#app');
    </script>
</body>
</html>
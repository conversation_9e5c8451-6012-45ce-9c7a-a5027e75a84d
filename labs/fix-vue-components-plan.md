# YourStore Vue Components Fix Plan

## Current Issue
Vue components are not loading/rendering on WordPress pages despite proper plugin architecture and component files being in place.

## Diagnosis Steps (Run These First)

### 1. Test Plugin Initialization
```
Access: /wp-content/plugins/yourstore-commerce/labs/test-plugin-initialization.php
```
- Verify plugin is active and constants are defined
- Check class autoloading is working
- Confirm shortcodes are registered
- Review error logs for issues

### 2. Test Vue Component Loading
```
Access: /wp-content/plugins/yourstore-commerce/labs/diagnose-vue-loading.html
```
- Check Vue.js CDN loading
- Verify component scripts are loading
- Test component registration on window object
- Monitor browser console for errors

### 3. Test Shortcode Registration
```
Access: /wp-content/plugins/yourstore-commerce/labs/test-shortcode-registration.php
```
- Verify all expected shortcodes are registered
- Test shortcode execution
- Check WordPress hooks are firing

## Likely Issues & Fixes

### Issue 1: Script Loading Order ⚠️ HIGH PRIORITY
**Problem**: Vue components may be loading before Vue.js is available
**Fix**: Update script dependencies in `yourstore-commerce.php`

```php
// In enqueue_frontend_scripts() method
wp_enqueue_script(
    'yourstore-commerce-app',
    YOURSTORE_COMMERCE_PLUGIN_URL . 'assets/js/app.js',
    [
        'vue', 
        'yourstore-commerce-hero-section', 
        'yourstore-commerce-featured-products',
        'yourstore-commerce-product-grid',
        'yourstore-commerce-category-showcase',
        'yourstore-commerce-newsletter-signup',
        'yourstore-commerce-site-footer'
    ],
    YOURSTORE_COMMERCE_VERSION . '.' . time(),
    true
);
```

### Issue 2: Component Registration Timing ⚠️ HIGH PRIORITY
**Problem**: Components may not be registering on window object properly
**Fix**: Check component files end with proper window assignment

Each component file should end with:
```javascript
// At end of hero-section.js
window.HeroSection = HeroSection;
console.log('YourStore: HeroSection component registered on window');
```

### Issue 3: WordPress Integration ⚠️ MEDIUM PRIORITY
**Problem**: Components may not be initializing in WordPress context
**Fix**: Ensure plugin hooks are firing properly

Check in `yourstore-commerce.php`:
```php
public function __construct() {
    $this->init_hooks();
}

private function init_hooks(): void {
    // Ensure these hooks are firing
    add_action('wp_enqueue_scripts', [$this, 'enqueue_frontend_scripts']);
    add_action('init', [$this, 'init_components']);
}
```

### Issue 4: Theme Compatibility 🔍 LOW PRIORITY
**Problem**: Theme may not be loading plugin assets properly
**Fix**: Verify theme is calling wp_head() and wp_footer()

Check in `yourstore-theme/templates/front-page.html`:
- Ensure proper WordPress template structure
- Verify shortcodes are using correct format (`hero_section` not `hero-section`)

## Step-by-Step Fix Process

### Step 1: Run Diagnostics
1. Access each diagnostic tool listed above
2. Document any errors or missing components
3. Check browser console for JavaScript errors
4. Review WordPress error logs

### Step 2: Fix Script Loading (if needed)
1. Update script dependencies in `yourstore-commerce.php`
2. Ensure Vue.js loads before component scripts
3. Verify app.js loads after all components

### Step 3: Fix Component Registration (if needed)
1. Check each component file ends with `window.ComponentName = ComponentName`
2. Verify console.log statements show components registering
3. Test component availability in browser console

### Step 4: Test Frontend Loading
1. Visit WordPress homepage
2. Open browser developer tools
3. Check Console tab for component loading messages
4. Verify Vue components mount successfully

### Step 5: Verify Shortcode Execution
1. Test shortcodes in WordPress admin
2. Check shortcode output contains proper data-props
3. Verify Vue app.js finds and mounts components

## Expected Results After Fix

### Browser Console Should Show:
```
YourStore: Vue.js detected, initializing component...
YourStore: HeroSection component registered on window
YourStore: FeaturedProducts component registered on window
YourStore: ProductGrid component registered on window
YourStore: All components loaded, proceeding to mount
YourStore: Hero Section mounted successfully
YourStore Commerce: All Vue components mounted successfully
```

### Page Should Display:
- Hero section with background image and CTA
- Featured products grid with product cards
- Category showcase with category tiles
- Newsletter signup form
- Proper styling and animations

## Troubleshooting Common Issues

### Components Not Registering on Window
- Check component files for syntax errors
- Verify proper IIFE structure in component files
- Ensure window assignment at end of each file

### Vue.js Not Loading
- Check CDN availability: https://unpkg.com/vue@3/dist/vue.global.js
- Verify script loading order in browser Network tab
- Test Vue availability in browser console: `typeof Vue`

### Shortcodes Not Working
- Verify plugin is active in WordPress admin
- Check shortcode names use underscores: `hero_section`
- Test shortcode registration with diagnostic tools

### Styling Issues
- Verify Tailwind CSS is loading: https://cdn.tailwindcss.com
- Check custom CSS file is enqueued
- Test Tailwind classes in browser inspector

## Success Criteria

✅ All diagnostic tools show green status
✅ Browser console shows component registration messages  
✅ Vue components render with proper styling
✅ Interactive features work (buttons, forms, etc.)
✅ No JavaScript errors in browser console
✅ Shortcodes work in WordPress admin
✅ Block editor integration functional

## Files to Monitor

- `wp-content/plugins/yourstore-commerce/yourstore-commerce.php`
- `wp-content/plugins/yourstore-commerce/assets/js/app.js`
- `wp-content/plugins/yourstore-commerce/assets/js/components/*.js`
- `wp-content/themes/yourstore-theme/templates/front-page.html`
- WordPress error logs
- Browser console output

## Contact Points

If issues persist after following this plan:
1. Check WordPress error logs for PHP errors
2. Use browser developer tools to identify JavaScript errors
3. Test with default WordPress theme to isolate theme issues
4. Verify plugin file permissions and accessibility
5. Test on different browsers to rule out browser-specific issues

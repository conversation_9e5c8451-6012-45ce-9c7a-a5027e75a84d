<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Component Stability Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status-bar {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
            font-family: monospace;
            font-size: 14px;
        }
        .status-item {
            margin: 5px 0;
            padding: 5px 10px;
            border-radius: 3px;
        }
        .status-success { background: #d4edda; color: #155724; }
        .status-warning { background: #fff3cd; color: #856404; }
        .status-error { background: #f8d7da; color: #721c24; }
        .test-section {
            border: 2px dashed #ccc;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            min-height: 100px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .controls {
            margin: 20px 0;
            text-align: center;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 0 10px;
            font-size: 14px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn-danger {
            background: #dc3545;
        }
        .btn-danger:hover {
            background: #c82333;
        }
        .log-output {
            background: #1a1a1a;
            color: #00ff00;
            font-family: monospace;
            padding: 15px;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
            font-size: 12px;
            white-space: pre-wrap;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Component Stability Test</h1>
        <p>This page helps diagnose why Vue components disappear after loading.</p>
        
        <!-- Status Bar -->
        <div id="status-bar" class="status-bar">
            <div id="vue-status" class="status-item">Vue.js: Checking...</div>
            <div id="components-status" class="status-item">Components: Checking...</div>
            <div id="elements-status" class="status-item">DOM Elements: Checking...</div>
            <div id="errors-status" class="status-item">Errors: None detected</div>
        </div>
        
        <!-- Controls -->
        <div class="controls">
            <button class="btn" onclick="checkStatus()">🔍 Check Status</button>
            <button class="btn" onclick="simulateLoad()">🔄 Simulate Load</button>
            <button class="btn" onclick="clearLog()">🗑️ Clear Log</button>
            <button class="btn btn-danger" onclick="simulateError()">💥 Simulate Error</button>
        </div>
        
        <!-- Test Sections -->
        <div class="test-section" id="hero-test">
            <h3>🦸 Hero Section Test</h3>
            <div class="yourstore-hero-section" data-props='{"title":"Test Hero","subtitle":"This should stay visible","cta_text":"Test Button","cta_url":"#test"}'>
                <div style="padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; text-align: center; border-radius: 8px;">
                    <h2>Test Hero Section</h2>
                    <p>If this disappears, there's a mounting issue</p>
                    <button style="background: white; color: #333; border: none; padding: 10px 20px; border-radius: 4px;">Test Button</button>
                </div>
            </div>
        </div>
        
        <div class="test-section" id="products-test">
            <h3>🛍️ Featured Products Test</h3>
            <div class="yourstore-featured-products" data-props='{"title":"Test Products","limit":4,"columns":2}'>
                <div style="padding: 20px; background: #f8f9fa; border-radius: 8px;">
                    <h2>Test Featured Products</h2>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-top: 15px;">
                        <div style="background: white; padding: 15px; border-radius: 4px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                            <div style="background: #ddd; height: 100px; border-radius: 4px; margin-bottom: 10px;"></div>
                            <h4 style="margin: 0 0 5px 0;">Test Product 1</h4>
                            <p style="margin: 0; color: #666;">$29.99</p>
                        </div>
                        <div style="background: white; padding: 15px; border-radius: 4px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                            <div style="background: #ddd; height: 100px; border-radius: 4px; margin-bottom: 10px;"></div>
                            <h4 style="margin: 0 0 5px 0;">Test Product 2</h4>
                            <p style="margin: 0; color: #666;">$39.99</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="test-section" id="categories-test">
            <h3>📂 Category Showcase Test</h3>
            <div class="yourstore-category-showcase" data-props='{"title":"Test Categories","columns":3}'>
                <div style="padding: 20px; background: #f8f9fa; border-radius: 8px;">
                    <h2>Test Categories</h2>
                    <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px; margin-top: 15px;">
                        <div style="background: white; padding: 15px; border-radius: 4px; text-align: center;">
                            <div style="background: #007bff; height: 60px; border-radius: 4px; margin-bottom: 10px;"></div>
                            <h4 style="margin: 0;">Electronics</h4>
                        </div>
                        <div style="background: white; padding: 15px; border-radius: 4px; text-align: center;">
                            <div style="background: #28a745; height: 60px; border-radius: 4px; margin-bottom: 10px;"></div>
                            <h4 style="margin: 0;">Fashion</h4>
                        </div>
                        <div style="background: white; padding: 15px; border-radius: 4px; text-align: center;">
                            <div style="background: #ffc107; height: 60px; border-radius: 4px; margin-bottom: 10px;"></div>
                            <h4 style="margin: 0;">Home</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Log Output -->
        <div id="log-output" class="log-output"></div>
    </div>

    <script>
        // Test utilities
        let logOutput = document.getElementById('log-output');
        let errorCount = 0;
        let checkInterval;
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const colors = {
                info: '#00ff00',
                warn: '#ffff00',
                error: '#ff0000',
                success: '#00ffff'
            };
            
            logOutput.innerHTML += `<span style="color: ${colors[type]}">[${timestamp}] ${message}</span>\n`;
            logOutput.scrollTop = logOutput.scrollHeight;
            
            console.log(`[ComponentTest] ${message}`);
        }
        
        function updateStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `status-item status-${type === 'error' ? 'error' : type === 'warn' ? 'warning' : 'success'}`;
            element.textContent = message;
        }
        
        function checkStatus() {
            log('=== Status Check Started ===', 'info');
            
            // Check Vue.js
            const vueAvailable = typeof Vue !== 'undefined';
            updateStatus('vue-status', `Vue.js: ${vueAvailable ? 'Available' : 'Missing'}`, vueAvailable ? 'success' : 'error');
            log(`Vue.js: ${vueAvailable ? 'Available' : 'Missing'}`, vueAvailable ? 'success' : 'error');
            
            // Check components
            const components = ['HeroSection', 'FeaturedProducts', 'CategoryShowcase', 'NewsletterSignup'];
            const availableComponents = components.filter(comp => window[comp]);
            updateStatus('components-status', `Components: ${availableComponents.length}/${components.length} loaded`, availableComponents.length === components.length ? 'success' : 'warn');
            log(`Components: ${availableComponents.join(', ')} available`, availableComponents.length > 0 ? 'success' : 'error');
            
            // Check DOM elements
            const selectors = ['.yourstore-hero-section', '.yourstore-featured-products', '.yourstore-category-showcase'];
            let totalElements = 0;
            let mountedElements = 0;
            
            selectors.forEach(selector => {
                const elements = document.querySelectorAll(selector);
                totalElements += elements.length;
                
                elements.forEach((element, index) => {
                    const hasMounted = element.__vue_app;
                    const hasContent = element.innerHTML.trim().length > 0;
                    
                    if (hasMounted) mountedElements++;
                    
                    log(`${selector} ${index + 1}: Mounted=${hasMounted ? 'Yes' : 'No'}, Content=${hasContent ? 'Yes' : 'No'}`, hasMounted ? 'success' : 'warn');
                });
            });
            
            updateStatus('elements-status', `DOM Elements: ${mountedElements}/${totalElements} mounted`, mountedElements === totalElements ? 'success' : 'warn');
            updateStatus('errors-status', `Errors: ${errorCount} detected`, errorCount === 0 ? 'success' : 'error');
            
            log('=== Status Check Complete ===', 'info');
        }
        
        function simulateLoad() {
            log('=== Simulating Component Load ===', 'info');
            
            // Simulate the loading process
            const elements = document.querySelectorAll('[class*="yourstore-"]');
            elements.forEach((element, index) => {
                setTimeout(() => {
                    log(`Processing element ${index + 1}: ${element.className}`, 'info');
                    
                    // Check if content disappears
                    const originalContent = element.innerHTML;
                    
                    setTimeout(() => {
                        if (element.innerHTML !== originalContent) {
                            log(`Element ${index + 1} content changed!`, 'warn');
                        } else {
                            log(`Element ${index + 1} content stable`, 'success');
                        }
                    }, 1000);
                    
                }, index * 500);
            });
        }
        
        function simulateError() {
            log('=== Simulating Error ===', 'error');
            errorCount++;
            updateStatus('errors-status', `Errors: ${errorCount} detected`, 'error');
            
            // Trigger a fake error
            try {
                throw new Error('Simulated component error');
            } catch (e) {
                log(`Error caught: ${e.message}`, 'error');
            }
        }
        
        function clearLog() {
            logOutput.innerHTML = '';
            log('Log cleared', 'info');
        }
        
        // Monitor for changes
        function startMonitoring() {
            log('Starting component monitoring...', 'info');
            
            const observer = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    if (mutation.type === 'childList') {
                        mutation.removedNodes.forEach((node) => {
                            if (node.nodeType === Node.ELEMENT_NODE && node.className && node.className.includes('yourstore-')) {
                                log(`ALERT: Component element removed: ${node.className}`, 'error');
                                errorCount++;
                                updateStatus('errors-status', `Errors: ${errorCount} detected`, 'error');
                            }
                        });
                    }
                });
            });
            
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
            
            // Periodic status checks
            checkInterval = setInterval(checkStatus, 5000);
        }
        
        // Error handling
        window.addEventListener('error', (event) => {
            errorCount++;
            log(`JavaScript Error: ${event.message} at ${event.filename}:${event.lineno}`, 'error');
            updateStatus('errors-status', `Errors: ${errorCount} detected`, 'error');
        });
        
        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            log('Component Stability Test initialized', 'success');
            startMonitoring();
            setTimeout(checkStatus, 1000);
        });
        
        // Cleanup
        window.addEventListener('beforeunload', () => {
            if (checkInterval) clearInterval(checkInterval);
        });
    </script>
</body>
</html>
